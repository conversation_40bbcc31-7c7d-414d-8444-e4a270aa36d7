psycopg2-binary==2.9.6
pandas==1.4.3
numpy==1.23.5
# Office365-REST-Python-Client==2.3.15 Version cu~
Office365-REST-Python-Client==2.5.3
openpyxl==3.0.10
apache-airflow-providers-oracle==3.9.2
UliPlot==0.2.2
xlsxwriter==3.0.3
apache-airflow-providers-amazon==6.1.0
apache-airflow-providers-postgres[amazon]==5.3.0
gspread==5.3.2
apache-airflow-providers-mysql==3.3.0
apache-airflow-providers-microsoft-mssql==3.3.2
apache-airflow-providers-http==4.3.0
PyExcelerate==0.10.0
polars==0.19.19
pydantic==2.4.2
pyarrow==13.0.0
apache-airflow-providers-mongo==3.5.0
boto3==1.33.7
connectorx==0.3.2
SQLAlchemy==1.4.50
wrapt_timeout_decorator==1.4.1
oracledb==2.0.1
pyxlsb==1.0.10
joblib==1.2.0
#xgboost==1.7.5
xgboost==2.0.3
seaborn==0.12.2
statsmodels==0.14.4
scikit-learn
pycryptodome
chardet==5.2.0
paramiko