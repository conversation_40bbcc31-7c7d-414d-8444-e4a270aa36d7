### Python template
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.idea
.vscode
# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

### Python template
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

### Python template
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/
**/DWH2SharePoint/DAILY/**/test.py
DAGs/BOT_GAPO/Downloaded
**/DWH2SharePoint/DAILY/QLV/Downloaded
**/DWH2SharePoint/DAILY/1.XLN/Downloaded
**/DWH2SharePoint/DAILY/2.TLS/Downloaded
**/DWH2SharePoint/DAILY/3.VH/Downloaded
**/DWH2SharePoint/DAILY/4.PTKD/Downloaded
**/DWH2SharePoint/DAILY/5.PTSP/Downloaded
**/DWH2SharePoint/DAILY/6.UBKT/Downloaded
**/DWH2SharePoint/DAILY/7.OFFSHORE/Downloaded
**/DWH2SharePoint/DAILY/8.DTHT/Downloaded
**/DWH2SharePoint/DAILY/9.INS/Downloaded
**/DWH2SharePoint/DAILY/10.TNKH/Downloaded
**/DWH2SharePoint/DAILY/11.CLKD/Downloaded
**/DWH2SharePoint/DAILY/12.TCQT/Downloaded
**/DWH2SharePoint/DAILY/13.TT/Downloaded
**/DWH2SharePoint/DAILY/14.MKT/Downloaded
**/DWH2SharePoint/DAILY/15.VAS/Downloaded
**/DWH2SharePoint/DAILY/16.CNTT/Downloaded
**/DWH2SharePoint/DAILY/17.DVS/Downloaded
**/DWH2SharePoint/DAILY/CIMB/Downloaded
**/DWH2SharePoint/DAILY/QLV/Downloaded
**/DWH2SharePoint/REALTIME/XLN/Downloaded
**/DWH2SharePoint/REALTIME/TDTD/Downloaded
**/DWH2SharePoint/REALTIME/VH/Downloaded
**/DWH2SharePoint/WEEKLY/PTKD/Downloaded
**/DWH2SharePoint/WEEKLY/PTSP/Downloaded
**/DWH2SharePoint/WEEKLY/XLN/Downloaded
**/DWH2SharePoint/MONTHLY/DVS/Downloaded
**/DWH2SharePoint/MONTHLY/KT/Downloaded
**/DWH2SharePoint/MONTHLY/PTKD/Downloaded
**/DWH2SharePoint/MONTHLY/VH/Downloaded
**/DWH2SharePoint/MONTHLY/TTNV/Downloaded
**/External2DWH/SharePoint/VAS/Downloaded
**/External2DWH/SharePoint/VH/Downloaded
**/External2DWH/SharePoint/DTHT/Downloaded
**/External2DWH/SharePoint/PTKD/Downloaded
**/External2DWH/SharePoint/GAPO_ID/Downloaded
**/External2DWH/SharePoint/XLN/Downloaded
**/External2DWH/SharePoint/TCQT/Downloaded
**/External2DWH/SharePoint/VH/Downloaded
**/External2DWH/SharePoint/HR/Downloaded
**/External2DWH/SharePoint/HR/Downloaded
**/External2DWH/SharePoint/TLS/Downloaded
**/External2DWH/SharePoint/PT/Downloaded
**/External2DWH/SharePoint/QTRR/Downloaded
**/DWH2SharePoint/DAILY/17.DVS/Downloaded
**/DAGs/DWH2SharePoint/MONTHLY/DVS/Downloaded
**/DAGs/DWH2SharePoint/WEEKLY/PTSP/Downloaded
**/DAGs/DWH2SharePoint/DAILY/CIMB/Downloaded
**/DAGs/DWH2SharePoint/DAILY/18.TDTD/Downloaded
**/DAGs/External2DWH/SharePoint/TLS/Downloaded
/DAGs/**/Downloaded
**/DAILY_RUN_PROC_HANHDTM.py

**/DAGs/Score/src/Case/Bscore/Data
**/DAGs/Score/src/Case/CScore/Data
**/DAGs/Score_v3/src/Case/BScore/Data
**/DAGs/External2DWH/CNTT/SFTP/Data/*