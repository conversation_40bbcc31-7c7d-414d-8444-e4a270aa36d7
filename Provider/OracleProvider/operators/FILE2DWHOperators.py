import os
import pandas as pd
from airflow.providers.oracle.hooks.oracle import <PERSON><PERSON><PERSON>
from airflow.models import BaseOperator
from datetime import datetime, timedelta
from typing import Any, Dict, List, Tuple
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import TableMetaData
import re
import polars as pl
from polars.exceptions import ComputeError
import chardet
import csv
class XLSX2DWHOperator(BaseOperator):

    def __init__(
            self,
            table_metadata: TableMetaData,
            oracle_conn_id: str | None = None,
            local_file_path: str | List[str] | Tuple[str] | None = None,
            **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)
        self.table_metadata = table_metadata
        self.oracle_conn_id = oracle_conn_id
        self.oracle_hook = OracleHook(oracle_conn_id=oracle_conn_id) if oracle_conn_id else None
        self.local_file_path = local_file_path if not isinstance(local_file_path, str) else (local_file_path,)

    def detect_separator(self,file_path, skip_rows_list=None, encoding = 'utf-8'):
        """Tự động nhận diện phân tách trong file CSV, bỏ qua các dòng không cần thiết."""
        with open(file_path, 'r', encoding=encoding) as f:
            lines = f.readlines()  # Đọc toàn bộ nội dung file vào danh sách các dòng
            if skip_rows_list:
                # Loại bỏ các dòng cần bỏ qua theo skip_rows_list
                lines = [line for i, line in enumerate(lines) if i not in skip_rows_list]

            # Chỉ lấy một phần của dữ liệu còn lại (ví dụ 1024 ký tự đầu tiên) để nhận diện phân tách
            sample = ''.join(lines[:1024])  # Chỉ đọc 1024 ký tự đầu tiên của phần dữ liệu đã lọc

            # Sử dụng Sniffer để nhận diện phân tách
            dialect = csv.Sniffer().sniff(sample)
            return dialect.delimiter  # Trả về phân tách nhận diện được
    def _usecolumns(self,value_keeps_columns=None,list_index_keeps_columns=None):
        list_columns = []
        if value_keeps_columns:
            # Nếu value_keeps_columns là danh sách
            if isinstance(value_keeps_columns, list):
                list_columns = value_keeps_columns
            else:
                # Nếu value_keeps_columns là số,  range từ 0 đến value_keeps_columns
                for i in range(value_keeps_columns):
                    list_columns.append(i)
        elif list_index_keeps_columns is not None:
            list_columns = list_index_keeps_columns

        return list_columns

    def _insert_data_to_stg(self, file_path: str, process_date: datetime) -> None:
        """datatype_mapping: 'str', 'int', 'float', 'datetime64'
        """
        self.log.info(f"start insert data to stg {file_path}")
        self.log.info(f'Read And Ingest Sheet {self.table_metadata.xlsx_sheet_name}')
        list_columns = self._usecolumns(self.table_metadata.value_colums_keep,self.table_metadata.list_index_columns_keep)
        skip_rows = self.table_metadata.xlsx_read_options.skiprows if self.table_metadata.xlsx_read_options else None
        header = self.table_metadata.xlsx_read_options.header if self.table_metadata.xlsx_read_options else None

        if file_path.endswith('.csv') and re.findall('\*', os.path.basename(file_path)):
            if self.table_metadata.xlsx_read_options:
                if self.table_metadata.xlsx_read_options.skiprows:
                    skip_rows = self.table_metadata.xlsx_read_options.skiprows
            else:
                skip_rows = 0
            try:
                df = pl.read_csv(file_path, infer_schema_length=0, skip_rows=skip_rows, has_header=True, truncate_ragged_lines=True).to_pandas()
            except pl.exceptions.ComputeError as e:
                if 'no matching files found in' in str(e):
                    df = pd.DataFrame()
                else:
                    raise pl.exceptions.ComputeError(e)
        # kienpv: 2025-02-10
        elif file_path.endswith('.csv') and not re.findall('\*', os.path.basename(file_path)):
            with open(file_path, 'rb') as f:
                result = chardet.detect(f.read())  # Đọc file ở chế độ nhị phân và phân tích mã hóa
                print(f"Detected encoding: {result.get('encoding')}")

                # Đảm bảo kết quả không phải là None hoặc không phải mã hóa lạ
            if result['encoding'] is not None:
                encoding = result.get('encoding')
            else:
                encoding = 'utf-8'
            #encoding = result.get('encoding', 'utf-8')  # Nếu không có mã hóa xác định, mặc định là 'utf-8'
            print(f"encoding = "+ encoding)
            if header:
                # Nếu có header, tính skip_rows dựa trên header
                skip_rows = header - 1
                skip_rows_list = [i for i in range(0, skip_rows)]  # Tạo danh sách skip rows từ 1 đến header - 1
                print(f"Skipping rows based on header: {skip_rows_list}")
            elif skip_rows is not None:
                # Nếu không có header mà có skip_rows, kiểm tra xem skip_rows là int hay list
                if isinstance(skip_rows, int):
                    # Nếu skip_rows là số nguyên, chuyển thành danh sách
                    skip_rows_list = [i for i in range(0, skip_rows)]
                    print(f"Skipping rows based on int skip_rows: {skip_rows_list}")
                elif isinstance(skip_rows, list):
                    # Nếu skip_rows là list, sử dụng trực tiếp
                    skip_rows_list = skip_rows
                    print(f"Skipping rows based on list skip_rows: {skip_rows_list}")
                else:
                    raise ValueError(
                        f"Invalid skip_rows value: {skip_rows}. It should be either an int or a list of ints.")
            else:
                skip_rows_list = None  # Nếu không có skip_rows thì không bỏ qua dòng nào
                print("No rows to skip.")
            sep = self.detect_separator(file_path, skip_rows_list=skip_rows_list, encoding = encoding)
            print(f"Detected separator: '{sep}'")
            if skip_rows:  # Nếu skip_rows có giá trị, thêm vào đối số
                if list_columns:
                    df = pd.read_csv(file_path, usecols=list_columns, encoding=encoding, dtype=str, skiprows=skip_rows_list,sep=sep)
                else:
                    df = pd.read_csv(file_path, encoding=encoding, dtype=str, skiprows=skip_rows_list,sep=sep)
            else:  # Nếu không có skiprows, chỉ cần đọc bình thường
                if list_columns:
                    df = pd.read_csv(file_path, usecols=list_columns, encoding=encoding, dtype=str,sep=sep)
                else:
                    df = pd.read_csv(file_path, encoding=encoding, dtype=str,sep=sep)
        elif file_path.endswith('.xlsx'):
            sheet_name = 0 if not self.table_metadata.xlsx_sheet_name else self.table_metadata.xlsx_sheet_name
            if list_columns:
                df = pd.read_excel(file_path, sheet_name,usecols=list_columns, dtype=str)
            else:
                df = pd.read_excel(file_path,sheet_name,dtype=str)
            if self.table_metadata.xlsx_read_options:
                if self.table_metadata.xlsx_read_options.header:
                    df.columns = df.iloc[self.table_metadata.xlsx_read_options.header-1].values
                if self.table_metadata.xlsx_read_options.skiprows:
                    df.drop(index=[i-1 for i in self.table_metadata.xlsx_read_options.skiprows], inplace=True)  
        if not df.empty:
            self.log.info(f'Total record in df: {str(len(df))}')     
            df.columns = [str(i).strip() for i in df.columns]
            df = df.rename(columns=self.table_metadata.rename_columns)
            print(f"data df:{df}")
            df = df[self.table_metadata.list_columns_to_ingest_to_stg]
            df = df.astype(self.table_metadata.datatype_mapping)
            df[df.select_dtypes("object").columns] = df.select_dtypes("object").fillna("nan")
            df.drop_duplicates(inplace=True)
            self.log.info(f'Total record in df after clean: {str(len(df))}')  
            rows = df.itertuples(index=False)
            self.oracle_hook.bulk_insert_rows(table=self.table_metadata.stg_table_schema.table_name, rows=rows, target_fields=self.table_metadata.list_columns_to_ingest_to_stg, commit_every=1000)
            self.log.info(f'Ingested Sheet {self.table_metadata.xlsx_sheet_name}')

    def execute(self, context: Dict[str, Any]) -> Any:
        self.log.info("Get List Downloaded Files")
        if self.local_file_path:
            local_file_path = self.local_file_path
        else:
            xcom_key = context['dag_run'].dag_id + '_' + 'downloaded_file_list'
            local_file_path = context['ti'].xcom_pull(key=xcom_key)
        process_date = context['dag_run'].logical_date + timedelta(1)
        if self.table_metadata and self.oracle_hook:
            self.log.info('Begin Ingest Data To DWH')
            for file_path in local_file_path:
                self._insert_data_to_stg(file_path=file_path, process_date=process_date)
        else:
            self.log.info('Do Nothing!')