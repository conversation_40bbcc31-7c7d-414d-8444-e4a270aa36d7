from __future__ import annotations
import json
# import connectorx
import pandas as pd
from UliPlot.XLSX import auto_adjust_xlsx_column_width
from typing import TYPE_CHECKING, Iterable, Mapping, Sequence, Any
# import re
from airflow.models import BaseOperator
# from airflow.providers.oracle.hooks.oracle import OracleHook

from DAGs.utils import DAGMonitor
from airflow.hooks.base import BaseHook
from airflow.models.connection import Connection

if TYPE_CHECKING:
    from airflow.utils.context import Context

from Provider.utils import *
def get_hook_by_connection_id(connection_id):
    hook = BaseHook.get_connection(connection_id)
    return hook

class GetDataOracleOperator(BaseOperator):
    """
    Executes sql code in a specific Oracle database.

    :param sql: the sql code to be executed. Can receive a str representing a sql statement,
        a list of str (sql statements), or reference to a template file.
        Template reference are recognized by str ending in '.sql'
        (templated)
    :param oracle_conn_id: The :ref:`Oracle connection id <howto/connection:oracle>`
        reference to a specific Oracle database.
    :param parameters: (optional, templated) the parameters to render the SQL query with.
    """

    template_fields: Sequence[str] = (
        'parameters',
        'sql',
    )
    template_ext: Sequence[str] = ('.sql',)
    template_fields_renderers = {'sql': 'sql'}
    ui_color = '#ededed'

    def __init__(
            self,
            *,
            sql: str | Iterable[str] | Mapping[str],
            oracle_conn_id: str = 'oracle_default',
            saved_folder: str,
            file_name: str,
            report_name: str,
            overwrite: bool,
            freq: Any,
            parameters: dict | None = None,
            split_by_file_key: bool = False,
            **kwargs,
    ) -> None:
        super().__init__(**kwargs)
        self.oracle_conn_id = oracle_conn_id
        self.sql = sql
        self.saved_folder = saved_folder  # saved folder path
        self.file_name = file_name  # saved file name
        self.report_name = report_name  # saved folder name
        self.overwrite = overwrite
        self.freq = freq
        self.parameters = parameters
        self.split_by_file_key = split_by_file_key

    def execute(self, context: Context) -> None:
        self.log.info("=====================================")
        self.parameters = DAGMonitor.set_params_operator(**context)

        self.log.info(self.parameters)
        execution_date = self.parameters['YESTERDAY']
        year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
        hook = Connection.get_connection_from_secrets(conn_id=self.oracle_conn_id).get_hook()
        saved_folder, file_name = set_saved_folder_and_file_name(self.overwrite, self.freq,
                                                                 self.saved_folder,
                                                                 self.file_name, self.report_name, year,
                                                              month, day)
        if not self.split_by_file_key:
            writer = pd.ExcelWriter(os.path.join(saved_folder, file_name), engine='xlsxwriter',
                                    options={'strings_to_urls': False})
            if self.sql:
                if not isinstance(self.sql, dict):
                    filtered_parameters = DAGMonitor.sql_get_filtered_params(self.parameters, self.sql)
                    self.log.info('Executing: %s', self.sql)
                    self.log.info('filtered_parameters:')
                    self.log.info(str(filtered_parameters))
                    df = hook.get_pandas_df(self.sql, parameters=filtered_parameters)
                    df.to_excel(writer, index=False)
                    # if not df.empty:
                    #     auto_adjust_xlsx_column_width(df, writer, sheet_name='Sheet1', margin=0, index=False)
                    writer.save()
                else:
                    for sql in self.sql:
                        filtered_parameters = DAGMonitor.sql_get_filtered_params(self.parameters, self.sql[sql])
                        self.log.info('Executing: %s', self.sql[sql])
                        self.log.info('filtered_parameters:')
                        self.log.info(str(filtered_parameters))
                        df = hook.get_pandas_df(self.sql[sql], parameters=filtered_parameters)
                        df.to_excel(writer, index=False, sheet_name=f'Sheet{sql}')
                        # if not df.empty:
                        #     auto_adjust_xlsx_column_width(df, writer, sheet_name=f'Sheet{sql}', margin=0, index=False)
                    writer.save()
        else:
            filtered_parameters = DAGMonitor.sql_get_filtered_params(self.parameters, self.sql)
            self.log.info('Executing: %s', self.sql)
            self.log.info('filtered_parameters:')
            self.log.info(str(filtered_parameters))
            df = hook.get_pandas_df(self.sql, parameters=filtered_parameters)
            if 'width_adj' not in df.columns:
                width_adj = 0
            else:
                width_adj = df['width_adj'][0]
                df = df.drop('width_adj', axis='columns')
            files = df.file_key.unique()
            if width_adj == 1:
                self.log.info('File use Auto Adjust')
            else:
                self.log.info('File does not use Auto Adjust')
            for i in files:
                self.log.info(f'Begin to save file {str(i)}')
                file_path = os.path.join(saved_folder, i)
                save_df = df.query(f"file_key == '{i}'").drop('file_key', axis='columns')
                if i.endswith('xlsx'):
                    writer = pd.ExcelWriter(file_path, engine='xlsxwriter', options={'strings_to_urls': False})
                    save_df.to_excel(writer, index=False, sheet_name='Sheet1')
                    if width_adj == 1:
                        auto_adjust_xlsx_column_width(save_df, writer, sheet_name='Sheet1', margin=0, index=False)
                    writer.save()
                elif i.endswith('csv'):
                    save_df.to_csv(file_path, index=False)
                elif i.endswith('json'):
                    save_df.to_json(file_path, orient="records")
                self.log.info(f'Saved {str(i)}')
            self.log.info(f'===================== Total Saved {str(len(files))} excel files! =====================')
            xcom_key = 'excel_file_path_'+context['dag'].dag_id
            self.log.info(f'============================================{xcom_key}')
            get_data_and_save_excel_xcom_result_file_path_json = os.path.join(saved_folder, f'{xcom_key}_{execution_date}.json')
    
            self.log.info(f'Writing File Name To Json File: {get_data_and_save_excel_xcom_result_file_path_json}')
            
            with open(get_data_and_save_excel_xcom_result_file_path_json, 'w') as file:
                file.write(json.dumps(df.file_key.str.replace('_', '/').str.replace('+', '_').unique().tolist()))
            
            self.log.info(f'Done Writing File Name To Json File: {get_data_and_save_excel_xcom_result_file_path_json}')
            
            self.log.info(f'Push File Json File Path To XCom: Key = {xcom_key}')
            
            context['ti'].xcom_push(key=xcom_key, value=get_data_and_save_excel_xcom_result_file_path_json)