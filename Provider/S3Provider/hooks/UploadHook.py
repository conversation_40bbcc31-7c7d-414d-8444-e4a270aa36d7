import requests
import os
import time
import datetime
from concurrent.futures import ThreadPoolExecutor
from airflow.hooks.base import BaseHook
from airflow.utils.context import Context
import json

class UpLoadS3Hook(BaseHook):
    
    def __init__(
        self,
        presign_url: str,
        saved_folder: str,
        **context: Context
    ) -> None:
        super().__init__(**context)
        self.presign_url = presign_url
        self.saved_folder = saved_folder
        
    def __putFileToS3(self, file_url: tuple[str]) -> tuple[str, int]:
        """Put FILE TO S3

        Args:
            file_url (tuple[str]): file name in index 0, S3 PUT URL in index 1
            Cụ thể: File name khi truyền vào đang ở định dạng aaa_bbb_ccc_dddd+eeee.xlsx
            Trong đó: 
                + aaa_bbb_ccc sẽ được viết là aaa/bbb/ccc -> Prefix để đưa đến endpoint API Presign
                + dddd+eeee.xlsx sẽ được viết là dddd_eeee.xlsx -> Tên File lưu dưới local dạng dddd+eeee.xlsx và trên S3 dạng dddd_eeee.xlsx
            -> File name khi được xử lý sẽ là: aaa/bbb/ccc/dddd_eeee.xlsx + gộp với Presign Base URL là sẽ tới endpoint
        Returns:
            _type_: path of local file and http put status code
        """
        
        file_name, url_put_s3 = file_url
        file_name = file_name.replace('_', '+').replace('/', '_')
        file_path = os.path.join(self.saved_folder, file_name)
        if file_path.endswith('.xlsx'):
            put_s3_headers = {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            }
        elif file_path.endswith('.csv'):
            put_s3_headers = {
            'Content-Type': 'text/csv'
            }
        elif file_path.endswith('.json'):
            put_s3_headers = {
            'Content-Type': 'application/json'
            }
        else:
            raise ValueError(f'Not support PUT {file_path.split(".")[-1]} file format!!!')
        self.log.info(f'Begin to put {str(file_name)}')
        with open(file_path, 'rb') as payload:
            res = requests.request("PUT", url_put_s3, headers=put_s3_headers, data=payload)
            if res.status_code != 200:
                timeout = 1
                while res.status_code != 200 and timeout < 5:
                    self.log.info('Failed to put file, attempt %s to retry %s now!' %(timeout, url_put_s3))
                    res = requests.request("PUT", url_put_s3, headers=put_s3_headers, data=payload)
                    time.sleep(5)
                    timeout += 1
        self.log.info(f'Done putting {str(file_name)}')
        return (file_path, res.status_code)
    
    def __concurrentPutFileToS3(self, presign_url_result: dict[str, str], max_workers: int = 20, **context) -> None:
        # list_files_and_url = context['ti'].xcom_pull(key='s3hook_presign_url_result')
        date_report = (context['dag_run'].logical_date - datetime.timedelta(1)).strftime("%Y%m%d")
        xcom_key = 'upload_file_result_'+context['dag'].dag_id
        s3hook_upload_file_result_file_path_json = os.path.join(self.saved_folder, f'{xcom_key}_{date_report}.json')
        
        with ThreadPoolExecutor(max_workers=max_workers) as pool:
            putFileResult = list(pool.map(self.__putFileToS3, presign_url_result.items()))
            
        self.log.info(f'Writing Put File Result To File: {s3hook_upload_file_result_file_path_json}')
        
        with open(s3hook_upload_file_result_file_path_json, 'w') as file:
            file.write(json.dumps(putFileResult))
            
        self.log.info(f'Done Writing Put File Result To File')
        
        self.log.info(f'Push Put File Result File Path To XCom: Key = s3hook_upload_file_result')
        
        self.log.info(f'============================= s3hook_upload_file_result =============================')
        
        self.log.info(str(putFileResult))
        
        context['ti'].xcom_push(key='s3hook_upload_file_result', value=s3hook_upload_file_result_file_path_json)
        
        self.log.info(f'Done Put File To S3')
        
    def run(self, presign_url_result: dict[str, str], max_workers: int = 20, **context):
        return self.__concurrentPutFileToS3(presign_url_result=presign_url_result, max_workers=max_workers, **context)
        