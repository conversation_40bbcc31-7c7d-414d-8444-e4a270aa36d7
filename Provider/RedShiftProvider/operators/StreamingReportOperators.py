from __future__ import annotations

import os
import pandas as pd
from UliPlot.XLSX import auto_adjust_xlsx_column_width
from typing import Iterable, Mapping, Sequence
from airflow.providers.common.sql.operators.sql import BaseSQLOperator


class GetDataRedShiftStreamingOperator(BaseSQLOperator):
    """
    Executes SQL code in a specific database
    :param sql: the SQL code or string pointing to a template file to be executed (templated).
    File must have a '.sql' extensions.
    :param autocommit: (optional) if True, each command is automatically committed (default: False).
    :param parameters: (optional) the parameters to render the SQL query with.
    :param handler: (optional) the function that will be applied to the cursor (default: fetch_all_handler).
    :param split_statements: (optional) if split single SQL string into statements (default: False).
    :param return_last: (optional) if return the result of only last statement (default: True).

    .. seealso::
        For more information on how to use this operator, take a look at the guide:
        :ref:`howto/operator:SQLExecuteQueryOperator`
    """

    template_fields: Sequence[str] = ("sql", "parameters")
    template_ext: Sequence[str] = (".sql", ".json")
    template_fields_renderers = {"sql": "sql", "parameters": "json"}
    ui_color = "#cdaaed"

    def __init__(
            self,
            *,
            redshift_conn_id: str = "redshift_default",
            sql: str | list[str],
            saved_folder,
            file_name,
            report_name,
            parameters: Mapping | Iterable | None = None,
            **kwargs,
    ) -> None:
        super().__init__(conn_id=redshift_conn_id, **kwargs)
        self.sql = sql
        self.parameters = parameters
        self.saved_folder = saved_folder
        self.file_name = file_name
        self.report_name = report_name

    def execute(self, context):
        self.log.info("Executing: %s", self.sql)
        hook = self.get_db_hook()
        df = hook.get_pandas_df(
            sql=self.sql,
            parameters=self.parameters,
        )
        print(df.head())
        # if hasattr(self, "_process_output"):
        #     self._process_output(*df)
        writer = pd.ExcelWriter(
            os.path.join(self.saved_folder, self.report_name, f'{self.file_name}.xlsx'), engine='xlsxwriter')
        df.to_excel(writer, index=False)
        auto_adjust_xlsx_column_width(df, writer, sheet_name='Sheet1', margin=0, index=False)
        writer.save()

    def _process_output(self, output):
        pass
