from DAGs.utils import DAGMonitor
from Provider.GoogleSheetProvider.hooks.Hook import GoogleSheetHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.models import BaseOperator
from datetime import datetime, timedelta
from typing import Dict, Any
from airflow.utils.context import Context
from contextlib import closing
import pandas as pd

class GoogleSheetGetDataOperator(BaseOperator):
    def __init__(self, 
                 oracle_conn_id: str,
                 spreadsheet_key: str,
                 schema: Dict[str, Any],
                 **kwargs,
                 ) -> None:
        super().__init__(**kwargs)
        self.oracle_hook = OracleHook(oracle_conn_id=oracle_conn_id)
        self.spreadsheet_key = spreadsheet_key
        self.schema = schema
    
    def _create_stg_table(self, stg_table_name: str, column_datatye: Dict[str, Any]) -> None:
        oracle_datamapping = {
                                'str': 'VARCHAR2(4000)', # Max length VARCHAR2
                                'float': 'NUMBER',
                                'int': 'NUMBER',
                                'datetime64': 'DATE'
                              }
        query = f"""CREATE TABLE STGDWH.{stg_table_name} (\n"""
        for column, datatype in column_datatye.items():
            query += f'\t{column} {oracle_datamapping.get(datatype)},\n'
        query=query[:-2]
        query += '\n\t)'
        try:
            self.log.info('Start Creating Table {stg_table_name}')
            self.log.info(query)
            self.oracle_hook.run(query, autocommit=True)
        except Exception as e:
            self.log.info('Table already exists')
    
    def _insert_data_to_stg(self, process_date: datetime) -> None:
        """datatype_mapping: 'str', 'int', 'float', 'datetime64'
        """
        stg_schema = 'STGDWH'
        for sheet_name, sheet_info in self.schema.items():
            self.log.info(f'Read And Ingest Sheet {sheet_name}')
            datatype_mapping = sheet_info.get('datatype_mapping')
            datatype_mapping.update({'PROCESS_DATE': 'datetime64'})
            stg_table_name = sheet_info.get('stg_table_name')
            new_column_name = tuple(datatype_mapping.keys())
            df = self._read_data(sheet_name=sheet_name)
            self.log.info(str(df.columns))
            df = df.rename(columns=sheet_info.get('rename_columns'))
            df['PROCESS_DATE'] = process_date
            df = df[datatype_mapping.keys()]
            df = df.astype(datatype_mapping)
            rows = df.itertuples(index=False)
            self._create_stg_table(stg_table_name=stg_table_name, column_datatye=datatype_mapping)
            self.oracle_hook.run(f'TRUNCATE TABLE {stg_schema}.{stg_table_name}', autocommit=True)
            self.oracle_hook.bulk_insert_rows(table=f'{stg_schema}.{stg_table_name}', rows=rows, target_fields=new_column_name, commit_every=1000)
            self.log.info(f'Ingested Sheet {sheet_name}')
        
    def _read_data(self, sheet_name):
        
        return self.sheet_hook.get_sheet_data_to_df(spreadsheet_key=self.spreadsheet_key, sheet_name=sheet_name)
    
    def execute(self, context: Context) -> Any:
        params = DAGMonitor.set_params_operator(**context)
        process_date = datetime.strptime(params['YESTERDAY'],'%Y%m%d') + timedelta(1)
        self._insert_data_to_stg(process_date=process_date)
            
        
        
        