from typing import Any, Dict
from airflow.models import BaseOperator
from airflow.utils.decorators import apply_defaults
from Provider.SharePointProvider.hooks.DownloadHook import DownloadSharePointHook
import pendulum
from DAGs.utils import DAGMonitor


class DownloadSharePointOperator(BaseOperator):

    @apply_defaults
    def __init__(
            self,
            sharepoint_conn_id: str,
            department: str,
            report_name: str,
            local_saved_folder: str,
            freq: Any,
            _type: Any,
            **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.department = department
        self.report_name = report_name
        self.local_saved_folder = local_saved_folder
        self.freq = freq
        self._type = _type

    def execute(self, context: Dict[str, Any]) -> Any:
        hook = DownloadSharePointHook(self.sharepoint_conn_id)
        self.log.info("Call SharePoint method")
        execution_date = DAGMonitor.set_params_operator(**context)['YESTERDAY']
        print("execution_date: " + execution_date)
        downloaded_file_list = hook.run(self.department, self.report_name, self.local_saved_folder, self.freq,
                                        self._type, execution_date)
        key_xcom = context['dag_run'].dag_id + '_' + 'downloaded_file_list'
        self.log.info(f'Push downloaded file path to xcom {key_xcom}')
        self.log.info(f"path:{self.local_saved_folder}")
        context['ti'].xcom_push(key=key_xcom, value=downloaded_file_list)
