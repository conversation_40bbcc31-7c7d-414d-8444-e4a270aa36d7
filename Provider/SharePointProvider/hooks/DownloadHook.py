from typing import List
import os
import shutil
from typing import Any, Dict, Optional
from office365.runtime.auth.user_credential import UserCredential
from office365.sharepoint.client_context import ClientContext
from airflow.hooks.base import BaseHook
from Provider.utils import *
import datetime


class DownloadSharePointHook(BaseHook):

    def __init__(
            self,
            sharepoint_conn_id: str
    ) -> None:
        super().__init__()
        self.sharepoint_conn_id = sharepoint_conn_id
        self.download_path = None

    def get_conn(self, headers: Optional[Dict[Any, Any]] = None) -> Any:
        conn = self.get_connection(self.sharepoint_conn_id)
        host = conn.host
        user = conn.login
        password = conn.get_password()
        self.download_path = conn.extra_dejson.get('download_path')
        session = ClientContext(host).with_credentials(UserCredential(user, password))
        self.log.info('Connect to SharePoint Sucessfully')
        return session

    def run(
            self,
            department: str,
            report_name: str,
            local_saved_folder: str,
            freq: Any,
            _type: Any,
            execution_date: Any
    ) -> List[str]:
        session = self.get_conn()
        if freq == 'daily_test':
            execution_date = (datetime.datetime.strptime(execution_date, '%Y%m%d') - datetime.timedelta(1)).strftime('%Y%m%d')
            year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
        else:
            year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
        saved_folder, file_name = set_saved_folder_and_file_name(None, freq, local_saved_folder,
                                                                 None, report_name, year, month, day)
        if os.path.exists(saved_folder):
            shutil.rmtree(saved_folder)
        os.mkdir(saved_folder)
        folder_rel_url = self.set_target_folder_url(freq, _type, department, report_name, year, month, day)
        files = session.web.get_folder_by_server_relative_url(folder_rel_url).files
        session.load(files).execute_query()
        list_file_path = []
        for file in files:
            self.log.info("Downloading file: {0} ...".format(file.properties["ServerRelativeUrl"]))
            download_file_name = os.path.join(saved_folder, os.path.basename(file.properties["Name"]))
            with open(download_file_name, "wb") as local_file:
                file.download(local_file)
                session.execute_query()
            self.log.info("[Ok] file has been downloaded: {0}".format(download_file_name))
            list_file_path.append(download_file_name)
        return list_file_path

    def set_target_folder_url(self, freq, _type, department, report_name, *args):
        year, month, day = args
        if freq == 'daily':
            return f'{self.download_path}/{_type}/{department}/{report_name}/{year}/{month}/{day}/'
        elif freq == 'monthly':
            return f'{self.download_path}/{_type}/{department}/{report_name}/{year}/{month}/'
        elif freq == 'quarterly':
            quarter = 'Q' + str((int(month)-1)//3 + 1)
            return f'{self.download_path}/{_type}/{department}/{report_name}/{year}/{quarter}/'
        elif freq == 'annually':
            return f'{self.download_path}/{_type}/{department}/{report_name}/{year}/'
        else:
            return f'{self.download_path}/{_type}/{department}/{report_name}/'

class DownloadFolderSharePointHook(DownloadSharePointHook):
    def __init__(self, sharepoint_conn_id: str, local_saved_folder: str) -> None:
        super().__init__(sharepoint_conn_id)
        self.local_saved_folder = local_saved_folder

    def run(self, relative_folder_url: str):
        session = self.get_conn()
        from_folder = session.web.get_folder_by_server_relative_url(relative_folder_url)
        zip_path = os.path.join(self.local_saved_folder, f'{os.path.basename(relative_folder_url)}.zip')
        with open(zip_path, "wb") as to_file:
            from_folder.download_folder(to_file).execute_query()
            self.log.info("Files has been downloaded: {0}".format(zip_path))