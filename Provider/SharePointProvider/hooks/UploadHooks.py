import os
from typing import Any, Callable, Dict, Optional, Union
from office365.runtime.auth.user_credential import UserCredential
from office365.sharepoint.client_context import ClientContext
from airflow.hooks.base import BaseHook
from Provider.utils import *


class UploadSharePointHook(BaseHook):

    def __init__(
            self,
            sharepoint_conn_id: str
    ) -> None:
        super().__init__()
        self.sharepoint_conn_id = sharepoint_conn_id
        self.upload_path = None

    def get_conn(self, headers: Optional[Dict[Any, Any]] = None) -> Any:
        conn = self.get_connection(self.sharepoint_conn_id)
        host = conn.host
        user = conn.login
        password = conn.get_password()
        self.upload_path = conn.extra_dejson.get('upload_path')
        session = ClientContext(host).with_credentials(UserCredential(user, password))
        self.log.info('Connect to SharePoint Sucessfully')
        return session

    def run(
            self,
            department: str,
            report_name: str,
            local_file_name: str,
            local_saved_folder: str,
            overwrite: bool,
            freq: Any,
            _type: Any,
            execution_date: Any
    ) -> Any:
        session = self.get_conn()
        year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
        saved_folder, file_name = set_saved_folder_and_file_name(overwrite, freq, local_saved_folder,
                                                                 local_file_name, report_name, year, month, day)
        target_folder_url = self.set_target_folder_url(_type, freq, department, report_name, year, month, day)
        target_folder = session.web.ensure_folder_path(target_folder_url).execute_query()
        self.log.info(f'Create or replace folder {department}/{report_name} Successfully!')
        target_folder.files.create_upload_session(f'{saved_folder}/{file_name}',
                                                  1000000).execute_query()
        self.log.info(f'Upload file {report_name} to SharePoint Successfully!')

    def set_target_folder_url(self, _type, freq, department, report_name, *args):
        year, month, day = args
        if freq == 'daily':
            return f'{self.upload_path}/{_type}/{department}/{report_name}/{year}/{month}/{day}/'
        elif freq == 'monthly':
            return f'{self.upload_path}/{_type}/{department}/{report_name}/{year}/{month}/'
        elif freq == 'quarterly':
            quarter = 'Q' + str((int(month)-1)//3 + 1)
            return f'{self.upload_path}/{_type}/{department}/{report_name}/{year}/{quarter}/'
        elif freq == 'annually':
            return f'{self.upload_path}/{_type}/{department}/{report_name}/{year}/'
        else:
            return f'{self.upload_path}/{_type}/{department}/{report_name}/'
