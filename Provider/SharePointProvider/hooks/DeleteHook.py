from concurrent.futures import ThreadPoolExecutor
from typing import List, <PERSON><PERSON>
from Provider.SharePointProvider.hooks.DownloadHook import DownloadSharePointHook
from tenacity import retry, wait_fixed, stop_after_attempt

class DeleteFileSharePointHook(DownloadSharePointHook):
    def __init__(self, sharepoint_conn_id: str) -> None:
        super().__init__(sharepoint_conn_id)
    
    @retry(wait=wait_fixed(10), stop=stop_after_attempt(5))
    def _delete_file_by_relative_url(self, file_relative_url: str):
        session = self.get_conn()
        try:
            self.log.info(f'Start deleting sharepoint file: {file_relative_url}')
            file = session.web.get_file_by_server_relative_url(file_relative_url)
            file.delete_object().execute_query()
            self.log.info(f'Done deleting sharepoint file: {file_relative_url}')
        except BaseException as err:
            if '404 Client Error' not in str(err):
                raise RuntimeError(err)
            else:
                self.log.info(f'File not exist on Sharepoint. Skip deleting {file_relative_url}')

    def run(self, list_file_relative_url: List[str] | Tuple[str]):
        with ThreadPoolExecutor(max_workers=6) as pool:
            res = list(pool.map(self._delete_file_by_relative_url, list_file_relative_url))   