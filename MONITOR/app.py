from flask import Flask, render_template, request
from utils import *

app = Flask(__name__)

ADMIN_TOKEN = 'admin_token'


# normal endpoint
@app.route('/')
def index():
    dag_pattern_type_list = get_pattern_type()
    return render_template('/index.html', dag_pattern_type_list=dag_pattern_type_list)


@app.route('/getByType')
def getByType():
    selected_type = request.args.get('selectedType')
    return get_by_type(selected_type)


@app.route('/getFolderByDepartment', methods=['GET'])
def getFolderByDepartment():
    department = request.args.get('department')
    rs = get_folder_by_department(department=department)
    return rs


@app.route('/getSourceAndTargetByType', methods=['GET'])
def getSourceAndTargetByType():
    type = request.args.get('type')
    rs = get_source_and_target_by_pattern_type(patternType=type)
    return rs


@app.route('/createDags', methods=['POST'])
def create():
    try:
        data = request.get_json()
        add_dag(data)
        return {'status': 'success', 'message': 'Data inserted successfully'}
    except Exception as e:
        print(str(e))
        return {'status': 'failure', 'message': str(e)}, 500


# # admin endpoint
@app.route('/admin')
def admin():
    token = request.args.get('token')
    if token == ADMIN_TOKEN:  # Check if the token is valid
        all_dag = get_all()
        general_tree = get_pattern_type_admin()
        return render_template('/admin.html', general_tree=general_tree, dags=all_dag)
    else:
        return 'Access denied'


@app.route('/getByTypeAndMainId')
def getByTypeAndMainId():
    selected_type = request.args.get('type')
    mainId = request.args.get('main_dag_id')
    dag_info = get_by_type_and_main_id(selected_type, mainId)
    general_tree = get_pattern_type_admin()
    return render_template('/admin.html', dags=dag_info, general_tree=general_tree)


@app.route('/getInfoDagByID')
def getInfoDagByID():
    dag_id = request.args.get('dagId')
    dag_info = get_by_dag_id(dag_id)
    json_dag = json.loads(dag_info)
    return json_dag


@app.route('/updateDag', methods=['POST'])
def updateDag():
    try:
        data = request.get_json()
        # print(data)
        update_dag(data)
        return {'status': 'success', 'message': 'Data updated successfully'}
    except Exception as e:
        print(str(e))
        return {'status': 'failure', 'message': str(e)}, 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
