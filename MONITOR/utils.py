import DAGs.utils
from MailService.SendMail import send_email_to_admin, send_email_to_requestor
import cx_Oracle
import ast
import json
from enum import Enum
from config import *
from DAGs.utils import DAGMonitor

DAG_MONITOR = 'F88DWH.W_DAG_MONITOR'


class FetchType(Enum):
    ALL = 'ALL'
    ONE = 'ONE'
    INSERT = 'INSERT'
    UPDATE = 'UPDATE'


class Connection:
    connection = None

    def execute_query(self, query, fetchType, parameters=None):
        if not self.connection:
            # connection init
            # dsn_tns = cx_Oracle.makedsn(Config.DB_HOST, Config.DB_PORT, service_name=Config.DB_SERVICE_NAME)
            # self.connection = cx_Oracle.connect(user=Config.DB_USER, password=Config.DB_PWD, dsn=dsn_tns)
            self.connection = DAGs.utils.DAGMonitor.get_db_connection()
        cursor = self.connection.cursor()
        if fetchType == FetchType.ONE:
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()
            return result
        elif fetchType == FetchType.INSERT:
            cursor.setinputsizes(query=cx_Oracle.CLOB)
            cursor.execute(query, parameters)
            self.connection.commit()
        elif fetchType == FetchType.ALL:
            cursor.execute(query)
            result = cursor.fetchall()
            cursor.close()
            return result
        else:
            cursor.execute(query, parameters)
            self.connection.commit()


class DAGMonitor:
    def __init__(self, main_dag_id, dag_id, source_conn_id, destination_conn_id, query,
                 save_folder, file_name, report_name, overwrite, freq, department, type, status, description,
                 pattern_type, requestor, tableMetaData, createdDt, startDate, scheduleInterval, reason, tags):
        self.main_dag_id = main_dag_id
        self.dag_id = dag_id
        self.source_conn_id = source_conn_id
        self.destination_conn_id = destination_conn_id
        self.query = query
        self.save_folder = save_folder
        self.file_name = file_name
        self.report_name = report_name
        self.overwrite = overwrite
        self.freq = freq
        self.department = department
        self.status = status
        self.description = description
        self.pattern_type = pattern_type
        self.type = type
        self.requestor = requestor
        self.tableMetaData = tableMetaData
        self.createdDt = createdDt
        self.startDate = startDate
        self.scheduleInterval = scheduleInterval
        self.reason = reason
        self.tags = tags

    def to_json(self):
        return json.dumps(self, cls=DAGMonitorEncoder)


class DAGMonitorEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, DAGMonitor):
            return obj.__dict__
        return super().default(obj)


def convert_to_tree(main_dag_ids):
    result = {}
    for item in main_dag_ids:
        key = next(iter(item))
        value = item[key]
        category, file_name = value.split("_", 2)[1:]

        if category not in result:
            result[category] = []
        # if file_name not in ['DWH2S3_1.LOS']:
        result[category].append(file_name)
    return result


def get_folder_by_department(department):
    query = f"SELECT DISTINCT DEPARTMENT FROM {DAG_MONITOR} WHERE MAIN_DAG_ID like '%{department}' AND DEPARTMENT IS NOT NULL"
    conn = Connection()
    row = conn.execute_query(query=query, fetchType=FetchType.ONE)
    if row:
        department = row[0]
        dag_monitor = DAGMonitor(None, None, None, None, None, None, None, None, None, None,
                                 department, None, None, None, None, None, None, None, '', '', '', '')
        json_data = dag_monitor.to_json()
        return json_data
    else:
        return {}


def get_source_and_target_by_pattern_type(patternType):
    query = f"SELECT DISTINCT SOURCE_CONN_ID,DESTINATION_CONN_ID,TYPE FROM {DAG_MONITOR} WHERE PATTERN_TYPE = '{patternType}'"
    conn = Connection()
    row = conn.execute_query(query=query, fetchType=FetchType.ONE)
    if row:
        source_conn_id = row[0]
        destination_conn_id = row[1]
        type_ = row[2]
        dag_monitor = DAGMonitor(None, None, source_conn_id, destination_conn_id, None, None, None, None, None, None,
                                 None, type_, None, None, None, None, None, None, '', '', '', '')
        json_data = dag_monitor.to_json()
        return json_data
    else:
        return {}


def get_pattern_type():
    result = {}
    query = f"SELECT DISTINCT PATTERN_TYPE FROM {DAG_MONITOR} ORDER BY PATTERN_TYPE ASC"  # WHERE STATUS = 1
    conn = Connection()
    rows = conn.execute_query(query=query, fetchType=FetchType.ALL)
    # Iterate over the rows and process the CLOB data
    for row in rows:
        data_by_type = get_by_type(row[0])
        result[row[0]] = (data_by_type)

    return result


def get_by_type(type):
    dags = get_by_pattern_type(type)
    formatted_main_dag_ids = convert_to_tree(dags)
    return formatted_main_dag_ids


def get_by_pattern_type(pattern_type):
    dag_monitor_list = []
    query = f"SELECT DISTINCT TO_NUMBER(REGEXP_SUBSTR( MAIN_DAG_ID , '\d+\.')) AS a ,MAIN_DAG_ID  FROM {DAG_MONITOR} WHERE PATTERN_TYPE ='{pattern_type}' ORDER BY a ASC" #AND STATUS = 0
    conn = Connection()
    rows = conn.execute_query(query=query, fetchType=FetchType.ALL)
    # Iterate over the rows and process the CLOB data
    for row in rows:
        dag_monitor_list.append({row[0]: row[1]})
    return dag_monitor_list


def get_by_type_and_main_id(selected_type, department):
    dag_monitor_list = []
    query = f"SELECT main_dag_id, dag_id, source_conn_id, destination_conn_id, query, save_folder, file_name, report_name, overwrite, freq, department, type, status, description, pattern_type,requestor,table_metadata,TO_CHAR(created_dt, 'DD-MM-YYYY') FROM {DAG_MONITOR} WHERE PATTERN_TYPE='{selected_type}' AND MAIN_DAG_ID LIKE '%{department}'"
    conn = Connection()
    rows = conn.execute_query(query=query, fetchType=FetchType.ALL)
    for row in rows:
        dag_monitor = DAGMonitor(
            row[0],
            row[1],
            row[2],
            row[3],
            '' if row[4] is None else row[4].read(),
            row[5],
            row[6],
            row[7],
            row[8],
            row[9],
            row[10],
            row[11],
            row[12],
            row[13],
            row[14],
            row[15],
            '' if row[16] is None else row[16].read(),
            row[17],
            '',
            '',
            '',
            ''
        )
        dag_monitor_list.append(dag_monitor)

    return dag_monitor_list


def add_dag(dag):
    try:

        # Define the parameter values
        parameters = {
            'mainDagID': dag['mainDagID'],
            'dagId': dag['dagId'],
            'sourceConID': dag['sourceConID'],
            'desConID': dag['desConID'],
            'saveFolder': 'Downloaded',
            'fileName': dag['fileName'],
            'reportName': dag['reportName'],
            'overwrite': int(dag['overwrite']),
            'freq': dag['freq'],
            'department': dag['department'],
            'type': dag['reportFolder'],
            'status': 0,  # DEACTIVE
            'description': dag['description'],
            'patternType': dag['patternType'],
            'requestor': dag['requestor'],
            'startDate': dag['startDate'],
            'scheduleInterval': dag['scheduleInterval'],
            'tags': dag['tags'],
            'query': dag['query']
        }

        if dag['patternType'] == 'SHAREPOINT2DWH':
            sql_insert = f"""
                           INSERT INTO {DAG_MONITOR} (
                               MAIN_DAG_ID, DAG_ID, SOURCE_CONN_ID, DESTINATION_CONN_ID, 
                               SAVE_FOLDER, FILE_NAME, REPORT_NAME, OVERWRITE, FREQ, DEPARTMENT, "TYPE",STATUS, DESCRIPTION, REQUESTOR, PATTERN_TYPE,CREATED_DT,START_DATE,SCHEDULE_INTERVAL,TAGS,TABLE_METADATA
                           )
                           VALUES(:mainDagID,:dagId,:sourceConID,:desConID, :saveFolder,:fileName,:reportName,:overwrite,:freq,:department,:type,:status,:description,:requestor,:patternType,SYSDATE,TO_DATE(:startDate,'yyyy-mm-dd'),:scheduleInterval,:tags,:query)

                       """


        else:
            sql_insert = f"""
                          INSERT INTO {DAG_MONITOR} (
                              MAIN_DAG_ID, DAG_ID, SOURCE_CONN_ID, DESTINATION_CONN_ID, 
                              SAVE_FOLDER, FILE_NAME, REPORT_NAME, OVERWRITE, FREQ, DEPARTMENT, "TYPE",STATUS, DESCRIPTION, REQUESTOR, PATTERN_TYPE,CREATED_DT,START_DATE,SCHEDULE_INTERVAL,TAGS,QUERY
                          )
                          VALUES(:mainDagID,:dagId,:sourceConID,:desConID, :saveFolder,:fileName,:reportName,:overwrite,:freq,:department,:type,:status,:description,:requestor,:patternType,SYSDATE,TO_DATE(:startDate,'yyyy-mm-dd'),:scheduleInterval,:tags,:query)

                          """
        conn = Connection()
        conn.execute_query(query=sql_insert, fetchType=FetchType.INSERT, parameters=parameters)
        send_email_to_admin(dag['requestor'], dag['dagId'])
    except Exception as e:
        raise e


def update_dag(dag):
    try:
        parameters = {
            # Provide the value for MAIN_DAG_ID
            'status': dag['status'],  # Provide the value for DAG_ID
            'dag_id': dag['dagId'],
            'scheduleInterval': dag['scheduleInterval'],
            'reason': dag['reason'],
            'tags': dag['tag'],
            'startDate': dag['startDate'],
            'requestor': dag['requestor'],
            'description': dag['description']
        }
        if dag['patternType'] == 'SHAREPOINT2DWH':
            parameters['query'] = json.dumps(ast.literal_eval(dag['query']))
            query = f"""
                    UPDATE
                        {DAG_MONITOR} d
                    SET
                        STATUS =:status,
                        SCHEDULE_INTERVAL=:scheduleInterval,
                        REASON=:reason,
                        START_DATE=TO_DATE(:startDate,'yyyy-mm-dd'),
                        REQUESTOR=:requestor,
                        DESCRIPTION=:description,
                        TAGS=:tags,
                        TABLE_METADATA =:query
                        
                    WHERE
                        DAG_ID = :dag_id
                """
        else:
            query = f"""
                    UPDATE
                        {DAG_MONITOR}
                    SET
                        STATUS =:status,
                        SCHEDULE_INTERVAL=:scheduleInterval,
                        REASON=:reason,
                        START_DATE=TO_DATE(:startDate,'yyyy-mm-dd'),
                        REQUESTOR=:requestor,
                        DESCRIPTION=:description,
                        TAGS=:tags,
                        QUERY =:query
                    WHERE
                        DAG_ID = :dag_id
                """
            parameters['query'] = dag['query']

        conn = Connection()
        conn.execute_query(query=query, fetchType=FetchType.UPDATE, parameters=parameters)
    except Exception as e:
        raise e

    try:
        send_email_to_requestor(reason=dag['reason'], status=dag['status'], requestor=dag['requestor'],
                                dagId=dag['dagId'])
    except Exception:
        # Try to send mail
        pass


def get_all():
    dag_monitor_list = []
    # Execute a SELECT statement to retrieve data from the table with a CLOB column
    query = f"SELECT main_dag_id,dag_id,source_conn_id,destination_conn_id,query,save_folder,file_name,report_name,overwrite, freq, department,type,status,description,pattern_type,requestor,table_metadata,TO_CHAR(created_dt, 'DD-MM-YYYY') FROM {DAG_MONITOR}"

    conn = Connection()
    rows = conn.execute_query(query=query, fetchType=FetchType.ALL)
    # Iterate over the rows and process the CLOB data
    for row in rows:
        dag_monitor = DAGMonitor(
            row[0],
            row[1],
            row[2],
            row[3],
            '' if row[4] is None else row[4].read(),
            row[5],
            row[6],
            row[7],
            row[8],
            row[9],
            row[10],
            row[11],
            row[12],
            row[13],
            row[14],
            row[15],
            '' if row[16] is None else row[16].read(),
            row[17],
            '',
            '',
            '',
            ''
        )
        dag_monitor_list.append(dag_monitor)
    return dag_monitor_list


def get_by_dag_id(dagId):
    # Execute a SELECT statement to retrieve data from the table with a CLOB column
    query = f"SELECT main_dag_id,dag_id,source_conn_id,destination_conn_id,query,save_folder,file_name,report_name,overwrite, freq, department,type,status,description,pattern_type,requestor,table_metadata,TO_CHAR(created_dt, 'DD-MM-YYYY'),TO_CHAR(start_date,'YYYY-MM-DD'),schedule_interval,reason,tags as created_dt FROM {DAG_MONITOR} WHERE DAG_ID='{dagId}'"
    conn = Connection()
    row = conn.execute_query(query=query, fetchType=FetchType.ONE)
    # Iterate over the rows and process the CLOB data
    if row:
        dag_monitor = DAGMonitor(
            row[0],
            row[1],
            row[2],
            row[3],
            '' if row[4] is None else row[4].read(),
            row[5],
            row[6],
            row[7],
            row[8],
            row[9],
            row[10],
            row[11],
            row[12],
            row[13],
            row[14],
            row[15],
            '' if row[16] is None else row[16].read(),
            row[17],
            row[18],  # START_DATE
            row[19],  # SCHEDULE_INTERVAL
            row[20],  # REASON
            '' if row[21] is None else row[21].read()  # TAGS
        )
        return dag_monitor.to_json()
    return {}


# admin function

def get_pattern_type_admin():
    result = {}
    query = f"SELECT DISTINCT PATTERN_TYPE FROM {DAG_MONITOR} ORDER BY PATTERN_TYPE ASC"  # WHERE STATUS = 1
    conn = Connection()
    rows = conn.execute_query(query=query, fetchType=FetchType.ALL)
    # Iterate over the rows and process the CLOB data
    for row in rows:
        data_by_type = get_by_type_admin(row[0])
        result[row[0]] = (data_by_type)

    return result


def get_by_type_admin(type):
    dags = get_by_pattern_type_admin(type)
    formatted_main_dag_ids = convert_to_tree(dags)
    return formatted_main_dag_ids


def get_by_pattern_type_admin(pattern_type):
    dag_monitor_list = []
    query = f"SELECT DISTINCT TO_NUMBER(REGEXP_SUBSTR( MAIN_DAG_ID , '\d+\.')) AS a ,MAIN_DAG_ID  FROM {DAG_MONITOR} WHERE PATTERN_TYPE ='{pattern_type}' ORDER BY a ASC"
    conn = Connection()
    rows = conn.execute_query(query=query, fetchType=FetchType.ALL)
    # Iterate over the rows and process the CLOB data
    for row in rows:
        dag_monitor_list.append({row[0]: row[1]})
    return dag_monitor_list
