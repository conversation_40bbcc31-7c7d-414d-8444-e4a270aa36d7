import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr
from email.header import Header
from config import *

your_email = Config.BOT_EMAIL
your_password = Config.BOT_EMAIL_PWD
EMAIL_ADMIN = Config.EMAIL_ADMIN
def send_email_to_admin(requestor, dagId):
    server = smtplib.SMTP_SSL(Config.SMTP_HOST, Config.SMTP_PORT)
    server.ehlo()
    server.login(your_email, your_password)

    html_content = f'''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị Quản Trị Dữ Liệu,</p>
        <p></p>
        <p>Anh chị có một yêu cầu thêm pipeline mới của <strong>{requestor}</strong></p>
        <p><strong>Dag ID</strong>: {dagId}</p>
        <P></P>
        <P>Trân trọng!</P>
        <P>Em trợ lý - Dag Monitor Assistant</P>
    </body>
    </html>
    '''

    msg = MIMEMultipart()
    msg['From'] = formataddr((str(Header('Phòng Quản trị dữ liệu - DAG MONITOR', 'utf-8')), your_email))
    msg['To'] = EMAIL_ADMIN  #EMAIL ADMIN 
    msg['Subject'] = 'THÔNG BÁO VỀ VIỆC YÊU CẦU THÊM MỚI JOB PIPELINE - AIRFLOW'
    # content=html_content.format(dag_id = dag_id, date = execution_date)
    content=html_content
    html_part = MIMEText(content, 'html')
    # msg.attach(html_part)
    msg.attach(html_part)
    

    server.send_message(msg)
    server.close()
    
    
def send_email_to_requestor(requestor,status,reason, dagId):
    server = smtplib.SMTP_SSL(Config.SMTP_HOST, Config.SMTP_PORT)
    server.ehlo()
    server.login(your_email, your_password)

    reject_html_content = f'''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị {requestor},</p>
        <p></p>
        <p>Yêu cầu thêm pipeline mới của anh/chị với Dag ID: {dagId} đã bị <strong>từ chối</strong> vì lý do <strong>{reason}</strong>  </p>
        <P></P>
        <P><strong>Trân trọng!</strong></P>
        <P><strong>DAG MONITOR BOT</strong></P>
        <P><strong>Phòng Quản Trị Dữ Liệu</strong></P>
        
    </body>
    </html>
    '''

    approve_html_content = f'''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị {requestor},</p>
        <p></p>
        <p>Yêu cầu thêm pipeline mới của anh/chị với Dag ID: {dagId} đã được quản trị viên <strong>phê duyệt</strong></p>
        <P></P>
        <P><strong>Trân trọng!</strong></P>
        <P><strong>DAG MONITOR BOT</strong></P>
        <P><strong>Phòng Quản Trị Dữ Liệu</strong></P>
        
    </body>
    </html>
    '''
    
    msg = MIMEMultipart()
    msg['From'] = formataddr((str(Header('Phòng Quản trị dữ liệu - DAG MONITOR', 'utf-8')), your_email))
    msg['To'] = f"{requestor}@f88.vn"
    msg['Subject'] = 'THÔNG BÁO VỀ VIỆC YÊU CẦU THÊM MỚI JOB PIPELINE - AIRFLOW'
    if int(status) == 0:
        content=reject_html_content
    elif int(status) == 1:
        content=approve_html_content
        
    html_part = MIMEText(content, 'html')
    msg.attach(html_part)
    server.send_message(msg)
    server.close()
    
    

