//admin script
$(document).ready(function() {
    // Event listener for the click on the edit link
    $('.edit').click(function(e) {
      e.preventDefault(); // Prevent the default link behavior
      var dagId = $(this).data('row-id');
      // Make the AJAX request with the dagId value
      $.ajax({
        url: '/getInfoDagByID?dagId=' + dagId,
        method: 'GET',
        success: function(response) {
          // Handle the successful response
          // Assuming the response is a JSON object, you can access its properties
          $('#sourceConIDTxt').val(response.source_conn_id),
          $('#desConIDTxt').val(response.destination_conn_id),
          $('#departmentFolderTxt').val(response.department),
          $('#reportFolderTxt').val(response.type),
          $('#dagId').val(response.dag_id),
          $('#fileName').val(response.file_name),
          $('#reportName').val(response.report_name),
          $('#description').val(response.description),
          $('#patternTypeInput').val(response.pattern_type)
          $('#requestor').val(response.requestor);
          $('#startDate').val(response.startDate);
          $('#schedulerInput').val(response.scheduleInterval);
          $('#reason-input').val(response.reason);
          $('#tags').val(response.tags)

          if (response.overwrite === 0){
            $('#overwrite').val('False')
          } else{
            $('#overwrite').val('True')
          }

          if (response.status === 1) {
            $('#active').prop('checked', true);
          } else {
            $('#deactive').prop('checked', true);
          }

          if(response.pattern_type=="SHAREPOINT2DWH"){
            $('#query').val(JSON.stringify(JSON.parse(response.tableMetaData)));
          } else{
            $('#query').val(response.query);
          } 
          // Open the modal
          $('#editEmployeeModal').modal('show');
        },
        error: function(xhr, status, error) {
          // Handle the error response
          console.error(error);
        }
      });
    });

    $('#schedulerInput').change(function (event) {
        try {
          cronExpression = $('#schedulerInput').val();
          if (cronExpression == "") {
            $('#error-message').hide()
            $('#result-cron').hide()
            return
          }
          $('#schedulerInput').removeClass('is-invalid');
          $('#schedulerInput').addClass('is-valid');
          $('#error-message').hide()
          $('#result-cron').text(cronstrue.toString(cronExpression));
          $('#result-cron').show()
        } catch (error) {
          $('#result-cron').hide()
          $('#schedulerInput').addClass('is-invalid');
          $('#error-message').text(error);
          $('#error-message').show();
          event.preventDefault();
          console.error("An error occurred:", error);
        }
      });

    $('#update-form').submit(function(event) {
        var patternType = $('#patternTypeInput').val()
        event.preventDefault(); // Prevent form submission
        // Get form data
        var status = $('input[name="status-radio"]:checked').val()
        var query =  $('#query').val()
        var reason = $('#reason-input').val()
        var scheduleInterval = $('#schedulerInput').val()
        var requestor = $('#requestor').val()
        var description = $('#description').val()
        var formData = {
          dagId: $('#dagId').val(),       
          status: status,
          query:query,
          reason:reason,
          scheduleInterval:scheduleInterval,
          patternType: patternType,
          tag:$('#tags').val(),
          startDate:$('#startDate').val(),
          requestor:requestor,
          description:description
        };
        if (status === "0") {
            // Check if the input element is empty
            if ($('#reason-input').val() === '') {
              // Add the 'required' attribute to the input element
              $('#reason-input').prop('required', true);
              // Prevent the form submission
              event.preventDefault();
              return;
            }
        }
        $.ajax({
          url: '/updateDag',
          type: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(formData),
          success: function(response) {
            $('#update-form')[0].reset();
            var alertHtml = '<div class="alert alert-success" role="alert">Data successfully updated.</div>';
            $('#alertContainer').html(alertHtml);
            $('#alertContainer').fadeIn().delay(5000).fadeOut();
            location.reload();
          },
          error: function(xhr, status, error) {
            var alertHtml = '<div class="alert alert-danger" role="alert">'+xhr.responseText+'</div>';
            $('#alertContainer').html(alertHtml);
            $('#alertContainer').fadeIn().delay(5000).fadeOut();
          }
        });
      });
  });