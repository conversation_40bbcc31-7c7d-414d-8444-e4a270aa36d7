$(document).ready(function () {
  var dag_info;
  var categorySelect = $('#categorySelect');

  $('#typeSelect').change(function () {
    var selectedType = $(this).val();
    resetDepartment()
    if (selectedType == "DWH2SHAREPOINT") {
      $('#fileName').attr('required', true);
      $('#form-filename').show();

      $('#slc-overwrite').attr('required', true);
      $('#form-overwrite').show();

      $('#categorySelect').attr('required', true);
      $('#form-fre').show()

      $('#quarterly-report').hide()

      $('#lbl-query').text('Query');
      $('#query').attr('placeholder', 'Fill query to get data from DWH');

    } else if (selectedType == "DWH2S3") {
      $('#categorySelect').attr('required', true);
      $('#form-fre').show()
      $('#lbl-query').text('Query');
      $('#query').attr('placeholder', 'Fill query to get data from DWH');
      $('#fileName').removeAttr('required');
      $('#form-filename').hide();
      $('#slc-overwrite').removeAttr('required');
      $('#form-overwrite').hide();
      $('#quarterly-report').hide()
    } else if (selectedType == "SHAREPOINT2DWH") {
      $('#lbl-query').text('Meta data');
      $('#query').attr('placeholder', 'Fill meta data to input your pipeline');
      $('#fileName').removeAttr('required');
      $('#form-filename').hide();
      $('#slc-overwrite').removeAttr('required');
      $('#form-overwrite').hide();
      $('#categorySelect').removeAttr('required');
      $('#form-fre').hide()
      $('#quarterly-report').show()

    }

    $.ajax({
      url: '/getByType',
      type: 'GET',
      data: {
        selectedType: selectedType
      },
      dataType: 'json',
      success: function (data) {
        dag_info = data
        if (selectedType == "SHAREPOINT2DWH") {
          var departments = dag_info[selectedType];
          resetDepartment()
          departments = dag_info[selectedType]
          $.each(dag_info[selectedType], function (index) {
            $('#departmentSelect').append('<option value="' + departments[index] + '">' + departments[index] + '</option>');
          });
          $('#departmentSelect').append('<option value="other">Other</option>');

        } else {
          categorySelect.empty();
          categorySelect.append('<option value="">Select an frequency...</option>');
          $.each(dag_info, function (key) {
            console.log(key)
            categorySelect.append('<option value="' + key + '">' + key + '</option>');
          });
        }
      },
      error: function (xhr, status, error) {
        console.log('Error:', error);
      }
    });

    $.ajax({
      url: '/getSourceAndTargetByType',
      type: 'GET',
      data: {
        type: selectedType
      },
      dataType: 'json',
      success: function (data) {
        $('#desConIDTxt').val(data.destination_conn_id)
        $('#sourceConIDTxt').val(data.source_conn_id)
        $('#reportFolderTxt').val(data.type)
      },
      error: function (xhr, status, error) {
        console.log('Error:', error);
      }
    });
  });

  $(document).on('change', '#categorySelect', function () {
    resetDepartment()
    var selectedCategory = $(this).val();
    if (selectedCategory) {
      var departments = dag_info[selectedCategory];
      $.each(departments, function (index) {
        $('#departmentSelect').append('<option value="' + departments[index] + '">' + departments[index] + '</option>');
      });
      $('#departmentSelect').append('<option value="other">Other</option>');
    }

  });

  $(document).on('change', '#departmentSelect', function () {
    department = $(this).val();
    if (department === 'other') {
      var inputField = $('<input>').attr({
        type: 'text',
        class: 'form-control',
        id: 'departmentSelect',
        placeholder: 'Enter the department...'
      });

      $(this).replaceWith(inputField);
      $('#departmentFolderTxt').removeAttr('disabled');
    }
    else {
      $.ajax({
        url: '/getFolderByDepartment',
        type: 'GET',
        data: {
          department: department
        },
        dataType: 'json',
        success: function (data) {
          $('#departmentFolderTxt').val(data.department)

        },
        error: function (xhr, status, error) {
          console.log('Error:', error);
        }
      });

    }
    var element = $("#departmentSelect");

    if (element.is("select")) {
      $('#departmentFolderTxt').attr('disabled', 'true');
    }

  });

  $('#dagId').change(function (event) {
    if (!validateDagId()) {
      event.preventDefault();
      $('#dagId').focus()
    }
  });
  // check scheduler interval
  $('#schedulerInput').change(function (event) {
    validateCron()
  });

  $('#myForm').submit(function (event) {
    event.preventDefault();
    if (!validateCron()) {

      return;
    }
    if (!validateDagId) {
      return;
    }
    tagCombined = [$('#typeSelect').val(), $('input[name="folder-type-radio"]:checked').val(), $('#departmentFolderTxt').val()]
    var jsonTagCombined = JSON.stringify(tagCombined);
    var formData = {
      patternType: $('#typeSelect').val(),
      mainDagID: 'MAIN_' + $('#categorySelect').val() + "_" + $('#departmentSelect').val(),
      sourceConID: $('#sourceConIDTxt').val(),
      desConID: $('#desConIDTxt').val(),
      query: $('#query').val(),
      department: $('#departmentFolderTxt').val(),
      reportFolder: $('#reportFolderTxt').val(),
      dagId: $('#dagId').val(),
      fileName: $('#fileName').val(),
      reportName: $('#reportName').val(),
      overwrite: $('#slc-overwrite').val(),
      description: $('#description').val(),
      requestor: $('#requestor').val(),
      query: $('#query').val(),
      freq: $('input[name="folder-type-radio"]:checked').val(),
      startDate: $('#startDate').val(),
      tags: jsonTagCombined,
      scheduleInterval: $('#schedulerInput').val(),
    };


    $.ajax({
      url: '/createDags',
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify(formData),
      success: function (response) {
        $('#myForm')[0].reset();
        var alertHtml = '<div class="alert alert-success" role="alert">Data successfully inserted.</div>';
        $('#alertContainer').html(alertHtml);
        // Show alertContainer for 5 seconds
        $('#alertContainer').fadeIn().delay(5000).fadeOut();
      },
      error: function (xhr, status, error) {
        var alertHtml = '<div class="alert alert-danger" role="alert">' + xhr.responseText + '</div>';
        $('#alertContainer').html(alertHtml);
        // Show alertContainer for 5 seconds
        $('#alertContainer').fadeIn().delay(5000).fadeOut();
      }
    });
  });

  function resetDepartment() {
    var optionField = $('<select>').attr({
      class: "form-control",
      id: "departmentSelect",
      required: true
    });
    $('#departmentSelect').replaceWith(optionField);
    $('#departmentSelect').empty();
    $('#departmentSelect').append('<option value="">Select a department...</option>');
  }

  function validateDagId() {
    dagId = $('#dagId').val()
    if (checkValid(dagId)) {
      $('#dagId').removeClass('is-invalid');
      $('#dagId').addClass('is-valid');
      return true
    } else {
      $('#dagId').removeClass('is-valid');
      $('#dagId').addClass('is-invalid');
      $('#invalid-hint').text("DAG ID must be capitalized, without accents and separated by '_'. " + (validateName(dagId) == null ? '' : 'Suggestion for you: ' + validateName(dagId)));
      $('#dagId').focus()
      return false
    }
  }
  function validateCron() {
    try {
      cronExpression = $('#schedulerInput').val();
      if (cronExpression == "") {
        $('#schedulerInput').removeClass('is-invalid');
        $('#schedulerInput').addClass('is-valid');
        $('#error-message').hide()
        $('#result-cron').text('None')
        return true
      }
      $('#schedulerInput').removeClass('is-invalid');
      $('#schedulerInput').addClass('is-valid');
      $('#error-message').hide()
      $('#result-cron').text(cronstrue.toString(cronExpression));
      $('#result-cron').show()
      return true
    } catch (error) {
      $('#result-cron').hide()
      $('#schedulerInput').addClass('is-invalid');
      $('#error-message').text(error);
      $('#error-message').show();
      $('#schedulerInput').focus()
      console.error("An error occurred:", error);
      return false
    }
  }
});


function checkValid(name) {
  validName = validateName(name)
  return name == validName
}

function validateName(name) {
  // Remove accents from the name
  const nameWithoutAccents = name.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

  // Replace spaces and special characters with underscores
  const nameWithUnderscores = nameWithoutAccents.replace(/[^a-zA-Z0-9]/g, "_");

  // Capitalize all characters in the name
  const capitalizedName = nameWithUnderscores.toUpperCase();

  // Check if the name matches the desired pattern
  const namePattern = /^[A-Z][A-Z0-9_]*$/;
  const isValid = namePattern.test(capitalizedName);

  return isValid ? capitalizedName : null;
}

