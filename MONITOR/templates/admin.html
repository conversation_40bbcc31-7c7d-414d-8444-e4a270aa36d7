<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>DAG MONITORING</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static',filename='styles/style.css') }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
<!--    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>-->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"></script>
<!--    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"></script>-->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="{{ url_for('static', filename='admin-script.js') }}"></script>
    <script src="{{ url_for('static', filename='cronstrue.min.js') }}"></script>
  <!--embeded nav-->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js" integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js" integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF" crossorigin="anonymous"></script>
    <style type="text/css">
    .navbar {position: sticky;}
    .navbar .megamenu{ padding: 1rem;
         }
    /* ============ desktop view ============ */
    @media all and (min-width: 992px) {
        
        .navbar .has-megamenu{position:static!important;}
        .navbar .megamenu{left:0; right:0; width:80%; margin-top:0;  }
        
    }	
    /* ============ desktop view .end// ============ */
    </style>
    
    <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", function(){
            /////// Prevent closing from click inside dropdown
            document.querySelectorAll('.dropdown-menu').forEach(function(element){
                element.addEventListener('click', function (e) {
                    e.stopPropagation();
                });
            })
        }); 
        // DOMContentLoaded  end
    </script>
    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>

    <link href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

    <script>
        $(document).ready(function() {
            $('#example').DataTable({
                pageLength: 5
            });
            
        } );
    </script>
</head>

<body>
   <header>
    <nav class="navbar navbar-expand-lg " style="background-color: #00833E;position: fixed;
    top: 0;
    width: 100%;
    z-index: 10;" >
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin?token=admin_token">DAG MONITORING</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#main_nav"  aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="main_nav">
                <ul class="navbar-nav ms-auto">
                    {% for key, value in general_tree.items() %}
                    <!--<h1>{{general_tree|length}}</h1> -->
                    <li class="nav-item dropdown has-megamenu">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown"> {{key}}  </a>
                        <div class="dropdown-menu megamenu" role="menu">
                            <div class="row g-3">
                                {% for sub_key, sub_value in value.items() %}
                                <div class="col-lg-3 col-6">
                                    <div class="col-megamenu">
                                        <h6 class="title">{{sub_key}}</h6>
                                        <ul class="list-unstyled">
                                            {% for item in sub_value %}
                                            <li><a href="/getByTypeAndMainId?type={{key}}&main_dag_id=MAIN_{{sub_key}}_{{ item }}">{{ item }}</a></li>
                                            {% endfor %}
                                        </ul>
                                    </div>  <!-- col-megamenu.// -->
                                </div><!-- end col-3 -->
                                {% endfor %}
                            </div><!-- end row --> 
                        </div> <!-- dropdown-mega-menu.// -->
                    </li>
                    {% endfor %}    
                    
                </ul>
            </div> <!-- navbar-collapse.// -->
        </div> <!-- container-fluid.// -->
        </nav>
   </header>
    <div class="container">
            <section class="section-content py-5">
                <div class="table-responsive">
                    <div class="table-wrapper">
                        <table id="example" class="table table-striped" style="width:100%">

                            <thead>
                                <tr>
                                    <th class="fixed-cell">FLOW TYPE</th>
                                    <th class="fixed-cell">DAG ID</th>
                                    <th class="fixed-cell">STATUS</th>
                                    <th class="fixed-cell">REQUESTOR</th>
                                    <th class="fixed-cell">CREATED DATE</th>
                                    <th class="fixed-cell">REPORT FOLDER</th>
                                    <th class="fixed-cell">DEPARTMENT</th>
                                    <th class="fixed-cell">ROOT REPORT FOLDER</th>
                                    <th class="fixed-cell">ACTION</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dag in dags %}
                                <tr>
                                    <td>{{ dag.pattern_type }}</td>
                                    <td>{{ dag.dag_id }}</td>
                                    <td>{% if dag.status == 1 %}
                                        Active
                                      {% elif dag.status == 0 %}
                                        Deactive
                                      {% endif %}</td>
                                    <td>{{dag.requestor}}</td>
                                    <td>{{ dag.createdDt }}</td>
                                    <td>{{ dag.report_name }}</td>
                                    <td>{{ dag.department }}</td>
                                    <td>{{ dag.type }}</td>
                                    
                                    <td>
                                        <a href="#editEmployeeModal" class="edit" data-toggle="modal" data-row-id="{{ dag.dag_id }}"><i
                                            class="material-icons" data-toggle="tooltip" 
                                            title="Edit">&#xE254;</i></a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>       
                    </div>   
                </div>
            </section>
    </div>
    <!-- admin form -->
    <div id="editEmployeeModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" style="max-width: 90%; margin: 0 auto;">
            <input type="hidden" id="rowIdInput" value="">
            <div class="modal-content">
                <form id='update-form'>
                    <div class="modal-header">
                        <h4 class="modal-title">APPROVAL PIPELINE INFORMATION - FOR DATA ENGINEER</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    </div>
                    <div class="modal-body ">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Source connection ID</label>
                                    <input type="text" class="form-control" id="sourceConIDTxt" >
                                </div>
                                <div class="form-group">
                                    <label>DAG ID</label>
                                    <input type="text" class="form-control" id="dagId" >
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Destination connection ID</label>
                                    <input type="text" class="form-control" id="desConIDTxt" >
                                </div>
                                <div class="form-group">
                                    <label>Report folder name</label>
                                    <input type="text" class="form-control" id="fileName" >
                                </div>
                            </div>                                        
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Department folder</label>
                                    <input type="text" class="form-control" id="departmentFolderTxt" >
                                </div>
                                <div class="form-group">
                                    <label>Report folder name</label>
                                    <input type="text" class="form-control" id="reportName" required >
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Root sharepoint folder</label>
                                    <input type="text" class="form-control" id="reportFolderTxt" >
                                </div>
                                <div class="form-group">
                                    <label for="overwrite">Overwrite</label>
                                    <input type="text" class="form-control" id="overwrite" >
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Start date <i>(* Date format MM/DD/YYYY)</i> </label>
                                    <!---->
                                    <input type="date" class="form-control" name="startDate" id="startDate" >
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="schedulerInput">Scheduler Interval</label> <a href="https://crontab.guru/" target="_blank"> <i>Cron expression documentation</i></a>
	                                <input type="text" id="schedulerInput" name="schedulerInput" class="form-control">
                                    <div class="invalid-feedback" id="error-message"></div>
                                    <div class="form-text hint-text" id="result-cron"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Requestor</label>
                                    <!--note test -->
                                    <input type="text" class="form-control" id="requestor">
<!--                                    </textarea>-->
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Tags</label>
                                    <!---->
                                    <input type="text" class="form-control" id="tags" >
<!--                                    </textarea>-->
                                </div>
                            </div>
                           
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea class="form-control" id="description" ></textarea>
                                </div>
                            </div>
                           
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Status</label><span class="required-indicator">*</span>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="status-radio" value="0" id="deactive" checked>
                                        <label class="form-check-label" for="deactive">
                                            Deactive
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="status-radio" value="1" id="active">
                                        <label class="form-check-label" for="active">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-9" >
                                <div class="form-group">
                                    <label>Reason</label>
                                    <input type="text" class="form-control" id="reason-input">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Query & MetaData</label>
                                    <textarea class="form-control" required rows="20" id="query" required></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-group d-none">
                            <input type="text" class="form-control" id="patternTypeInput">
                          </div>
                    </div>
                    <div id='alertContainer'></div>
                    <div class="modal-footer">
                        <input type="button" class="btn btn-default" data-dismiss="modal" value="Cancel">
                        <input type="submit" class="btn btn-success" value="update">
                    </div>
                </form>
            </div>
        </div>
        <!-- Flexbox container for aligning the toasts -->
    </div>
    <!-- end admin form -->
</body>

</html>