<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>DAG MONITORING - ADD NEW PIPELINE</title>
<!--    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>-->
<!--    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"></script>-->
<!--    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>-->
<!--    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">-->
<!--    <script src="{{ url_for('static', filename='script.js') }}"></script>-->
<!--    <script src="{{ url_for('static', filename='cronstrue.min.js') }}"></script>-->
<!--    <link rel="stylesheet" type="text/css" href="{{ url_for('static',filename='styles/style.css') }}">-->
    <script src="{{ url_for('static', filename='jquery-3.5.1.min.js') }}"></script>
    <script src="{{ url_for('static', filename='bootstrap-4.5.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='jquery-1.11.1.min.js') }}"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script src="{{ url_for('static', filename='cronstrue.min.js') }}"></script>

    <link rel="stylesheet" type="text/css" href="{{ url_for('static',filename='styles/bootstrap-4.5.2.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static',filename='styles/style.css') }}">
</head>
    
<body>
    <div class="container-fluid">
        <div>
            <div style="position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 480px;
            max-width: 480px;
            border: 1px #EBEBEB solid;
            border-radius: 10px;
            box-shadow: 0px 0px 5px rgba(0,0,0, 0.05);
            padding: 20px 50px;
            background: #FFF;">
                <div style=" display: flex;
                justify-content: center;
                align-items: center;">
                 <svg data-v-0fdb4033="" width="100" height="42" viewBox="0 0 100 42" fill="none" xmlns="http://www.w3.org/2000/svg"><path data-v-0fdb4033="" d="M64.9247 20.3319V20.2313C67.8728 18.7165 69.2484 15.9933 69.2484 13.1718C69.2484 9.23921 66.2975 4.60066 57.9081 4.60066C50.9424 4.60066 46.1611 8.33243 46.1611 14.0297C46.1611 16.9004 47.23 19.5232 50.2303 21.1874L50.281 21.289C46.8251 22.7999 44.6377 25.3723 44.6377 29.3061C44.6377 33.5387 48.3495 38.4787 57.45 38.4787C65.6872 38.4787 70.7716 34.3964 70.7716 28.3456C70.7715 24.0112 67.7723 21.3882 64.9247 20.3319ZM57.5622 10.7574C59.8989 10.7574 60.7884 12.4099 60.7884 14.1729C60.7884 15.7858 59.7961 17.4547 58.4752 17.7583C56.2881 17.0519 53.8565 15.5512 53.8565 13.8366C53.8565 12.1218 55.3743 10.7574 57.5622 10.7574ZM57.8068 32.3303C55.5184 32.3303 53.8565 30.4422 53.8565 28.4239C53.8565 26.407 54.9515 24.4171 56.9359 23.9131C59.3249 24.5188 61.5523 26.5771 61.5523 28.9481C61.5523 30.8634 59.9409 32.3303 57.8068 32.3303Z" fill="#00833E"></path><path data-v-0fdb4033="" d="M73.8658 29.306C73.8658 33.5386 77.5785 38.4786 86.6798 38.4786C94.9156 38.4786 100 34.3963 100 28.3455C100 24.0112 97.0011 21.3882 94.1525 20.3319V20.2313C97.102 18.7165 98.478 15.9933 98.478 13.1718C98.478 9.23921 95.527 4.60066 87.1363 4.60066C80.1719 4.60066 75.3889 8.33243 75.3889 14.0297C75.3889 16.9004 76.4603 19.5232 79.4592 21.1874L79.511 21.289C76.0535 22.7998 73.8658 25.3723 73.8658 29.306ZM86.7896 10.7574C89.1292 10.7574 90.0166 12.4099 90.0166 14.1729C90.0166 15.7858 89.025 17.4547 87.7038 17.7583C85.5157 17.0519 83.0857 15.5512 83.0857 13.8366C83.0857 12.1218 84.605 10.7574 86.7896 10.7574ZM86.1638 23.9131C88.5534 24.5188 90.7829 26.5771 90.7829 28.9481C90.7829 30.8634 89.1712 32.3303 87.0356 32.3303C84.7476 32.3303 83.0857 30.4422 83.0857 28.4239C83.0857 26.407 84.1813 24.4171 86.1638 23.9131Z" fill="#00833E"></path><path data-v-0fdb4033="" d="M41.5587 20.7793C41.5587 9.30172 32.2555 0 20.7793 0C9.3041 0 0 9.30166 0 20.7793C0 32.2537 9.3041 41.5577 20.7793 41.5577C32.2555 41.5577 41.5587 32.2537 41.5587 20.7793ZM18.4709 30.7846C18.4709 35.0348 15.0253 38.4786 10.7756 38.4786V30.7846V12.3128C10.7756 8.06161 14.2223 4.61918 18.4709 4.61918H24.6273H24.6282H32.324C32.324 8.86608 28.8774 12.3128 24.6282 12.3128H18.4709V18.4709H23.0883H23.0896H30.7848C30.7848 22.7221 27.3393 26.1668 23.0896 26.1668H18.4709V30.7846Z" fill="#00833E"></path></svg>
                </div>
                 <a href="#addDagModal"  data-toggle="modal"><button style="display: inline-block;
                    position: relative;
                    width: 100%;
                    height: 80px;
                    line-height: 46px;
                    text-align: center;
                    border-radius: 5px;
                    font-size: 16px;
                    font-weight: 600;
                    border: 0;
                    box-shadow: 0px 2px 3px rgba(0,0,0, 0.05);
                    cursor: pointer;
                    margin-top: 20px;
                    background-color: #00833E;
                    " ><span style="color: #FFF;">ADD NEW PIPELINE</span></button></a>
        </div>
    </div>
    <div id="addDagModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" style="max-width: 90%; margin: 0 auto;">
            <div class="modal-content">
                <form id='myForm'>
                    <div class="modal-header">
                        <h4 class="modal-title">Add PIPELINE INFORMATION</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    </div>
                    <div class="modal-body ">
                        <div class="row">
                            <div class="col-md-3">
                              <div class="form-group">
                                <label for="typeSelect">Select a type</label><span class="required-indicator">*</span>
                                <select class="form-control" id="typeSelect" required>
                                    <option value="">Select a type...</option>
                                    <!-- <option value="DWH2SHAREPOINT">DWH2SHAREPOINT</option> -->
                                  {% for key in dag_pattern_type_list %}
                                  <option value="{{key}}">{{ key }}</option>
                                  {% endfor %}

                                </select>
                              </div>
                            </div>
                            <div class="col-md-3">
                              <div class="form-group" id="form-fre">
                                <label for="categorySelect">Select a frequency</label><span class="required-indicator">*</span>
                                <select class="form-control" id="categorySelect" required>
                                  <option value="">Select a frequency...</option>
                                  <!-- Add your options here -->
                                </select>
                              </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="departmentSelect">Select an department</label><span class="required-indicator">*</span>
                                    <select class="form-control" id="departmentSelect" required>
                                        <option value="">Select an department...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Sharepoint folder type</label><span class="required-indicator">*</span>
                                    <div class="form-check">
                                        <div class="form-check-inline">
                                            <input class="form-check-input" type="radio" name="folder-type-radio" value="daily" id="daily" checked>
                                            <label class="form-check-label" for="daily">
                                                Daily
                                            </label>
                                        </div>
                                        <div class="form-check-inline">
                                            <input class="form-check-input" type="radio" name="folder-type-radio" value="monthly" id="monthly">
                                            <label class="form-check-label" for="monthly">
                                                Monthly
                                            </label>
                                        </div>
                                        <div class="form-check-inline" id ="quarterly-report">
                                            <input class="form-check-input" type="radio" name="folder-type-radio" value="quarterly" id="quarterly">
                                            <label class="form-check-label" for="root">
                                                Quarterly
                                            </label>
                                        </div>
                                        <div class="form-check-inline">
                                            <input class="form-check-input" type="radio" name="folder-type-radio" value="root" id="root">
                                            <label class="form-check-label" for="root">
                                                Report folder
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Source connection ID</label>
                                    <input type="text" class="form-control" id="sourceConIDTxt" disabled=true>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Destination connection ID</label>
                                    <input type="text" class="form-control" id="desConIDTxt" disabled=true>
                                </div>
                            </div>  
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Root sharepoint folder</label>
                                    <input type="text" class="form-control" id="reportFolderTxt" disabled=true>
                                </div>
                            </div>                                      
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Department folder</label>
                                    <input type="text" class="form-control" id="departmentFolderTxt" disabled=true>
                                </div>
                            </div>
                            
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Requestor</label><span class="required-indicator">*</span>
                                    <input type="text" class="form-control" id="requestor" placeholder="Fill your username" required>
                                    <div class="hint" id="requestor-hint">Please fill your username match with email - <EMAIL></div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Report folder</label><span class="required-indicator">*</span>
                                    <input type="text" class="form-control" id="reportName" placeholder="Fill report folder name" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group" id='form-filename'>
                                    <label>Report name</label><span class="required-indicator">*</span>
                                    <input type="text" class="form-control" id="fileName" placeholder="Fill name of report file " required>
                                </div>
                            </div>
                           
                           
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group" id='form-overwrite'>
                                    <label for="slc-overwrite">Overwrite</label>
                                    <select class="form-control" id="slc-overwrite"  required>
                                        <option value=1>Overwrite existed report</option>
                                        <option value=0>Create another report</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="startDate">Start date</label><span class="required-indicator">*</span> <i class="required-indicator"> mm/dd/yyyy</i>
                                    <input type="date" class="form-control" name="startDate" id="startDate"  required>
                                  </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="schedulerInput">Scheduler Interval <i>(Optional)</i></label> <a href="https://crontab.guru/" target="_blank"> <i>Cron expression documentation</i></a>
	                                <input type="text" id="schedulerInput" name="schedulerInput" class="form-control">
                                    <div class="invalid-feedback" id="error-message"></div>
                                    <div class="form-text hint-text" id="result-cron"></div>
                                </div>
                            </div>
                           
                        </div> 

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>DAG ID</label><span class="required-indicator">*</span>
                                    <input type="text" class="form-control" id="dagId"placeholder="Fill dag id (DAG ID must be capitalized, without accents and separated by '_') " required>
                                    <div class="invalid-feedback" id="invalid-hint">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Description</label><span class="required-indicator">*</span>
                                    <textarea class="form-control"  rows="5" id="description" placeholder="Fill description" required></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label id="lbl-query">Query</label><span class="required-indicator">*</span>
                                    <textarea class="form-control" required rows="20" id="query" placeholder="Fill query to get data from DWH" required></textarea>
                                </div>     
                            </div>
                        </div>
                    </div>
                    <div id='alertContainer'></div>
                    <div class="modal-footer">
                        <input type="button" class="btn btn-default" data-dismiss="modal" value="Cancel">
                        <input type="submit" class="btn btn-success" value="Add">
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>

</html>

