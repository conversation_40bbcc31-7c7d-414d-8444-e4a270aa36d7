import os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.hooks.oracle import OracleHook
from DAGs.External2DWH.API.BaseAPI.HR.utils import load_to_stg, get_opening, transform_opening
from DAGs.utils import merge_data_stag_to_dwh


description = 'Get HRM opening data from BASE API to DWH'
tags = ['hrm', 'api', 'baseapi']
oracle_conn_id = 'oracle_f88_dwh'
schedule_interval = None
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
DAG_ID = "opening_base_dag"

parent_dir = os.path.dirname(os.path.abspath(__file__))
stg_table = ['STGPROD.BASE_OPENING']
proc_stg_to_dwh = 'F88DWH.PROC_BASE_OPENING_W'

# stg_table = ['BASE_OPENING']


def truncate_stg_opening_tbl():
    logging.info('Truncate opening staging table begin!')
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    sql = f"truncate table {stg_table[0]}"
    hook.run(sql, autocommit=True)
    logging.info(f'delete {stg_table[0]} Done!')
    logging.info('Truncate opening staging table - Done!')


def opening_ingestion(**kwargs):
    conf = kwargs['dag_run'].conf
    executed_start_date = conf['start_date']
    executed_end_date = conf['end_date']
    logging.info(f'Start opening ------->')
    logging.info(f'Start date opening: {executed_start_date}')
    logging.info(f'End date opening: {executed_end_date}')
    extract_opening = get_opening(executed_start_date, executed_end_date)
    logging.info(f"Number of opening: {len(extract_opening)}")
    transform_dict = transform_opening(extract_opening)
    load_to_stg(transform_dict, stg_table[0])


with DAG(dag_id=DAG_ID, start_date=datetime(2023, 9, 1), schedule_interval=schedule_interval,
         default_args=default_args, catchup=False, tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    truncate_stg_opening_tbl = PythonOperator(task_id='truncate_stg_opening_tbl',
                                              python_callable=truncate_stg_opening_tbl,
                                              provide_context=True)

    opening_ingestion = PythonOperator(task_id='opening_ingestion',
                                       python_callable=opening_ingestion,
                                       provide_context=True)
    merge_stg_to_opening_fact = PythonOperator(task_id='merge_stg_to_opening_fact',
                                                 python_callable=merge_data_stag_to_dwh,
                                                 op_kwargs={'conn_id': oracle_conn_id, 'proc_name': proc_stg_to_dwh},
                                                 provide_context=True)
    end = EmptyOperator(task_id='End')

(start
 >> truncate_stg_opening_tbl
 >> opening_ingestion
 >> merge_stg_to_opening_fact
 >> end)
