CREATE OR REPLACE PROCEDURE F88DWH.PROC_BASE_CANDIDATE_W AS
BEGIN
/*
  -- Author  : QUANNV2
  -- Created : 29/03/2024
  -- Purpose : Merge staging -> DWH for HRM Base candidate
*/
MERGE INTO F88DWH.w_base_candidate_f w
    USING STGPROD.base_candidate s ON (s.candidate_id = w.candidate_id)
    WHEN NOT MATCHED THEN
        INSERT (candidate_id,
                candidate_name,
                email,
                status,
                prefix,
                phone,
                dob,
                gender,
                ssn,
                address,
                opening_id,
                stage_id,
                stage_name,
                last_time_stage,
                source,
                source_id,
                create_dt,
                update_dt,
                metatype,
                assign_username,
                interview_count,
                last_interview,
                last_stage_before_fail,
                reason,
                reason_id,
                apply_date,
                offered_date,
                hired_date,
                rejected_date,
                time_campaign_qualified,
                owners,
                creator,
                title,
                load_dt,
                area_applied,
                office_applied)
            VALUES (TO_NUMBER(s.candidate_id),
                    s.candidate_name,
                    s.email,
                    s.status,
                    s.prefix,
                    s.phone,
                    CASE
                        WHEN REGEXP_LIKE(s.dob,
                                         '^\d{2}/\d{2}/\d{4}$') THEN
                            TO_DATE(s.dob, 'DD/MM/YYYY')
                        WHEN REGEXP_LIKE(s.dob,
                                         '^\d{2}/\d{2}/\d{2}$') THEN
                            TO_DATE(s.dob, 'DD/MM/YY')
                        ELSE
                            NULL
                        END,
                    s.gender,
                    s.ssn,
                    s.address,
                    TO_NUMBER(s.opening_id),
                    TO_NUMBER(s.stage_id),
                    s.stage_name,
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.last_time_stage / 86400),
                    s.source,
                    TO_NUMBER(s.source_id),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.create_dt / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.update_dt / 86400),
                    s.metatype,
                    s.assign_username,
                    TO_NUMBER(s.interview_count),
                    TO_NUMBER(s.last_interview),
                    s.last_stage_before_fail,
                    s.reason,
                    TO_NUMBER(s.reason_id),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.apply_date / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.offered_date / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.hired_date / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.rejected_date / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.time_campaign_qualified / 86400),
                    s.owners,
                    s.creator,
                    s.title,
                    SYSDATE,
                    s.area_applied,
                    s.office_applied)
    WHEN MATCHED THEN
        UPDATE SET
            w.candidate_name = s.candidate_name,
            w.email = s.email,
            w.status = s.status,
            w.prefix = s.prefix,
            w.phone = s.phone,
            w.dob = CASE
                        WHEN REGEXP_LIKE(s.dob,
                                         '^\d{2}/\d{2}/\d{4}$') THEN
                            TO_DATE(s.dob, 'DD/MM/YYYY')
                        WHEN REGEXP_LIKE(s.dob,
                                         '^\d{2}/\d{2}/\d{2}$') THEN
                            TO_DATE(s.dob, 'DD/MM/YY')
                        ELSE
                            NULL
                END
            ,
            w.gender = s.gender,
            w.ssn = s.ssn,
            w.address = s.address,
            w.opening_id = TO_NUMBER(s.opening_id),
            w.stage_id = TO_NUMBER(s.stage_id),
            w.stage_name = s.stage_name,
            w.last_time_stage = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.last_time_stage / 86400),
            w.source = s.source,
            w.source_id = TO_NUMBER(s.source_id),
            w.create_dt = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.create_dt / 86400),
            w.update_dt = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.update_dt / 86400),
            w.metatype = s.metatype,
            w.assign_username = s.assign_username,
            w.interview_count = TO_NUMBER(s.interview_count),
            w.last_interview = TO_NUMBER(s.last_interview),
            w.last_stage_before_fail = s.last_stage_before_fail,
            w.reason = s.reason,
            w.reason_id = TO_NUMBER(s.reason_id),
            w.apply_date = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.apply_date / 86400),
            w.offered_date = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.offered_date / 86400),
            w.hired_date = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.hired_date / 86400),
            w.rejected_date = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.rejected_date / 86400),
            w.time_campaign_qualified = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.time_campaign_qualified / 86400),
            w.owners = s.owners,
            w.creator = s.creator,
            w.title = s.title,
            w.load_dt = SYSDATE,
            w.area_applied = s.area_applied,
            w.office_applied = s.office_applied;

MERGE INTO F88DWH.w_base_candidate_changelogs_f w
    USING STGPROD.base_candidate_changelogs s ON (s.id = w.id)
    WHEN NOT MATCHED THEN
        INSERT (id,
                candidate_id,
                opening_id,
                created_dt,
                stage_in,
                stage_in_name,
                stage_in_state,
                stage_out,
                stage_out_name,
                stage_out_state,
                changelog_time,
                username,
                note,
                time,
                load_dt)
            VALUES (s.id,
                    TO_NUMBER(s.candidate_id),
                    TO_NUMBER(s.opening_id),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.created_dt / 86400),
                    TO_NUMBER(s.stage_in),
                    s.stage_in_name,
                    s.stage_in_state,
                    TO_NUMBER(s.stage_out),
                    s.stage_out_name,
                    s.stage_out_state,
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.changelog_time / 86400),
                    s.username,
                    s.note,
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.time / 86400),
                    SYSDATE)
    WHEN MATCHED THEN
        UPDATE SET
            w.candidate_id = TO_NUMBER(s.candidate_id),
            w.opening_id = TO_NUMBER(s.opening_id),
            w.created_dt = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.created_dt / 86400),
            w.stage_in = TO_NUMBER(s.stage_in),
            w.stage_in_name = s.stage_in_name,
            w.stage_in_state = s.stage_in_state,
            w.stage_out = TO_NUMBER(s.stage_out),
            w.stage_out_name = s.stage_out_name,
            w.stage_out_state = s.stage_out_state,
            w.changelog_time = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.changelog_time / 86400),
            w.username = s.username,
            w.note = s.note,
            w.time = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.time / 86400),
            w.load_dt = SYSDATE ;


END;
