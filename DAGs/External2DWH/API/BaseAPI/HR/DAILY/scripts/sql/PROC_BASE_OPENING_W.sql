CREATE OR R<PERSON>LACE PROCEDURE F88DWH.PROC_BASE_OPENING_W AS
BEGIN
/*
  -- Author  : QUANNV2
  -- Created : 29/03/2024
  -- Purpose : Merge staging -> DWH for HRM Base openings
*/
MERGE INTO F88DWH.W_BASE_OPENING_F w
    USING STGPROD.BASE_OPENING s ON (w.OPENING_ID = s.OPENING_ID AND
    (w.num_positions = s.num_positions AND
    w.START_DATE = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.START_DATE / 86400) AND
    w.END_DATE = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.END_DATE / 86400) AND
    w.DEADLINE = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.DEADLINE / 86400) AND
    w.OWNERS = s.owners
    ))
    WHEN NOT MATCHED THEN
        INSERT (
                opening_id,
                position_id,
                position_name,
                starred,
                dept_id,
                status,
                metatype,
                num_positions,
                offices,
                talent_pool_id,
                deadline,
                start_date,
                end_date,
                create_dt,
                update_dt,
                load_dt,
                owners,
                creator
            )
            VALUES (TO_NUMBER(s.opening_id),
                    s.position_id,
                    s.position_name,
                    TO_NUMBER(s.starred),
                    TO_NUMBER(s.dept_id),
                    TO_NUMBER(s.status),
                    s.metatype,
                    TO_NUMBER(s.num_positions),
                    s.offices,
                    TO_NUMBER(s.talent_pool_id),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.deadline / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.start_date / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.end_date / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.create_dt / 86400),
                    TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.update_dt / 86400),
                    SYSDATE,
                    s.owners,
                    s.creator)
   WHEN MATCHED THEN
        UPDATE SET
            w.position_id = s.position_id,
            w.position_name = s.position_name,
            w.starred = TO_NUMBER(s.starred),
            w.dept_id = TO_NUMBER(s.dept_id),
            w.status = TO_NUMBER(s.status),
            w.metatype = s.metatype,
            w.offices = s.offices,
            w.talent_pool_id = TO_NUMBER(s.talent_pool_id),
--            w.deadline = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.deadline / 86400),
--            w.start_date = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.start_date / 86400),
--            w.end_date = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.end_date / 86400),
            w.create_dt = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.create_dt / 86400),
            w.update_dt = TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.update_dt / 86400),
            w.load_dt = SYSDATE,
--            w.owners = s.owners,
            w.creator = s.creator;

END;
