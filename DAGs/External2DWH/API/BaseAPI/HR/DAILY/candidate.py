import logging
import os
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.hooks.oracle import OracleHook
from DAGs.External2DWH.API.BaseAPI.HR.utils import load_to_stg, get_candidate, get_change_logs, transform_candidate, \
    transform_changelog
from DAGs.utils import merge_data_stag_to_dwh

description = 'Get HRM candidate data from BASE API to DWH'
tags = ['hrm', 'api', 'baseapi']
oracle_conn_id = 'oracle_f88_dwh'
proc_stg_to_dwh = 'F88DWH.PROC_BASE_CANDIDATE_W'
schedule_interval = None
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
DAG_ID = "candidate_base_dag"
parent_dir = os.path.dirname(os.path.abspath(__file__))

stg_candidate = ['STGPROD.BASE_CANDIDATE', 'STGPROD.BASE_CANDIDATE_CHANGELOGS', 'F88DWH.W_BASE_OPENING_F']


def truncate_stg_candidate_tbl():
    logging.info('Truncate staging candidate table begin!')
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    for i in [0, 1]:
        sql = f"truncate table {stg_candidate[i]}"
        hook.run(sql, autocommit=True)
        logging.info(f'Truncate {stg_candidate[i]} Done!')
    logging.info('Truncate staging candidate table - Done!')


def candidate_ingestion(**kwargs):
    conf = kwargs['dag_run'].conf
    executed_start_date = conf['start_date']
    executed_end_date = conf['end_date']
    logging.info(f'Start date candidate: {executed_start_date}')
    logging.info(f'End date candidate: {executed_end_date}')
    opening_ids = get_opening_id(end_date=executed_end_date)
    candidate_dict = get_candidate(opening_ids, start_date=executed_start_date.strip(),
                                   end_date=executed_end_date.strip())
    # candidate_dict = get_candidate(opening_ids, start_date='',end_date=executed_end_date.strip())
    changelog_dict = get_change_logs(candidate_dict)

    transformed_candidate = transform_candidate(candidate_dict)
    transformed_changelog = transform_changelog(changelog_dict)

    load_to_stg(transformed_candidate, stg_candidate[0])
    load_to_stg(transformed_changelog, stg_candidate[1])


def get_opening_id(end_date):
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    sql = f"SELECT DISTINCT TO_NUMBER(OPENING_ID) OPENING_ID FROM {stg_candidate[2]} WHERE STATUS  IN (-1,7,10) AND DEADLINE  >= TO_DATE('{end_date}','yyyy-mm-dd')"
    logging.info(f"sql: {sql}")
    df_ora = hook.get_pandas_df(sql=sql)
    logging.info(f"data: {df_ora}")
    return df_ora['OPENING_ID'].values.tolist()


with DAG(dag_id=DAG_ID, start_date=datetime(2023, 1, 1),
         schedule_interval=schedule_interval, default_args=default_args, catchup=False, tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    truncate_stg_candidate_tbl = PythonOperator(task_id='truncate_stg_candidate_tbl',
                                                python_callable=truncate_stg_candidate_tbl,
                                                provide_context=True)
    candidate_ingestion = PythonOperator(task_id='candidate_ingestion',
                                         python_callable=candidate_ingestion,
                                         provide_context=True)
    merge_stg_to_candidate_fact = PythonOperator(task_id='merge_stg_to_candidate_fact',
                                                 python_callable=merge_data_stag_to_dwh,
                                                 op_kwargs={'conn_id': oracle_conn_id, 'proc_name': proc_stg_to_dwh},
                                                 provide_context=True)
    end = EmptyOperator(task_id='End')

(start
 >> truncate_stg_candidate_tbl
 >> candidate_ingestion
 >> merge_stg_to_candidate_fact
 >> end)
