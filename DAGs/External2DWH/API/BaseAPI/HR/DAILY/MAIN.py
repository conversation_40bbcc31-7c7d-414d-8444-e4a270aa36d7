from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
import logging
from datetime import date
import pendulum

description = 'Get HRM data from BASE API to DWH'
tags = ['hrm', 'daily', 'api', 'baseapi']
oracle_conn_id = 'oracle_f88_dwh'
schedule_interval = '0 1 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
DAG_ID = "hrm_base_to_dwh"

def validate_params(**kwargs):
    start_date_cfg = kwargs['dag_run'].conf.get('start_date')
    end_date_cfg = kwargs['dag_run'].conf.get('end_date')
    executed_start_date = ""
    executed_end_date = ""
    if start_date_cfg or end_date_cfg:
        logging.info("Trigger by config...")
        if start_date_cfg:
            executed_start_date = start_date_cfg
        if end_date_cfg:
            executed_end_date = end_date_cfg
    else:
        logging.info("Performing incremental load...")
        tz = pendulum.timezone('Asia/Ho_Chi_Minh')
        today = datetime.today().astimezone(tz)
        end_date = today - timedelta(days=1)
        logging.info(f'End date include timestamp : {end_date}')
        executed_end_date = end_date.strftime("%Y-%m-%d")
        executed_start_date = ''
    logging.info(f'Start date : {executed_start_date}')
    logging.info(f'End date : {executed_end_date}')
    return executed_start_date, executed_end_date


with DAG(dag_id=DAG_ID, start_date=datetime(2023, 9, 1),
         schedule_interval=schedule_interval, default_args=default_args, catchup=False, tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    validate_params = PythonOperator(task_id='validate_params',
                                     python_callable=validate_params,
                                     provide_context=True)
    # load_opening
    ingest_opening = TriggerDagRunOperator(task_id="ingest_opening", trigger_dag_id="opening_base_dag",
                                           conf={
                                               "start_date": "{{ task_instance.xcom_pull(task_ids='validate_params')[0] }}",
                                               "end_date": "{{ task_instance.xcom_pull(task_ids='validate_params')[1] }}"},
                                           wait_for_completion=True, poke_interval=5)

    # load_candidate
    ingest_candidate = TriggerDagRunOperator(task_id="ingest_candidate", trigger_dag_id="candidate_base_dag",
                                             conf={
                                                 "start_date": "{{ task_instance.xcom_pull(task_ids='validate_params')[0] }}",
                                                 "end_date": "{{ task_instance.xcom_pull(task_ids='validate_params')[1] }}"},
                                             wait_for_completion=True, poke_interval=5)
    end = EmptyOperator(task_id='End')

(start
 >> validate_params
 >> ingest_opening
 >> ingest_candidate
 >> end)


