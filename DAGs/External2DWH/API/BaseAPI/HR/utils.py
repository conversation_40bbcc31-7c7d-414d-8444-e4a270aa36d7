from airflow.providers.oracle.hooks.oracle import OracleHook
import logging
import requests
import math
import pandas as pd
from airflow.models import Variable

oracle_conn_id = 'oracle_f88_dwh'
BASE_INFO = Variable.get("BASE_INFO", deserialize_json=True)


def get_opening(start_date, end_date):
    page = 1
    num_per_page = 6000
    openings = []
    num_page = 0
    response = call_fetch_opening_all(page, num_per_page, start_date, end_date)
    if response.status_code == 200:
        data = response.json()
        if 'total' not in data or data['total'] == "0":
            return openings
        else:
            total = int(data['total'])
            num_page = math.ceil(total / num_per_page)
            openings.extend(response.json()["openings"])
            page += 1
    else:
        logging.info(f'API request failed with status code: {response.status_code}')

    # Check if there are more pages to fetch
    while page <= num_page:
        response = call_fetch_opening_all(page, num_per_page, start_date, end_date)
        openings.extend(response.json()["openings"])
        page += 1
    for opening in openings:
        opening_dtl = get_detail_opening(opening['id'])
        opening['owners'] = opening_dtl['owners']
        opening['creator'] = opening_dtl['username']
    return openings


def get_detail_opening(opening_id):
    response = call_dtl_opening(opening_id)
    if response.status_code == 200:
        return response.json()['opening']
    else:
        logging.info(f'API request failed with status code: {response.status_code}')
    return {}


def call_dtl_opening(opening_id):
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    # Request payload
    payload = {
        'access_token': BASE_INFO["access_token"],
        'id': opening_id,

    }
    return requests.post(BASE_INFO["url_opening_detail"], headers=headers, data=payload)


def get_candidate(opening_ids, start_date, end_date):
    candidates = []
    logging.info(f"Number of jobs: {len(opening_ids)}")
    # logging.info(f"Start_date: {start_date}")
    # logging.info(f"End_date: {end_date}")
    for opening_id in opening_ids:
        page = 1
        num_per_page = 100
        num_page = 0
        response = call_fetch_candidate(page, opening_id, num_per_page, start_date, end_date)
        if response.status_code == 200:
            data = response.json()
            if 'total' not in data or data['total'] == "0":
                continue
            else:
                total = int(data['total'])
                num_page = math.ceil(total / num_per_page)
                candidates.extend(response.json()["candidates"])
                page += 1
        else:
            logging.info(f'API request failed with status code: {response.status_code}')

        # Check if there are more pages to fetch
        while page <= num_page:
            response = call_fetch_candidate(page, opening_id, num_per_page, start_date, end_date)
            candidates.extend(response.json()["candidates"])
            page += 1
    return candidates


def get_change_logs(candidates):
    change_logs = []
    for candidate in candidates:
        raw_changelogs = candidate["changelogs"]
        for log in raw_changelogs:
            log["candidate_id"] = candidate["id"]
        change_logs.extend(raw_changelogs)

    return change_logs


def call_fetch_opening_all(page, num_per_page, start_date, end_date):
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    # Request payload
    payload = {
        'access_token': BASE_INFO["access_token"],
        'page': page,
        'num_per_page': num_per_page,
        'start_date': start_date,
        'end_date': end_date,
    }
    return requests.post(BASE_INFO['opening_all_url'], headers=headers, data=payload)


def call_fetch_candidate(page, opening_id, num_per_page, start_date, end_date):
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    # Request payload
    payload = {
        'access_token': BASE_INFO['access_token'],
        'page': page,
        'num_per_page': num_per_page,
        'opening_id': opening_id,
        # 'start_date': start_date,
        # 'end_date': end_date
    }
    return requests.post(BASE_INFO['candidate_by_opening_id_url'], headers=headers, data=payload)


def load_to_stg(df: pd.DataFrame, table_name):
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    if not df.empty:
        logging.info(f'Insert into table : {table_name}')
        logging.info(f'Number data : {len(df)}')
        rows = df.itertuples(index=False)
        hook.bulk_insert_rows(table=table_name, target_fields=df.columns.tolist(), rows=rows)


def merge_data_stag_to_dwh():
    logging.info(f'MERGE_STG_2_DWH begin!')
    logging.info(f'EXECUTE PROD_BASE_W')
    sql_merge = "BEGIN F88DWH.PROC_BASE_W; END;"
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    hook.run(sql_merge, autocommit=True)
    logging.info(f'MERGE_STG_2_DWH Done!')


# transform opening
def transform_opening(openings: list):
    df = pd.DataFrame(openings)
    df_transform = pd.DataFrame()
    if not df.empty:
        df_transform['OPENING_ID'] = '' if df.get('id') is None else df.get('id').apply(validate_str_value)
        df_transform['POSITION_ID'] = '' if df.get('codename') is None else df.get('codename').apply(
            validate_str_value)
        df_transform['POSITION_NAME'] = '' if df.get('name') is None else df.get('name').apply(validate_str_value)
        df_transform['STARRED'] = '' if df.get('starred') is None else df.get('starred').apply(validate_str_value)
        df_transform['DEPT_ID'] = '' if df.get('dept_id') is None else df.get('dept_id').apply(validate_str_value)
        df_transform['STATUS'] = '' if df.get('status') is None else df.get('status').apply(validate_str_value)
        df_transform['METATYPE'] = '' if df.get('metatype') is None else df.get('metatype').apply(validate_str_value)
        df_transform['NUM_POSITIONS'] = '' if df.get('num_positions') is None else df.get('num_positions').apply(
            validate_str_value)
        df_transform['OFFICES'] = '' if df.get('offices') is None else df['offices'].apply(
            lambda x: ','.join(map(str, x)) if isinstance(x, list) else x)
        df_transform['TALENT_POOL_ID'] = '' if df.get('talent_pool_id') is None else df.get('talent_pool_id').apply(
            validate_str_value)
        df_transform['DEADLINE'] = '' if df.get('deadline') is None else df.get('deadline').apply(validate_str_value)
        df_transform['START_DATE'] = '' if df.get('stime') is None else df.get('stime').apply(validate_str_value)
        df_transform['END_DATE'] = '' if df.get('etime') is None else df.get('etime').apply(validate_str_value)
        df_transform['CREATE_DT'] = '' if df.get('since') is None else df.get('since').apply(validate_str_value)
        df_transform['UPDATE_DT'] = '' if df.get('last_update') is None else df.get('last_update').apply(
            validate_str_value)
        if df.get('owners') is None:
            df_transform['owners'] = ''
        else:
            df_transform['owners'] = df['owners'].apply(lambda x: ','.join(x))

        if df.get('creator') is None:
            df_transform['creator'] = ''
        else:
            df_transform['creator'] = df['creator']
    return df_transform


def transform_candidate(candidates: list):
    df = pd.DataFrame(candidates)
    df_transform = pd.DataFrame()
    if not df.empty:
        df_transform['CANDIDATE_ID'] = '' if df.get('id') is None else df.get('id').apply(validate_str_value)
        df_transform['CANDIDATE_NAME'] = '' if df.get('name') is None else df.get('name').apply(validate_str_value)
        df_transform['EMAIL'] = '' if df.get('email') is None else df.get('email').apply(validate_str_value)
        df_transform['STATUS'] = '' if df.get('status') is None else df.get('status').apply(validate_str_value)
        df_transform['PREFIX'] = '' if df.get('prefix') is None else df.get('prefix').apply(validate_str_value)
        df_transform['PHONE'] = '' if df.get('phone') is None else df.get('phone').apply(validate_str_value)
        day = '' if df.get('dob_day') is None else df.get('dob_day')
        month = '' if df.get('dob_month') is None else df.get('dob_month')
        year = '' if df.get('dob_year') is None else df.get('dob_year')
        df_transform['DOB'] = day + '/' + month + '/' + year
        df_transform['GENDER'] = '' if df.get('gender') is None else df.get('gender').apply(validate_str_value)
        df_transform['SSN'] = '' if df.get('ssn') is None else df.get('ssn').apply(validate_str_value)
        df_transform['ADDRESS'] = '' if df.get('address') is None else df.get('address').apply(validate_str_value)
        df_transform['OPENING_ID'] = '' if df.get('opening_id') is None else df.get('opening_id').apply(
            validate_str_value)
        df_transform['STAGE_ID'] = '' if df.get('stage_id') is None else df.get('stage_id').apply(validate_str_value)
        df_transform['STAGE_NAME'] = '' if df.get('stage_name') is None else df.get('stage_name').apply(
            validate_str_value)
        df_transform['LAST_TIME_STAGE'] = '' if df.get('last_time_stage') is None else df.get('last_time_stage').apply(
            validate_str_value)
        df_transform['SOURCE'] = '' if df.get('source') is None else df.get('source').apply(validate_str_value)
        df_transform['SOURCE_ID'] = '' if df.get('source_id') is None else df.get('source_id').apply(validate_str_value)
        df_transform['CREATE_DT'] = '' if df.get('since') is None else df.get('since').apply(validate_str_value)
        df_transform['UPDATE_DT'] = '' if df.get('last_update') is None else df.get('last_update').apply(
            validate_str_value)
        df_transform['METATYPE'] = '' if df.get('metatype') is None else df.get('metatype').apply(validate_str_value)
        df_transform['ASSIGN_USERNAME'] = '' if df.get('assign_username') is None else df.get('assign_username').apply(
            validate_str_value)
        df_transform['INTERVIEW_COUNT'] = '' if df.get('interview_count') is None else df.get('interview_count').apply(
            validate_str_value)
        df_transform['LAST_INTERVIEW'] = '' if df.get('last_interview') is None else df.get('last_interview').apply(
            validate_str_value)
        df_transform['LAST_STAGE_BEFORE_FAIL'] = '' if df.get('last_stage_before_failed') is None else df.get(
            'last_stage_before_failed').apply(validate_str_value)
        df_transform['REASON'] = '' if df.get('reason') is None else df.get('reason').apply(validate_str_value)
        df_transform['REASON_ID'] = '' if df.get('reason_id') is None else df.get('reason_id').apply(validate_str_value)
        df_transform['APPLY_DATE'] = '' if df.get('time_apply') is None else df.get('time_apply').apply(
            validate_str_value)
        df_transform['OFFERED_DATE'] = '' if df.get('time_offered') is None else df.get('time_offered').apply(
            validate_str_value)
        df_transform['HIRED_DATE'] = '' if df.get('time_hired') is None else df.get('time_hired').apply(
            validate_str_value)
        df_transform['REJECTED_DATE'] = '' if df.get('time_rejected') is None else df.get('time_rejected').apply(
            validate_str_value)
        df_transform['TIME_CAMPAIGN_QUALIFIED'] = '' if df.get('time_campaign_qualified') is None else df.get(
            'time_campaign_qualified').apply(validate_str_value)
        if df.get('opening_export') is None:
            df_transform['owners'] = ''
            df_transform['creator'] = ''
        else:
            df_transform['creator'] = df['opening_export'].apply(
                lambda x: x.get('creator') if isinstance(x, dict) else '')
            df_transform['owners'] = df['opening_export'].apply(
                lambda x: ','.join(x.get('owners')) if isinstance(x, dict) and 'owners' in x else '')
        df_transform['title'] = '' if df.get('title') is None else df.get('title').apply(validate_str_value)
        df_transform['area_applied'] = df['form'].apply(
            lambda x: [i.get('value') for i in x if i.get('id') == 'khu_vuc_du_tuyen'])
        df_transform = df_transform.explode('area_applied').fillna('')
        df_transform['OFFICE_APPLIED'] = '' if df.get('tags') is None else df['tags'].apply(
            lambda x: ','.join(map(str, x)) if isinstance(x, list) else x)
    
    return df_transform


def transform_changelog(changelogs: list):
    df = pd.DataFrame(changelogs)
    df_transform = pd.DataFrame()
    if not df.empty:
        df_transform['ID'] = '' if df.get('id') is None else df.get('id').apply(validate_str_value)
        df_transform['CANDIDATE_ID'] = '' if df.get('candidate_id') is None else df.get('candidate_id').apply(
            validate_str_value)
        df_transform['CREATED_DT'] = '' if df.get('since') is None else df.get('since').apply(validate_str_value)
        if df.get('data') is None:
            df_transform['OPENING_ID'] = ''
            df_transform['STAGE_IN'] = ''
            df_transform['STAGE_IN_NAME'] = ''
            df_transform['STAGE_IN_STATE'] = ''
            df_transform['STAGE_OUT'] = ''
            df_transform['STAGE_OUT_NAME'] = ''
            df_transform['STAGE_OUT_STATE'] = ''
            df_transform['CHANGELOG_TIME'] = ''
        else:
            df_transform['OPENING_ID'] = df['data'].apply(
                lambda x: x.get('opening_id') if isinstance(x, dict) else '')
            df_transform['STAGE_IN'] = df['data'].apply(
                lambda x: x.get('stage_in') if isinstance(x, dict) else '')
            df_transform['STAGE_IN_NAME'] = df['data'].apply(
                lambda x: x.get('stage_in_name') if isinstance(x, dict) else '')
            df_transform['STAGE_IN_STATE'] = df['data'].apply(
                lambda x: x.get('stage_in_state') if isinstance(x, dict) else '')
            df_transform['STAGE_OUT'] = df['data'].apply(
                lambda x: x.get('stage_out') if isinstance(x, dict) else '')
            df_transform['STAGE_OUT_NAME'] = df['data'].apply(
                lambda x: x.get('stage_out_name') if isinstance(x, dict) else '')
            df_transform['STAGE_OUT_STATE'] = df['data'].apply(
                lambda x: x.get('stage_out_state') if isinstance(x, dict) else '')
            df_transform['CHANGELOG_TIME'] = df['data'].apply(
                lambda x: x.get('time') if isinstance(x, dict) else '')

        df_transform['USERNAME'] = '' if df.get('username') is None else df.get('username').apply(validate_str_value)
        df_transform['NOTE'] = '' if df.get('note') is None else df.get('note').apply(validate_str_value)
        df_transform['TIME'] = '' if df.get('time') is None else df.get('time').apply(validate_str_value)
    return df_transform


def call_fetch_office_all():
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    # Request payload
    payload = {
        'access_token': BASE_INFO["access_token"],
    }
    response = requests.post(BASE_INFO["office_all_url"], headers=headers, data=payload)

    try:
        data = response.json()["depts"]
        return data
    except Exception as e:
        logging.info(e)
        return []


def transform_office(offices: list):
    df_offices = pd.DataFrame(offices)
    df_transform = pd.DataFrame()
    if not df_offices.empty:
        df_transform['ID'] = '' if df_offices.get('id') is None else df_offices.get('id')
        df_transform['NAME'] = '' if df_offices.get('name') is None else df_offices.get('name')
        df_transform['ADDRESS'] = '' if df_offices.get('address') is None else df_offices.get('address')
        # df_transform['OFFICE_TOKEN'] = '' if df_offices.get('token') is None else df_offices.get('token')
    return df_transform

def call_fetch_department_all():
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    # Request payload
    payload = {
        'access_token': BASE_INFO["access_token"],
    }
    response = requests.post(BASE_INFO["department_all_url"], headers=headers, data=payload)

    try:
        data = response.json()["depts"]
        return data
    except Exception as e:
        logging.info(e)
        return []


def transform_department(offices: list):
    df_department = pd.DataFrame(offices)
    df_transform = pd.DataFrame()
    if not df_department.empty:
        df_transform['ID'] = '' if df_department.get('id') is None else df_department.get('id')
        df_transform['NAME'] = '' if df_department.get('name') is None else df_department.get('name')
        df_transform['content'] = '' if df_department.get('content') is None else df_department.get('content')
    return df_transform

def validate_str_value(value):
    try:
        return str(value)
    except (TypeError, ValueError):
        return ''

