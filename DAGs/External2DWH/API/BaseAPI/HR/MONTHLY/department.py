import os
import logging
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.hooks.oracle import OracleHook
from DAGs.External2DWH.API.BaseAPI.HR.utils import load_to_stg, call_fetch_department_all, transform_department

description = 'Get HR departments data from BASE API to DWH'
tags = ['hrm', 'api', 'baseapi']
oracle_conn_id = 'oracle_f88_dwh'
department = 'Project - HR'
# schedule_interval = '@monthly'
schedule_interval = '0 0 1 * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
parent_dir = os.path.dirname(os.path.abspath(__file__))
stg_table = ['STGPROD.BASE_DEPARTMENT']


def truncate_stg_department_tbl():
    logging.info('Truncate department staging table begin!')
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    sql = f"truncate table {stg_table[0]}"
    hook.run(sql, autocommit=True)
    logging.info(f'delete {stg_table[0]} Done!')
    logging.info('Truncate department staging table - Done!')


def department_ingestion(**kwargs):
    extract_department = call_fetch_department_all()
    logging.info(extract_department)
    transform_dict = transform_department(extract_department)
    load_to_stg(transform_dict, stg_table[0])


def merge_department_stag_to_dwh():
    logging.info(f'MERGE_department_STG_2_DWH begin!')
    logging.info(f'EXECUTE PROC_BASE_department_W')
    sql_merge = "BEGIN F88DWH.PROC_BASE_DEPARTMENT_W; END;"
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    hook.run(sql_merge, autocommit=True)
    logging.info(f'MERGE_STG_2_DWH Done!')


with DAG(dag_id="department_base_dag", start_date=datetime(2023, 9, 1), schedule_interval=schedule_interval,
         default_args=default_args, catchup=False, tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    truncate_stg_department_tbl = PythonOperator(task_id='truncate_stg_department_tbl',
                                             python_callable=truncate_stg_department_tbl,
                                             provide_context=True)

    department_ingestion = PythonOperator(task_id='department_ingestion',
                                      python_callable=department_ingestion,
                                      provide_context=True)

    merge_stg_to_dwh = PythonOperator(task_id='merge_stg_to_dwh',
                                      python_callable=merge_department_stag_to_dwh,
                                      provide_context=True)

    end = EmptyOperator(task_id='End')

(start
 >> truncate_stg_department_tbl
 >> department_ingestion
 >> merge_stg_to_dwh
 >> end)


