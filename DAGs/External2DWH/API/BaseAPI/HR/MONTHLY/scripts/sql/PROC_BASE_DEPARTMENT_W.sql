CREATE OR <PERSON><PERSON>LACE PROCEDURE F88DWH.PROC_BASE_DEPARTMENT_W AS
BEGIN

MERGE
INTO
    F88DWH.W_BASE_DEPARTMENT_D w
        USING STGPROD.BASE_DEPARTMENT s ON
    (w.DEPARTMENT_CONTENT = s.CONTENT)
    WHEN MATCHED THEN
    -- Nếu MATCH thì update những cột Overwrite
    UPDATE
SET
    w.INTEGRATION_ID = s.ID
    WHEN NOT MATCHED THEN
    -- Nếu không MATCH thì Insert những Case thêm mới hoàn toàn
    INSERT
    (INTEGRATION_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_CONTENT,
    VALID_FROM_DT,
    VALID_TO_DT,
    CRN_ROW_ID)
VALUES (s.ID,
s.NAME,
s.CONTENT,
TRUNC(SYSDATE),
TO_DATE('2400-01-01', 'YYYY-MM-DD'),
1);

-- Update những Case có thay đổi (SCD Type 2) -- Bỏ qua những thằng mới insert (có VALID_FROM_DT = TRUNC(SYSDATE))
-- Chỉ update những thằng CRN_ROW_ID = 1
MERGE
INTO
    F88DWH.W_BASE_DEPARTMENT_D w
        USING STGPROD.BASE_DEPARTMENT s ON
    (w.DEPARTMENT_CONTENT = s.CONTENT
        AND ((w.DEPARTMENT_NAME <> s.NAME)
            OR (w.DEPARTMENT_NAME IS NULL
                AND s.NAME IS NOT NULL)
            OR (w.DEPARTMENT_NAME IS NOT NULL
                AND s.NAME IS NULL))
            )
    WHEN MATCHED THEN
    UPDATE
SET
    w.VALID_TO_DT = TRUNC(SYSDATE),
    w.CRN_ROW_ID = 0
WHERE w.VALID_FROM_DT <> TRUNC(SYSDATE) AND w.CRN_ROW_ID = 1
    ;

-- Insert những thằng SCD type 2 -- Chỉ lấy những thằng vừa rồi đã sửa VALID_TO_DT = TRUNC(SYSDATE) và CRN_ROW_ID = 0
INSERT
    INTO
    F88DWH.W_BASE_DEPARTMENT_D (INTEGRATION_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_CONTENT,
    VALID_FROM_DT,
    VALID_TO_DT,
    CRN_ROW_ID)
SELECT
    ID,
    NAME,
    CONTENT,
    TRUNC(SYSDATE),
    TO_DATE('2400-01-01', 'YYYY-MM-DD'),
    1
FROM
    STGPROD.BASE_DEPARTMENT s
WHERE
    s.CONTENT IN (
    SELECT
        DEPARTMENT_CONTENT
    FROM
        F88DWH.W_BASE_DEPARTMENT_D
    WHERE
        VALID_TO_DT = TRUNC(SYSDATE)
            AND CRN_ROW_ID = 0)
;

END PROC_BASE_DEPARTMENT_W;