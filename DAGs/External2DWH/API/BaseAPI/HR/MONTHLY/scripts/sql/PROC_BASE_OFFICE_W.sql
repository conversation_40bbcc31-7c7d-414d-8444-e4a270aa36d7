CREATE OR R<PERSON>LACE PROCEDURE F88DWH.PROC_BASE_OFFICE_W AS
BEGIN

MERGE INTO F88DWH.W_BASE_OFFICE_D w
USING STGPROD.BASE_OFFICE s ON (w.INTEGRATION_ID  = s.ID)
W<PERSON><PERSON> MATCHED THEN
    UPDATE SET
        w.OFFICE_NAME = s.NAME,
        W.OFFICE_ADDRESS = s.ADDRESS
WHEN NOT MATCHED THEN
    INSERT (INTEGRATION_ID, OFFICE_NAME, OFFICE_ADDRESS, VALID_FROM_DT, VALID_TO_DT, CRN_ROW_ID)
    VALUES (s.ID, s.NAME, s.ADDRESS, TO_DATE('1900-01-01', 'YYYY-MM-DD'), TO_DATE('2400-01-01', 'YYYY-MM-DD'), 1);

END PROC_BASE_OFFICE_W;
