import requests
import pandas as pd
import os
import json
from DAGs.utils import df_to_oracle
from airflow.models import Variable

fb_apps_info = Variable.get("fb_apps_info", deserialize_json=True)
# fb_apps_info = \
# {
#     "base_url": 'https://graph.facebook.com',
#     "version_api": 'v18.0',
#     "business_id": ***************,
#     "access_token": 'EAAFkOe9AmugBOwmMYJJR6qV0wnYgdRIwQAGTJgp4IxVi8IIzHoggSf800niSg1hPIzsdPPs8CeO1mgRhWJZCroGqhg9u6mjuyMHLZBiZCRPJfqz42NtGZCThs9CDZA48KaOtDVU9iIopOOKmtD4MCTd4YOCDEtT0yOsxcRy68uGxpPRYTLnfvT1PJcV4u6EvpZCP8CLIrgMu1oqEsZD'
# }


def get_all_owned_ad_accounts():
    try:
        url = f"{fb_apps_info['base_url']}/{fb_apps_info['version_api']}/{fb_apps_info['business_id']}/owned_ad_accounts"
        params = {
            "access_token": fb_apps_info['access_token'],
            "page_size": 1000
        }
        response = requests.get(url, params=params)
        data = response.json()
        # print(data['data'])
        return data['data']
    except Exception as e:
        print(e)
        return []


def get_fb_insights(level, url, fields, time_range, access_token):
    try:
        url = url
        params = {
            "level": level,
            "access_token": access_token,
            "limit": 10000,
            "time_range": time_range,
            "fields": fields
        }

        response = requests.get(url, params=params)
        data = response.json()
        return data['data']
    except Exception as e:
        print(e)
        return []


def get_ad_insight(time_range, act_id):
    time_range = time_range
    level = 'ad'
    url = f"{fb_apps_info['base_url']}/{fb_apps_info['version_api']}/{act_id}/insights"
    fields = "account_id,account_name,action_values,actions,ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name,cost_per_action_type,cpm,reach,social_spend,spend,optimization_goal, objective, impressions"  # ,video_p100_watched_actions,video_p25_watched_actions" video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions"
    ad_data = get_fb_insights(level, url, fields, time_range, fb_apps_info['access_token'])
    df = pd.json_normalize(ad_data)
    if not df.empty:
        df['actions'] = df['actions'].apply(lambda d: d if isinstance(d, list) else [])

        df['actions_post'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'post'), None))
        df['actions_mobile_app_install'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'mobile_app_install'), None))
        df['actions_omni_app_install'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'omni_app_install'), None))
        df['actions_post_reaction'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'post_reaction'), None))
        df['actions_link_click'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'link_click'), None))
        df['actions_post_engagement'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'post_engagement'), None))
        df['actions_comment'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'comment'), None))
        df['actions_page_engagement'] = df['actions'].apply(
            lambda x: next((item['value'] for item in x if item and item['action_type'] == 'page_engagement'), None))
        df = df.drop(columns=['date_start', 'date_stop', 'cost_per_action_type', 'actions'], axis=1)

    return df


def fb_etl_process(execution_date, table_name, conn_id):
    time_range_data = f'{{"since": "{execution_date}", "until": "{execution_date}"}}'
    ad_dfs = pd.DataFrame()
    df_advertiser = pd.json_normalize(get_all_owned_ad_accounts())
    for row in df_advertiser.itertuples(index=False):
        ad_df = get_ad_insight(time_range_data, row.id)
        ad_dfs = pd.concat([ad_dfs, ad_df])

    if not ad_dfs.empty:
        ad_dfs['recording_date'] = f'{execution_date}'
        columns_to_cast = ['account_id', 'account_name', 'ad_id', 'ad_name', 'adset_id', 'adset_name',
                           'campaign_id', 'campaign_name', 'cpm', 'reach', 'social_spend', 'spend',
                           'optimization_goal', 'objective', 'impressions', 'actions_post',
                           'actions_mobile_app_install', 'actions_omni_app_install', 'actions_post_reaction',
                           'actions_link_click', 'actions_post_engagement', 'actions_comment',
                           'actions_page_engagement', 'recording_date']

        # Casting columns to str
        ad_dfs[columns_to_cast] = ad_dfs[columns_to_cast].astype(str)
    df_to_oracle(table_name=table_name, oracle_conn_id=conn_id, df=ad_dfs)


# def load_csv_to_stg(filepath, table_name, conn_id):
#     df = pd.read_csv(filepath)
#     columns_to_cast = ['account_id', 'account_name', 'ad_id', 'ad_name', 'adset_id', 'adset_name',
#                        'campaign_id', 'campaign_name', 'cpm', 'reach', 'social_spend', 'spend',
#                        'optimization_goal', 'objective', 'impressions', 'actions_post',
#                        'actions_mobile_app_install', 'actions_omni_app_install', 'actions_post_reaction',
#                        'actions_link_click', 'actions_post_engagement', 'actions_comment',
#                        'actions_page_engagement', 'recording_date']
#
#     # Casting columns to str
#     df[columns_to_cast] = df[columns_to_cast].astype(str)
#     # df.astype(str)
#     print(df.dtypes)
#     df.fillna('', inplace=True)
#     df_to_oracle(table_name=table_name, oracle_conn_id=conn_id, df=df)


# def test():
    # execution_date = '2024-02-19'
    # time_range_data = f'{{"since": "{execution_date}", "until": "{execution_date}"}}'
    # ad_dfs = pd.DataFrame()
    # df_advertiser = pd.json_normalize(get_all_owned_ad_accounts())
    # for row in df_advertiser.itertuples(index=False):
    #     ad_df = get_ad_insight(time_range_data, row.id)
    #     ad_dfs = pd.concat([ad_dfs, ad_df])
    #
    # if not ad_dfs.empty:
    #     ad_dfs['recording_date'] = f'{execution_date}'
    #     ad_dfs.astype(str)
    #
    # # print(ad_dfs.dtypes)
    # parent_dir = os.path.dirname(os.path.abspath(__file__))
    # file_path = os.path.join(parent_dir, 'fb_dvs_paid_20240223000003.csv')
    # ad_dfs.to_csv(file_path, index=False)
    # load_csv_to_stg(table_name='STGPROD.FACEBOOK_ADS_PAID', conn_id='oracle_f88_dwh', filepath='fb_dvs_paid_20240223000003.csv')


# test()
# # # file_path = os.path.join(parent_dir, 'files/fb_dvs_paid_20240223213623.csv')
# # load_csv_to_stg(file_path, 'STGPROD.FACEBOOK_ADS_PAID', 'oracle_f88_dwh')
