from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from DAGs.utils import truncate_stg_tbl, merge_data_stag_to_dwh
from DAGs.External2DWH.API.FacebookAds.DVS.fb_etl import fb_etl_process  # , load_csv_to_stg
from datetime import datetime
import logging
import os
from DAGs.utils import DAGMonitor

parent_dir = os.path.dirname(os.path.abspath(__file__))
description = 'Get paid data from facebook API'
tags = ['ads', 'facebook', 'daily', 'api']
oracle_conn_id = 'oracle_f88_dwh'
schedule_interval = '@daily'
default_args = {
    'owner': 'F88-DE',
}
staging_table_name = 'STGPROD.FACEBOOK_ADS_PAID'
proc_merge_stg_dwh = 'F88DWH.PROC_FB_ADS_PAID_W'


def validate_params(**kwargs):
    execution_date_cfg = kwargs['dag_run'].conf.get('execution_date')
    if execution_date_cfg:
        logging.info("Trigger by config...")
        execution_date = execution_date_cfg
    else:
        yesterday = DAGMonitor.set_params_operator(**kwargs)['YESTERDAY']
        date = datetime.strptime(yesterday, "%Y%m%d").date()
        execution_date = date.strftime("%Y-%m-%d")
    logging.info("execution_date: " + execution_date)
    kwargs['ti'].xcom_push(key='execute_date', value=execution_date)


# def ingestion_to_dfs(execution_date, **kwargs):
#     filename = f'files/fb_dvs_paid_{datetime.now().strftime("%Y%m%d%H%M%S")}.csv'
#     filepath = os.path.join(parent_dir, filename)
#     fb_etl_process(execution_date, filepath)
#     kwargs['ti'].xcom_push(key='filepath', value=filepath)


with DAG(dag_id="DVS_PAID_FB_ADS_APIS_TO_DWH", start_date=datetime(2024, 1, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=True,
         tags=tags,
         max_active_runs=1) as dag:
    start = EmptyOperator(task_id='Start')

    validate_params = PythonOperator(task_id='validate_params',
                                     python_callable=validate_params,
                                     provide_context=True)

    truncate_stg_dvs_paid_table = PythonOperator(task_id='truncate_stg_dvs_paid_table',
                                                 python_callable=truncate_stg_tbl,
                                                 op_kwargs={'conn_id': oracle_conn_id,
                                                            'tbl_name': staging_table_name},
                                                 provide_context=True)

    call_api_ingestion_to_dfs = PythonOperator(task_id='call_api_ingestion_to_dfs',
                                               python_callable=fb_etl_process,
                                               op_kwargs={
                                                   'execution_date': "{{ task_instance.xcom_pull(key='execute_date', task_ids='validate_params') }}",
                                                   'table_name': staging_table_name, 'conn_id': oracle_conn_id},
                                               dag=dag,
                                               provide_context=True)

    merge_data_stag_to_dwh_fb_paid = PythonOperator(
        task_id='merge_data_stag_to_dwh_fb_paid',
        python_callable=merge_data_stag_to_dwh,
        op_kwargs={'conn_id': oracle_conn_id, 'proc_name': proc_merge_stg_dwh},
        dag=dag
    )
    end = EmptyOperator(task_id='End')

    start >> validate_params >> call_api_ingestion_to_dfs >> merge_data_stag_to_dwh_fb_paid >> end
