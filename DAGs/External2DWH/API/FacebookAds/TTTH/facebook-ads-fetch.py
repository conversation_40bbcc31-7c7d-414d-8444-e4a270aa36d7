import requests
import pandas as pd
import os
import json

version_api = 'v18.0'
acc_id = 'act_1208361943165727'
base_url = f"https://graph.facebook.com/{version_api}/{acc_id}/insights"
access_token = 'EAAFkOe9AmugBOwmMYJJR6qV0wnYgdRIwQAGTJgp4IxVi8IIzHoggSf800niSg1hPIzsdPPs8CeO1mgRhWJZCroGqhg9u6mjuyMHLZBiZCRPJfqz42NtGZCThs9CDZA48KaOtDVU9iIopOOKmtD4MCTd4YOCDEtT0yOsxcRy68uGxpPRYTLnfvT1PJcV4u6EvpZCP8CLIrgMu1oqEsZD'
parent_dir = os.path.dirname(os.path.abspath(__file__))

def get_fb_insights(date_preset, level, url, fields, breakdowns, time_range):
    try:
        url = url
        params = {
            "level": level,
            "access_token": access_token,
            "limit": 10000,
            "data_preset": date_preset,
            "time_range": time_range,
            "breakdowns": breakdowns,
            "fields": fields
        }

        response = requests.get(url, params=params)
        data = response.json()
        # print(data)
        return data['data']
    except Exception as e:
        print(e)
        return []


def get_detail_by_id(id, fields):
    url = f"https://graph.facebook.com/{version_api}/{id}"
    try:
        params = {
            "access_token": access_token,
            "fields": fields
        }
        response = requests.get(url, params=params)
        data = response.json()
        return data
    except Exception as e:
        print(e)
        return {}


def get_campaign(time_range):
    level = 'campaign'
    fields = "account_id,account_name,campaign_id,campaign_name"
    fields_dtl = "lifetime_budget"

    date_preset = ''
    breakdown = ''
    time_range = time_range #'{"since": "2024-01-01", "until": "2024-01-16"}'
    campaigns = get_fb_insights(date_preset, level, base_url, fields, breakdown,time_range)
    df = pd.json_normalize(campaigns)
    df['lifetime_budget'] = df['campaign_id'].apply(lambda x: get_detail_by_id(x, fields_dtl).get('lifetime_budget'))
    df = df.drop(columns=['date_start', 'date_stop'], axis=1)
    df.to_csv(os.path.join(parent_dir, "campaign_insight.csv"),index=False)
    return df


def get_ad_insight(time_range):
    # Thieu location, link
    time_range = time_range #'{"since": "2024-01-01", "until": "2024-01-16"}'
    date_preset = ''
    breakdowns = "age,gender"
    level = 'ad'  # cost_per_inline_link_click,,cost_per_inline_post_engagement,cost_per_unique_click,cpp,ctr,inline_link_clicks,inline_post_engagement,reach,unique_clicks,unique_ctr,frequency,,website_ctr
    fields = "account_id,account_name,action_values,actions,ad_id,ad_name,adset_id,adset_name,campaign_id,campaign_name,cost_per_action_type,cpm,reach,social_spend,spend,impressions,video_p100_watched_actions,video_p25_watched_actions,video_p50_watched_actions,video_p75_watched_actions,video_p95_watched_actions"
    ad_data = get_fb_insights(date_preset, level, base_url, fields, breakdowns, time_range)

    df = pd.json_normalize(ad_data)
    df['actions'] = df['actions'].apply(lambda d: d if isinstance(d, list) else [])
    df['actions_link_click'] = df['actions'].apply(
        lambda x: next((item['value'] for item in x if item and item['action_type'] == 'link_click'), None))
    df['actions_post_engagement'] = df['actions'].apply(
        lambda x: next((item['value'] for item in x if item and item['action_type'] == 'post_engagement'), None))
    df['actions_comment'] = df['actions'].apply(
        lambda x: next((item['value'] for item in x if item and item['action_type'] == 'comment'), None))
    df['actions_page_engagement'] = df['actions'].apply(
        lambda x: next((item['value'] for item in x if item and item['action_type'] == 'page_engagement'), None))
    # cost_per_action
    df['cost_per_action_type'] = df['cost_per_action_type'].apply(lambda d: d if isinstance(d, list) else [])
    df['cost_per_link_click'] = df['actions'].apply(
        lambda x: next((item['value'] for item in x if item and item['action_type'] == 'link_click'), None))

    # cost_per_action
    # df['cost_per_action_type'] = df['cost_per_action_type'].apply(lambda d: d if isinstance(d, list) else [])
    # df['cost_per_link_click'] = df['cost_per_action_type'].apply(
    #     lambda x: next((item['value'] for item in x if item and item['action_type'] == 'link_click'), None))
    #
    # df['cost_per_post_engagement'] = df['cost_per_action_type'].apply(
    #     lambda x: next((item['value'] for item in x if item and item['action_type'] == 'post_engagement'), None))
    #
    # df['cost_per_page_engagement'] = df['cost_per_action_type'].apply(
    #     lambda x: next((item['value'] for item in x if item and item['action_type'] == 'page_engagement'), None))
    #
    # df['cost_per_omni_app_install'] = df['cost_per_action_type'].apply(
    #     lambda x: next((item['value'] for item in x if item and item['action_type'] == 'omni_app_install'), None))

    # df["video_p100_watched_actions"] = df["video_p100_watched_actions"].apply(
    #     lambda x: None if pd.isnull(x) else next(
    #         (item["value"] for item in x if isinstance(x, list) and item and item["action_type"] == "video_view"),
    #         None
    #     )
    # )
    # df['video_p100_watched_actions'] = df['video_p100_watched_actions'].apply(lambda x: next((item['value'] for item in x if item and item['action_type'] == 'video_view'), None))
    # df['video_p50_watched_actions'] = df['video_p50_watched_actions'].apply(lambda x: next((item['value'] for item in x if item and item['action_type'] == 'video_view'), None))
    # df['video_p75_watched_actions'] = df['video_p75_watched_actions'].apply(lambda x: next((item['value'] for item in x if item and item['action_type'] == 'video_view'), None))
    # df['video_p95_watched_actions'] = df['video_p95_watched_actions'].apply(lambda x: next((item['value'] for item in x if item and item['action_type'] == 'video_view'), None))

    df = df.drop(columns=['date_start', 'date_stop', 'cost_per_action_type', 'actions'], axis=1)
    df.to_csv(os.path.join(parent_dir, "ad_insight.csv"),index=False)

def get_ad_set_insight(time_range):
    level = 'adset'
    fields = "account_id,account_name,campaign_id,campaign_name,adset_id,adset_name"
    date_preset = ''
    breakdown = 'age,gender'
    fields_dtl = "targeting"
    time_range = time_range #'{"since": "2024-01-01", "until": "2024-01-16"}'
    adsets = get_fb_insights(date_preset, level, base_url, fields, breakdown, time_range)
    df = pd.json_normalize(adsets)
    df['location'] = df['adset_id'].apply(lambda x: get_detail_by_id(x, fields_dtl).get('targeting',{'geo_locations':{}}).get('geo_locations').get('countries'))
    df = df.drop(columns=['date_start', 'date_stop'], axis=1)
    df.to_csv(os.path.join(parent_dir, "adset_insight.csv"), index=False)
    return df



if __name__ == "__main__":
    advertiser_id = 7253304715377786881
    start_time = "2024-01-01"
    end_time = "2024-01-01"
    time_range_data = '{"since": "2024-01-01", "until": "2024-02-16"}'
    get_campaign(time_range_data)
    get_ad_set_insight(time_range_data)
    get_ad_insight(time_range_data)
