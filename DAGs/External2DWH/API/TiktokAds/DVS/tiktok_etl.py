from typing import Any

import requests
import os
import pandas as pd
import json
from DAGs.utils import df_to_oracle
from airflow.models import Variable

parent_dir = os.path.dirname(os.path.abspath(__file__))
# tiktok_apps_info = \
#     {
#         "page_size": 1000,
#         "access_token": "95cfbefe354b161f2058b68d044161ac8b1606cb",
#         "app_id": '7314578358744907778',
#         'secret': 'b66fb50314c52ded8ce4e96f6cf7babdfc66067c',
#     }
tiktok_apps_info = Variable.get("tiktok_apps_info", deserialize_json=True)
base_url = tiktok_apps_info["base_url"]
access_token = tiktok_apps_info["access_token"]
secret = tiktok_apps_info["secret"]
app_id = tiktok_apps_info["app_id"]

def get_advertiser_tiktok():
    ad_get_url = f"{base_url}/oauth2/advertiser/get/"
    try:
        headers = {
            'Access-Token': access_token
        }
        params = {
            'secret': secret,
            'app_id': app_id
        }

        response = requests.get(ad_get_url, headers=headers, params=params)
        data = response.json()
        return data['data']['list']
    except Exception as e:
        print(e)
        return []


def get_data_api_tiktok(advertiser_id, level, access_token, page_size):
    ad_get_url = f"{base_url}/{level}/get/?advertiser_id={advertiser_id}&page_size={page_size}"
    try:
        headers = {
            'Access-Token': access_token
        }
        response = requests.get(ad_get_url, headers=headers)
        data = response.json()
        return data['data']['list']

    except Exception as e:
        print(e)
        return []


def get_adgroup(advertiser_id):
    ad_raw = get_data_api_tiktok(advertiser_id=advertiser_id, level='adgroup',
                                 access_token=access_token,
                                 page_size=tiktok_apps_info['page_size'])
    df_adgroup = pd.json_normalize(ad_raw)
    return df_adgroup


def get_metrics_adgroup(advertiser_id, start_date, end_date):
    url = f"https://business-api.tiktok.com/open_api/v1.2/reports/integrated/get/"
    params = {
        "advertiser_id": advertiser_id,
        "service_type": "AUCTION",
        "report_type": "BASIC",  # BASIC
        "data_level": "AUCTION_ADGROUP",  # AUCTION_ADVERTISER AUCTION_ADGROUP,AUCTION_AD
        "dimensions": json.dumps(["adgroup_id"]),  # advertiser_id ,adgroup_id,ad_id
        "metrics": json.dumps(["conversion", "spend"]),
        "start_date": start_date,
        "end_date": end_date,
        "page": 1,
        "page_size": tiktok_apps_info['page_size']
    }
    headers = {
        "Access-Token": access_token,
    }
    rsp = requests.get(url, headers=headers, params=params)
    data = rsp.json()['data']['list']
    if len(data) > 0:
        extracted_data = [
            {
                **element.get('metrics'),
                'adgroup_id': element.get('dimensions').get('adgroup_id')
            }
            for element in data
        ]
        return pd.json_normalize(extracted_data)
    else:
        return pd.DataFrame()


def transform_tiktok_adgroup_paid(df: pd.DataFrame()):
    df_transform = pd.DataFrame()
    if not df.empty:
        df_transform['account_id'] = '' if df.get('advertiser_id') is None else df.get('advertiser_id')
        df_transform['account_name'] = '' if df.get('account_name') is None else df.get('account_name')
        df_transform['campaign_id'] = '' if df.get('campaign_id') is None else df.get('campaign_id')
        df_transform['campaign_name'] = '' if df.get('campaign_name') is None else df.get('campaign_name')
        df_transform['adgroup_id'] = '' if df.get('adgroup_id') is None else df.get('adgroup_id')
        df_transform['adgroup_name'] = '' if df.get('adgroup_name') is None else df.get('adgroup_name')
        df_transform['conversions'] = '' if df.get('conversion') is None else df.get('conversion')
        df_transform['spend'] = '' if df.get('spend') is None else df['spend']
    return df_transform


def etl_process(execution_date, table_name, conn_id):
    # Extract -> adgroup only
    df_advertiser = pd.json_normalize(get_advertiser_tiktok())
    print(df_advertiser)
    adgroup_dfs = pd.DataFrame()
    for row in df_advertiser.itertuples(index=False):
        print("account_id: "+row.advertiser_id)
        df_adgroup = get_adgroup(row.advertiser_id)

        if not df_adgroup.empty:
            df_adgroup_metric = get_metrics_adgroup(row.advertiser_id, start_date=execution_date, end_date=execution_date)
            df_adgroup_metric.adgroup_id = df_adgroup_metric.adgroup_id.astype(str)
            df_adgroup = df_adgroup.merge(df_adgroup_metric, on='adgroup_id', how='left')

            df_adgroup['account_name'] = row.advertiser_name
            adgroup_dfs = pd.concat([adgroup_dfs, df_adgroup])

    # Transform
    adgroup_dfs_transformed = transform_tiktok_adgroup_paid(df=adgroup_dfs)
    adgroup_dfs_transformed['recording_date'] = f'{execution_date}'
    # Load
    df_to_oracle(table_name=table_name, oracle_conn_id=conn_id, df=adgroup_dfs_transformed)
