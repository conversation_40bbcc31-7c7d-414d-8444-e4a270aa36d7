from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from DAGs.utils import truncate_stg_tbl, merge_data_stag_to_dwh
from DAGs.External2DWH.API.TiktokAds.DVS.tiktok_etl import etl_process
from datetime import date, timedelta, datetime
import logging
from DAGs.utils import DAGMonitor
description = 'Get paid data from tiktok API'
tags = ['ads', 'tiktok', 'daily', 'api']
oracle_conn_id = 'oracle_f88_dwh'
schedule_interval = '@daily'
default_args = {
    'owner': 'F88-DE',
}
staging_table_name = 'STGPROD.TIKTOK_ADS_PAID'
proc_merge_stg_dwh = 'F88DWH.PROC_TIKTOK_ADS_PAID_W'


def validate_params(**kwargs):
    execution_date_cfg = kwargs['dag_run'].conf.get('execution_date')
    if execution_date_cfg:
        logging.info("Trigger by config...")
        execution_date = execution_date_cfg
    else:
        # execution_date_proxy = kwargs['execution_date']  # Assuming kwarg['execution_date'] is <class 'Proxy'>
        # logical_date = execution_date_proxy.date() if hasattr(execution_date_proxy, 'date') else execution_date_proxy
        # if isinstance(logical_date, str):
        #     logical_date = datetime.strptime(logical_date, "%Y-%m-%dT%H:%M:%S%z").date()
        yesterday = DAGMonitor.set_params_operator(**kwargs)['YESTERDAY']
        date = datetime.strptime(yesterday, "%Y%m%d").date()
        execution_date = date.strftime("%Y-%m-%d")
    logging.info("execution_date: " + execution_date)
    kwargs['ti'].xcom_push(key='execute_date', value=execution_date)


with DAG(dag_id="DVS_PAID_TIKTOK_ADS_APIS_TO_DWH", start_date=datetime(2024, 1, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=True,
         tags=tags,
         max_active_runs=1) as dag:
    start = EmptyOperator(task_id='Start')

    validate_params = PythonOperator(task_id='validate_params',
                                     python_callable=validate_params,
                                     provide_context=True)
    truncate_stg_dvs_paid_table = PythonOperator(task_id='truncate_stg_dvs_paid_table',
                                                 python_callable=truncate_stg_tbl,
                                                 op_kwargs={'conn_id': oracle_conn_id,
                                                            'tbl_name': staging_table_name},
                                                 provide_context=True)

    etl_process_tiktok_paid = PythonOperator(
        task_id='etl_process_tiktok_paid',
        python_callable=etl_process,
        op_kwargs={'execution_date': "{{ task_instance.xcom_pull(key='execute_date', task_ids='validate_params') }}",
                   'table_name': staging_table_name, 'conn_id': oracle_conn_id},
        dag=dag
    )

    merge_data_stag_to_dwh_tiktok_paid = PythonOperator(
        task_id='merge_data_stag_to_dwh_tiktok_paid',
        python_callable=merge_data_stag_to_dwh,
        op_kwargs={'conn_id': oracle_conn_id, 'proc_name': proc_merge_stg_dwh},
        dag=dag
    )
    end = EmptyOperator(task_id='End')

start >> validate_params >> truncate_stg_dvs_paid_table >> etl_process_tiktok_paid >> merge_data_stag_to_dwh_tiktok_paid >> end
