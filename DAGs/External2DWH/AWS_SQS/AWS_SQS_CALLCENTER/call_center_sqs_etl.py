import gzip
import logging
import pandas as pd
import boto3
import json
from datetime import datetime
import os
import pyarrow.parquet as pq
from airflow.providers.oracle.hooks.oracle import OracleHook
import shutil
from airflow.hooks.base import BaseHook
from DAGs.utils import df_to_oracle
import pickle

parent_dir = os.path.dirname(os.path.abspath(__file__))

"""
    - Function name: ingestion
    - Behavior: get data from callcenter sqs -> landing bucket
"""


def ingestion(oracle_conn_id, sqs_conn_id, stg_tbl): 
    hook = BaseHook().get_connection(sqs_conn_id)
    queue_url = hook.host
    region_name = hook.schema
    access_key = hook.login
    secret_key = hook.get_password()

    staging_bucket = os.path.join(parent_dir, 'consume_storage')
    #message_bodies, delete_message_list = receive_message(queue_url, region_name, access_key, secret_key)
    message_bodies = receive_message(queue_url, region_name, access_key, secret_key)
    exception = None
    is_done = False
    # Write to staging bucket
    write_array_to_json(message_bodies, staging_bucket)

    # if not os.path.exists(staging_bucket):
    #     os.makedirs(staging_bucket)
    # error_pickle_path = os.path.join(staging_bucket, f"{formatted_time}-voicebot.pickle")
    # with open(error_pickle_path, 'wb') as file:
    #     pickle.dump(message_bodies, file, protocol=pickle.HIGHEST_PROTOCOL)
    try:
        # consume data => body + delete_list
        logging.info(f"Number of message bodies: {len(message_bodies)} ")
        first_messages = message_bodies[0]
        logging.info(f"First messages: {first_messages}")
        # transform | masked  and load to staging
        cleaned_bodies_df = transformation(message_bodies)
        df_to_oracle(stg_tbl, oracle_conn_id, cleaned_bodies_df) 
        # Delete the message using delete_message_batch
        #delete_message(delete_message_list, queue_url, access_key, secret_key, region_name)
        is_done = True
    except Exception as e:
        exception = e
        logging.info(e)

    if not is_done:
        raise RuntimeError(exception)


def receive_message(queue_url, region_name, access_key, secret_key):
    logging.info(f"region_name :{region_name}")
    logging.info(f"queue_url :{queue_url}")
    sqs_client = boto3.client('sqs', aws_access_key_id=access_key, aws_secret_access_key=secret_key,
                              region_name=region_name)

    # Receive messages from the queue until no more messages are available
    message_bodies = []
    messages_to_deletes = []
    while True:
        response = sqs_client.receive_message(
            QueueUrl=queue_url,
            MaxNumberOfMessages=10,  # Adjust the number of messages to receive per API call as per your requirement
            WaitTimeSeconds=20
        )
        messages = response.get('Messages', [])
        # No more messages in the queue, exit the loop
        if not messages:
            break
        for message in messages:
            # process message body
            
            body = json.loads(message['Body'])
            message_bodies.append(body)
            messages_to_deletes.append({
                'MessageId': message['MessageId'],
                'ReceiptHandle': message['ReceiptHandle']
            })
    return message_bodies, messages_to_deletes


"""
    - Function name: transformation()
    - Behavior: processing, cleaning data and storage in staging bucket 
"""


def transformation(message_bodies):
    logging.info(f"Transformation step - Begin")
    cleaned_df = pd.DataFrame()
    if len(message_bodies) != 0:
        df = pd.json_normalize(message_bodies)
        # Replace NaN with N/A
        df.fillna(value='N/A', inplace=True)
        logging.info(f"Replace NaN with N/A - Done")
        # Drop duplicate rows based on all columns
        # df.drop_duplicates(subset=['id'], inplace=True)

        # Count distinct IDs and display IDs with duplicate occurrences
        #distinct_counts = df['call_id'].value_counts()
        #duplicates = distinct_counts[distinct_counts > 1]
        # Display the duplicate IDs and their occurrence count
        #if len(duplicates) > 0:
           # logging.info("======================= Warning: Duplicates ======================")
            #logging.info("Duplicate IDs and Occurrence Count:")
            #for call_id, count in duplicates.items():
             #   logging.info(f"ID: {call_id}, Count: {count}")
           # logging.info('================================')

        # Clean data (encryption personal data of customer,etc...)
        logging.info(f"clean_callcenter - Begin")
        cleaned_df = clean_callcenter(df)
    logging.info(f"Number of message before transformed: {len(cleaned_df)} ")
    logging.info(f"Transformation step - Finished")
    return cleaned_df


"""
    - Function name: load()
    - Behavior: loading data from staging bucket to staging layer in dwh
"""


def clean_callcenter(combine_df):
    cleaned_df = pd.DataFrame()
    if not combine_df.empty:
        cleaned_df['xml_cdr_uuid'] = '' if combine_df.get('xml_cdr_uuid') is None else combine_df.get(
            'xml_cdr_uuid')
        cleaned_df['domain_uuid'] = '' if combine_df.get('domain_uuid') is None else combine_df.get(
            'domain_uuid')
        cleaned_df['domain_name'] = '' if combine_df.get('domain_name') is None else combine_df.get('domain_name')
        cleaned_df['direction'] = '' if combine_df.get('direction') is None else combine_df.get('direction')
        cleaned_df['caller_id_name'] = '' if combine_df.get('caller_id_name') is None else combine_df.get(
            'caller_id_name')
        cleaned_df['caller_id_number'] = '' if combine_df.get('caller_id_number') is None else combine_df.get(
            'caller_id_number')
        cleaned_df['caller_destination'] = '' if combine_df.get('caller_destination') is None else combine_df.get('caller_destination')
        cleaned_df['destination_number'] = '' if combine_df.get('destination_number') is None else combine_df.get('destination_number')
        cleaned_df['start_stamp'] = '' if combine_df.get('start_stamp') is None else combine_df.get('start_stamp')
        cleaned_df['answer_stamp'] = '' if combine_df.get('answer_stamp') is None else combine_df.get('answer_stamp')
        cleaned_df['end_stamp'] = '' if combine_df.get('end_stamp') is None else combine_df.get('end_stamp')
        cleaned_df['duration'] = '' if combine_df.get('duration') is None else combine_df.get('duration')
        cleaned_df['mduration'] = '' if combine_df.get('mduration') is None else combine_df.get('mduration')
        cleaned_df['billsec'] = '' if combine_df.get(
            'billsec') is None else combine_df.get('billsec')
        cleaned_df['billmsec'] = '' if combine_df.get(
            'billmsec') is None else combine_df.get('billmsec')
        cleaned_df['record_path'] = '' if combine_df.get('record_path') is None else combine_df.get('record_path')
        cleaned_df['record_name'] = '' if combine_df.get('record_name') is None else combine_df.get('record_name')
        cleaned_df['missed_call'] = '' if combine_df.get('missed_call') is None else combine_df.get('missed_call')
        cleaned_df['waitsec'] = '' if combine_df.get('waitsec') is None else combine_df.get('waitsec')
        cleaned_df['digits_dialed'] = '' if combine_df.get('digits_dialed') is None else combine_df.get('digits_dialed')
        cleaned_df['hangup_cause'] = '' if combine_df.get('hangup_cause') is None else combine_df.get('hangup_cause')
        cleaned_df['hangup_cause_q850'] = '' if combine_df.get('hangup_cause_q850') is None else combine_df.get('hangup_cause_q850')
        cleaned_df['user_id'] = '' if combine_df.get('user_id') is None else combine_df.get('user_id')
        cleaned_df['global_call_id'] = '' if combine_df.get('global_call_id') is None else combine_df.get('global_call_id')
        cleaned_df['bridge_hangup_cause'] = '' if combine_df.get('bridge_hangup_cause') is None else combine_df.get('bridge_hangup_cause')
        cleaned_df['call_id'] = '' if combine_df.get('call_id') is None else combine_df.get('call_id')
        cleaned_df['is_pushed'] = '' if combine_df.get('is_pushed') is None else combine_df.get('is_pushed')
    return cleaned_df


def delete_message(delete_message_list, queue_url, access_key, secret_key, region_name):
    logging.info(f"Number of deletion list: {len(delete_message_list)}")
    total_deleted = 0
    total_failed = 0
    sqs_client = boto3.client('sqs', aws_access_key_id=access_key, aws_secret_access_key=secret_key,
                              region_name=region_name)
    # Process messages in batches
    while delete_message_list:
        # Extract a batch of up to 10 messages
        batch = delete_message_list[:10]
        delete_message_list = delete_message_list[10:]
        # Delete messages in the current batch
        delete_response = sqs_client.delete_message_batch(
            QueueUrl=queue_url,
            Entries=batch
        )

        # Update counters for overall deletion progress
        if 'Successful' in delete_response:
            total_deleted += len(delete_response['Successful'])
            total_failed += len(batch) - len(delete_response['Successful'])
        else:
            total_failed += len(batch)

    # Log the summary of the entire batch deletion process
    logging.info(f"{total_deleted} messages deleted successfully.")
    if total_failed > 0:
        logging.info(f"{total_failed} messages failed to delete.")


def write_array_to_json(array, file_directory):
    if len(array) > 0:
        if not os.path.exists(file_directory):
            os.makedirs(file_directory, exist_ok=True)
        # Generate timestamp
        formatted_time = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

        # Create file name with timestamp
        file_name = f"{formatted_time}_callcenter.json.gz"

        # Create file path
        file_path = file_directory + '/' + file_name
        with gzip.open(file_path, 'wt', encoding='utf-8') as file:
            json.dump(array, file, indent=4)

        logging.info(f"Array successfully written to {file_path}")