CREATE OR <PERSON><PERSON>LACE PROCEDURE F88DWH.PROC_CALL_INFO_VOICEBOT_SQS_W AS
BEGIN

MERGE INTO F88DWH.W_CALL_INFO_VOICEBOT_SQS_F w
    USING STGPROD.CALL_INFO_VOICEBOT_SQS s ON
        (S.id = W.integration_id)
    WHEN NOT MATCHED THEN
        INSERT
            (
             integration_id,
             scenario_id,
             scenario_name,
             campaign_name,
             campaign_id,
             conversation_id,
             callcenter_phone,
             call_at,
             call_duration,
             customer_phone,
             hangup_at,
             pickup_at,
             create_at,
             status,
             reports_action_code,
             reports_call_result,
             load_dt
                )
            VALUES (
            		s.id,
            		s.scenario_id,
                    s.scenario_name,
                    s.campaign_name,
                    s.campaign_id,
                    s.conversation_id,
                    s.callcenter_phone,
                    CASE WHEN s.call_at = 0 THEN NULL ELSE TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.call_at / 86400000) END,
                    s.call_duration,
                    s.customer_phone,
                    CASE WHEN s.hangup_at = 0 THEN NULL ELSE TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.hangup_at / 86400000) END,
                    CASE WHEN s.pickup_at = 0 THEN NULL ELSE TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.pickup_at / 86400000) END,
                    CASE WHEN s.create_at = 0 THEN NULL ELSE TO_DATE('1970-01-01', 'YYYY-MM-DD') + 7 / 24 + (s.create_at / 86400000) END,
                    s.status,
                    s.reports_action_code,
                    s.reports_call_result,
                    sysdate) ;
END;
