from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from DAGs.External2DWH.AWS_SQS.voicebot_sqs_etl import ingestion
from datetime import datetime
import logging
from DAGs.utils import merge_data_stag_to_dwh, truncate_stg_tbl

description = 'Get Voicebot message data from AWS SQS'
tags = ['voicebot', 'daily', 'sqs']
schedule_interval = '@daily'
default_args = {
    'owner': 'F88-DE',
}
DAG_ID = "voicebot_sqs_to_dwh"
STG_TABLE = 'STGPROD.CALL_INFO_VOICEBOT_SQS'
PROC_MERGE_TO_DWH = 'F88DWH.PROC_CALL_INFO_VOICEBOT_SQS_W'
# PROC_MERGE_TO_DWH = 'DEVDWH.PROC_CALL_INFO_VOICEBOT_SQS_W'
# STG_TABLE = 'STGDWH.CALL_INFO_VOICEBOT_SQS'
oracle_conn_id = 'oracle_f88_dwh'
sqs_conn_id = 'f88_aws_sqs_conn_id'

with DAG(dag_id=DAG_ID, start_date=datetime(2023, 12, 29),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    truncate_stg_call_voicebot_sqs_table = PythonOperator(task_id='truncate_stg_call_voicebot_sqs_table',
                                                          python_callable=truncate_stg_tbl,
                                                          op_kwargs={'tbl_name': STG_TABLE, 'conn_id': oracle_conn_id},
                                                          provide_context=True)

    ingestion_data_voicebot = PythonOperator(
        task_id='ingestion_data_voicebot',
        python_callable=ingestion,
        op_kwargs={'oracle_conn_id': oracle_conn_id,
                   'sqs_conn_id': sqs_conn_id,
                   'stg_tbl': STG_TABLE},
        dag=dag
    )

    merge_data_stag_to_dwh_voicebot = PythonOperator(
        task_id='merge_data_stag_to_dwh_voicebot',
        python_callable=merge_data_stag_to_dwh,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': PROC_MERGE_TO_DWH},
        dag=dag
    )
    end = EmptyOperator(task_id='End')

start >>  truncate_stg_call_voicebot_sqs_table >> ingestion_data_voicebot >> merge_data_stag_to_dwh_voicebot >> end
