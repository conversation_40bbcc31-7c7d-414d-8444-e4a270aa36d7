import os
import datetime
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

DAG_ID = 'MAIN_DAILY_MYF88'
description = 'Main Dag for MyF88'
start_date = datetime.datetime(2023, 11, 13)
schedule_interval = None
tags = ['app_my88', 'daily', 'main']

with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'all_success'
         },
         start_date=start_date,
         catchup=False,
         max_active_runs=1,
         schedule_interval=schedule_interval,
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id="Start", dag=dag)
    end = EmptyOperator(task_id="End", dag=dag)

    trigger_DAILY_W_APP_MY88_PAWN = TriggerDagRunOperator(task_id='trigger_DAILY_W_APP_MY88_PAWN', trigger_dag_id='DAILY_W_APP_MY88_PAWN_F', poke_interval=5, wait_for_completion=True)

    trigger_DAILY_W_APP_MY88_USERINFO_D = TriggerDagRunOperator(task_id='trigger_DAILY_W_APP_MY88_USERINFO_D', trigger_dag_id='DAILY_W_APP_MY88_USERINFO_D', poke_interval=5, wait_for_completion=True)

    trigger_DAILY_W_APP_MY88_WALLETINFOMATION_HIS_F = TriggerDagRunOperator(task_id='trigger_DAILY_W_APP_MY88_WALLETINFOMATION_HIS_F', trigger_dag_id='DAILY_W_APP_MY88_WALLETINFOMATION_HIS_F', poke_interval=5, wait_for_completion=True)

    trigger_DAILY_W_APP_MY88_REGISTER_F = TriggerDagRunOperator(task_id='trigger_DAILY_W_APP_MY88_REGISTER_F', trigger_dag_id='DAILY_W_APP_MY88_REGISTER_F', poke_interval=5, wait_for_completion=True)

    start >> trigger_DAILY_W_APP_MY88_PAWN >> trigger_DAILY_W_APP_MY88_USERINFO_D >> trigger_DAILY_W_APP_MY88_WALLETINFOMATION_HIS_F >> trigger_DAILY_W_APP_MY88_REGISTER_F >> end