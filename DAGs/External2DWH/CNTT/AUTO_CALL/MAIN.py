import os
import datetime
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

DAG_ID = 'MAIN_DAILY_AUTOCALL'
description = 'Main Dag for AUTOCALL'
start_date = datetime.datetime(2024, 6, 20)
schedule_interval = '0 7 * * *'
tags = ['autocall', 'daily', 'main']

with DAG(dag_id=DAG_ID, description=description,
        dagrun_timeout=datetime.timedelta(minutes=45),
        catchup=False,
        max_active_runs=1,
        default_args={
            'owner': 'F88-DE',
            'retries': 3,
            'retry_delay': datetime.timedelta(minutes=1),
             'trigger_rule': 'all_success'
         },
         start_date=start_date,
         schedule_interval=schedule_interval,
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id="Start")
    end = EmptyOperator(task_id="End")
    parent_dir = os.path.dirname(os.path.abspath(__file__))
    list_dags = next(os.walk(parent_dir))[2]
    list_tasks = []

    for i in list_dags:
        i = i.replace('.py', '')
        if i in ('MAIN', '__init__'): #DAILY_W_ACTIVITY_ENTITY_F
            continue
        trigger_dag = TriggerDagRunOperator(task_id=f"{i}", trigger_dag_id=i, wait_for_completion=True, poke_interval=5, deferrable=True)
        list_tasks.append(trigger_dag)

    start >> list_tasks >> end
