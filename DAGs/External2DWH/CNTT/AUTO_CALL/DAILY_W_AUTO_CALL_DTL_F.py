from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMonitor, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh
import os
import logging
from datetime import datetime, timedelta
import pandas as pd

dag_name = 'DAILY_W_AUTO_CALL_DTL_F'
description = 'Get data CALL DETAIL FROM AUTO CALL TO DWH'
autocall_conn_id = 'autocall_conn_id'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.AUTOCALL_V_CALL_DETAIL'
proc_name = 'F88DWH.PROC_W_AUTO_CALL_DTL_F'
tags = ['autocall']
schedule_interval = None
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
parent_dir = os.path.dirname(os.path.abspath(__file__))


def validate_param(**context):
    start_date = context['dag_run'].logical_date
    params = DAGMonitor.set_params(start_date)
    context['ti'].xcom_push(key=dag_name, value=params)
    logging.info('Set Parameters Done!')

def ingestion(source_conn_id, target_conn_id, stg_tbl_name, **context):
    # init value sql and fill param for quert
    query_file = os.path.join(parent_dir, 'queries', f'{dag_name}.sql')
    sql = open(query_file, "r", encoding='utf-8').read()
    # params = DAGMonitor.set_params(datetime.datetime.now())
    params = context['ti'].xcom_pull(key=dag_name)
    filter_params = DAGMonitor.sql_get_filtered_params(sql=sql, parameters=params)

    # Get data from source
    autocall_hook = PostgresHook(postgres_conn_id=source_conn_id)
    for param, value in filter_params.items():
        sql = sql.replace(f':{param}', f"{value}")

    autocall_df = autocall_hook.get_pandas_df(sql=sql)
    autocall_df = autocall_df.fillna('')
    # Assuming your DataFrame is named 'autocall_df'
    autocall_df['creationtime'] = pd.to_datetime(autocall_df['creationtime'], errors='coerce')
    autocall_df['attempt_time'] = pd.to_datetime(autocall_df['attempt_time'], errors='coerce')

    autocall_df = autocall_df.astype({
        'id': 'str',
        'campaignid': 'str',
        'phonenumber': 'int',
        'hangup_cause': 'str',
        'answered_time': 'int',
        'hangup_time': 'int',
        'dialednumber': 'int',
        'campaign': 'str'
    })
    # ingest data to staging layer of dwh
    df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id, df=autocall_df)
    logging.info('Get data source to stg done!')


def merge_transform(conn_id, proc_name, **context):
    params = context['ti'].xcom_pull(key=dag_name)
    today = params.get('TODAY')
    proc_name_with_p_date = f"{proc_name}({today})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)


with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:
    set_param = PythonOperator(task_id='set_param',
                               python_callable=validate_param,
                               provide_context=True)

    # truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
    #                                     python_callable=truncate_stg_tbl,
    #                                     op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
    #                                     provide_context=True)


    # ingestion_autocall_data_to_stg = PythonOperator(
    #     task_id='ingestion_autocall_data_to_stg',
    #     python_callable=ingestion,
    #     op_kwargs={'source_conn_id': autocall_conn_id,
    #                'target_conn_id': oracle_conn_id,
    #                'stg_tbl_name': stg_table},
    #     dag=dag
    #)


    merge_to_dwh = PythonOperator(
        task_id='merge_to_dwh',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )

#set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
set_param >> merge_to_dwh


