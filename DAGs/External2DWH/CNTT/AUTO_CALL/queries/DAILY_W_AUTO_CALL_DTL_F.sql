with data_campaign as (
        select
            id,
            dialednumber,
            name from campaign c where c.dialednumber = '89999999'),
        data_v_call_detail as (
        select
            id,
            campaignid,
            creationtime,
            phonenumber,
            hangup_cause,
            attempt_time,
            answered_time,
            hangup_time
            from v_call_detail d
            where
            and d.attempt_time >= ':YESTERDAY'
            and d.attempt_time < ':TODAY'
        )
        select
            cast(d.id as VARCHAR) as id,
            cast(d.campaignid as VARCHAR) as campaignid,
            cast(d.creationtime as TIMESTAMP) as creationtime,
            cast(d.phonenumber as DECIMAL(38,
            10)) as phonenumber,
            d.hangup_cause,
            cast(d.attempt_time as TIMESTAMP) as attempt_time,
            cast(d.answered_time as DECIMAL(38,
            10)) as answered_time,
            cast(d.hangup_time as DECIMAL(38,
            10)) as hangup_time,
            cast(c.dialednumber as DECIMA<PERSON>(38,
            10)) as dialednumber,
            c.name as Campaign from data_v_call_detail d join data_campaign c on
            d.campaignid = c.id