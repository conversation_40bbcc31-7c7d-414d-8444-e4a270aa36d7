from airflow.providers.postgres.hooks.postgres import Postg<PERSON>Hook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMoni<PERSON>, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from airflow.utils.context import Context

dag_name='DAILY_W_ALLOCATE_DAILY_F'
description = 'Get data loan allocate from Nexusti to DWH'
qlts_conn_id = 'qlts_conn_id'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.QLTS_ALLOCATE_DAILY'
dwh_table = 'F88DWH.W_ALLOCATE_DAILY_F'
proc_name='F88DWH.PROC_W_ALLOCATE_DAILY_F'
tags = ['qlts']
schedule_interval = None
default_args = {
    'owner': 'F88-DE',
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
}
parent_dir = os.path.dirname(os.path.abspath(__file__))

def validate_param(**context):
    start_date = context['dag_run'].logical_date
    params = DAGMonitor.set_params(start_date)
    context['ti'].xcom_push(key=dag_name, value=params)
    logging.info('Set Parameters Done!')


def ingestion(source_conn_id, target_conn_id, stg_tbl_name, **context):

    # init value sql and fill param for quert
    query_file = os.path.join(parent_dir, 'queries', f'{dag_name}.sql')
    sql = open(query_file, "r", encoding='utf-8').read()
    # params = DAGMonitor.set_params(datetime.datetime.now())
    params = context['ti'].xcom_pull(key=dag_name)
    filter_params = DAGMonitor.sql_get_filtered_params(sql=sql,parameters=params)
    
    # Get data from source
    qlts_hook = PostgresHook(postgres_conn_id=source_conn_id)
    for param, value in filter_params.items():
        sql = sql.replace(f':{param}', f"{value}")

    allocate_df = qlts_hook.get_pandas_df(sql=sql)
    allocate_df = allocate_df.fillna('')
    number_records = allocate_df.shape[0]
    logging.info(f'Số lượng bản ghi: {number_records}')
    print(allocate_df.columns)

    # Assuming your DataFrame is named 'allocate_df'
    allocate_df['last_payment_date'] = pd.to_datetime(allocate_df['last_payment_date'], errors='coerce')
    allocate_df['creation_date'] = pd.to_datetime(allocate_df['creation_date'], errors='coerce')
    allocate_df = allocate_df.astype({
        'id': 'str',
        'code': 'str',
        'product_type_hd': 'str',
        'cif': 'str',
        'toddpd': 'str',
        'bom_total_principle': 'str',
        'tod_total_principle': 'str',
        'bom_collection_must': 'str',
        'assignee': 'str',
        'call_assignee': 'str',
        'field_assignee': 'str',
        'bom_bucket': 'str',
        'tod_bucket': 'str',
        'created_by': 'str',
        'loan_package': 'str',
        'sub_channel': 'str',
        'ovd_farthest_period_amt': 'str'
    })

    #ingest data to staging layer of dwh
    df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id,df=allocate_df)
    logging.info('Get data source to stg done!')

def delete_data_run_before(target_conn_id, target_table, **context):
    query_file = os.path.join(parent_dir, 'queries', f'{dag_name}.sql')
    sql = open(query_file, "r", encoding='utf-8').read()
    # params = DAGMonitor.set_params(datetime.datetime.now())
    params = context['ti'].xcom_pull(key=dag_name)
    filter_params = DAGMonitor.sql_get_filtered_params(sql=sql,parameters=params)
    processing_date = pd.Series(filter_params).min()
    processing_next_date = pd.Series(filter_params).max()
    year_num = int(str(processing_date)[:4])
    month_num = int(str(processing_date)[4:6])
    sql_delete = f"DELETE FROM {target_table} WHERE YEAR_NUM = {year_num} and MONTH_NUM = {month_num} and DATE_WID >= {processing_date} and DATE_WID < {processing_next_date}"
    logging.info(f'sql_delete {sql_delete}')
    hook = OracleHook(oracle_conn_id=target_conn_id)
    hook.run(sql_delete, autocommit=True)

def merge_transform(conn_id, proc_name, **context):
    run_time = DAGMonitor.set_params(datetime.now())
    logging.info(f"proc_name_with_p_date: {proc_name}")
    logging.info(f"run_time: {run_time}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name)

def dagmonitor_on_fail_callback(**context):
    # dag_id = 'QTRR_MONTHLY_SHAREPOINT_RRHD_PGD_TO_DWH'
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    get_target_email = '''SELECT EMAIL FROM F88DWH.W_EMPLOYEE_D wed WHERE DEPARTMENT_NM = 'Phòng Quản trị dữ liệu' AND CRN_ROW_IND = 1 AND JOB_DATE_OUT IS NULL'''
    email_results = hook.get_records(get_target_email)
    users_callback = [row[0] for row in email_results]
    print(users_callback)
    if not users_callback:
        users_callback = ['<EMAIL>','<EMAIL>']
    execution_date = datetime.now()
    # change these as per use
    your_email = "<EMAIL>"
    your_password = "F88@6388"
    print('Send mail')
    # establishing connection with gmail
    session = smtplib.SMTP('smtp.f88.co', 587)
    session.starttls()
    session.login(your_email, your_password)

    html_content = '''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị Phòng Quản trị dữ liệu</p>
        <p></p>
        <p>Hiện tại dag có id là: {dag_name} đang bị lỗi vào ngày: {date}.</p>
        <P></P>
        <P>Trân trọng!</P>
    </body>
    </html>
    '''
        
    msg = MIMEMultipart()
    msg['From'] = your_email
    msg['To'] = ", ".join(users_callback)
    msg['Subject'] = f'THÔNG BÁO VỀ DAG CHẠY LỖI NGÀY {execution_date.strftime("%Y-%m-%d")}'
    content=html_content.format(dag_name = dag_name, date = execution_date, exception=context.get('exception'))

    html_part = MIMEText(content, 'html')
    msg.attach(html_part)

    session.sendmail(your_email, users_callback, msg.as_string())
    session.quit()
    print('Mail sent')

with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         max_active_runs=1,
         default_args=default_args,
         on_failure_callback=dagmonitor_on_fail_callback,
         catchup=False,
         tags=tags,
         dagrun_timeout=timedelta(minutes=20) ) as dag:
    
    set_param = PythonOperator(task_id='set_param',
                            python_callable=validate_param,
                            provide_context=True)

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                                          python_callable=truncate_stg_tbl,
                                                          op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
                                                          provide_context=True)

    ingestion_qlts_data_to_stg = PythonOperator(
        task_id='ingestion_qlts_data_to_stg',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': qlts_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )

    delete_dwh_data_run_before = PythonOperator(
        task_id='delete_dwh_data_run_before',
        python_callable=delete_data_run_before,
        op_kwargs={'target_conn_id': oracle_conn_id,
                   'target_table': dwh_table},
        dag=dag
    )

    merge_to_dwh = PythonOperator(
        task_id='merge_to_dwh',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )

set_param >> truncate_stg_table >> ingestion_qlts_data_to_stg >> delete_dwh_data_run_before >> merge_to_dwh