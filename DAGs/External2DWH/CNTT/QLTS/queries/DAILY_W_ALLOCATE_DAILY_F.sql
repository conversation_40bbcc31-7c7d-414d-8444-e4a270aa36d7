with a as (select debt_id, dept_code,
	sum(total_outstanding) ovd_farthest_period_amt --over(partition by dept_code)
from
	debt_repay_plan
where
	status <> 4
	and repay_date::date <= now()::date
group by dept_code, debt_id),-- <PERSON><PERSON> tiền phải thu từ kì nợ xa nhất đến thời điểm hiện tại
assign as (select 
	id,
	debt_code,
	debt_id,
	created_by,
	creation_date,
	last_modified_by,
	last_modified_date,
	assignee
from
	assignment_history ahe where assignee is not null),
call_ as (select 
	id,
	debt_code,
	debt_id,
	created_by,
	creation_date,
	last_modified_by,
	last_modified_date,
	call_assignee
from
	assignment_history ahe where call_assignee is not null),
field as (select 
	id,
	debt_code,
	debt_id,
	created_by,
	creation_date,
	last_modified_by,
	last_modified_date,
	field_assignee
from
	assignment_history ahe where field_assignee is not null),
union_is_null as (select
	assign.id,
	assign.debt_code,
	assign.debt_id,
	assign.created_by,
	assign.creation_date,
	assign.last_modified_by,
	assign.last_modified_date,
	assign.assignee,
	call_.call_assignee,
	field.field_assignee
from assign left join call_ on assign.debt_code = call_.debt_code and assign.creation_date = call_.creation_date
left join field on assign.debt_code = field.debt_code and assign.creation_date = field.creation_date
union 
select 
	id,
	debt_code,
	debt_id,
	created_by,
	creation_date,
	last_modified_by,
	last_modified_date,
	assignee,
	call_assignee,
	field_assignee
from
	assignment_history ahe where field_assignee is null and call_assignee is null and assignee is null),
b as (select
row_number() over(partition by union_is_null.debt_code, creation_date::date
order by
	union_is_null.creation_date desc) rnk,
	union_is_null.*
--	assign.id,
--	assign.debt_code,
--	assign.debt_id,
--	assign.created_by,
--	assign.creation_date,
--	assign.last_modified_by,
--	assign.last_modified_date,
--	assign.assignee,
--	call_.call_assignee,
--	field.field_assignee
from union_is_null), --Người và thời gian phân bổ hợp đồng
c as (select
	de.id,
	de.code,
    de.product_type_hd,
	de.cif,
	ass.assignee,
	ass.call_assignee,
	ass.field_assignee,
	dte.bom_bucket,
	dte.tod_bucket,
	de.loan_package,
	de.sub_channel,
	dte.toddpd,
	dte.last_payment_date,
	dte.bom_total_principle,
	dte.tod_total_principle,
	cie.bom_collection_must,
	ass.created_by,
	ass.creation_date,
    ass.last_modified_date
from
	debt de
	left join debt_tracker dte on de.id = dte.debt_entity_id
	left join (select * from b where b.rnk = 1) ass on de.code = ass.debt_code
	left join collection_info cie on de.id = cie.debt_entity_id)
select
	c.id,
	c.code,
    c.product_type_hd,
	c.cif,
	c.toddpd,
	c.last_payment_date,
	c.bom_total_principle,
	c.tod_total_principle,
	c.bom_collection_must,
	c.assignee,
	c.call_assignee,
	c.field_assignee,
	c.bom_bucket,
	c.tod_bucket,
	c.creation_date,
	c.created_by,
	c.loan_package,
	c.sub_channel,
	a.ovd_farthest_period_amt
from c left join a on c.code = a.dept_code
where
--	c.creation_date >= '2024-06-05' and 
--    c.creation_date < '2024-06-06'
	(c.creation_date >= ':YESTERDAY' and c.creation_date < ':TODAY')
	or (c.last_modified_date >= ':YESTERDAY' and c.last_modified_date < ':TODAY')