select
	ae.id,
    de.code loan_code,
	de.cif customer_code,
	de.product_type_hd,
	ae.created_by,
	ae.creation_date,
	ae.last_modified_by,
	ae.last_modified_date,
	ae.activity_code,
	ae.assignee,
	ae.call_code_map,
	ae.call_id,
	ae.call_info_updated,
	ae.call_provider,
	ae.collection_type,
	ae.contact_name,
	ae.contact_number,
	ae.debt_id,
	ae.distance,
	ae.end_time,
	ae.is_deleted,
	ae.is_promised,
	ae."location",
	case
	when extract(year from ae.next_appointment) < 2022 then '2400-01-01'
    when length(extract(year from next_appointment)::TEXT) > 4 then '2400-01-01'
	else ae.next_appointment
	end next_appointment,
	ae.promise_date,
	ae.promise_money,
	ae.promise_money_payed,
	ae.promise_status,
	ae.relationship,
	ae.start_time,
	ae.asset_type_name,
	ae.bom_bucket,
	ae.channel,
	ae.duration,
	ae.product_id,
	ae.tod_bucket,
	ae.transaction_office_name,
	ae.campaign_id,
	ae.source_app,
    ae.detail,
	ae.audio_url
	-- null img_urls,
	-- null video_urls,
	-- null geo_location
from
	activity ae
left join debt de on
	ae.debt_id = de.id
where

	(ae.creation_date >= ':DAY_BEFORE_YESTERDAY'  and ae.creation_date < ':TODAY' )   
	or (ae.last_modified_date >= ':DAY_BEFORE_YESTERDAY' and ae.last_modified_date < ':TODAY') 
	-- (ae.creation_date >= TO_DATE(:YESTERDAY,'YYYYMMDD') and ae.creation_date < TO_DATE(:TODAY,'YYYYMMDD'))   
	-- or (ae.last_modified_date >= TO_DATE(:YESTERDAY,'YYYYMMDD') and ae.last_modified_date < TO_DATE(:TODAY,'YYYYMMDD'))