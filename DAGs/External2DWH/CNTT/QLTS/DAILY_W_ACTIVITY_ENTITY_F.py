from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMoni<PERSON>, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh
import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from airflow.utils.context import Context

dag_name='DAILY_W_ACTIVITY_ENTITY_F'
description = 'Get data activity history from QLTS.activity to DWH'
qlts_conn_id = 'qlts_conn_id'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.QLTS_ACTIVITY_ENTITY'
proc_name='F88DWH.PROC_W_ACTIVITY_ENTITY_F'
tags = ['qlts']
schedule_interval = None
default_args = {
    'owner': 'F88-DE',
    'retries': 1,
    'retry_delay': timedelta(minutes=1),
}
parent_dir = os.path.dirname(os.path.abspath(__file__))

def validate_param(**context):
    start_date = context['dag_run'].logical_date
    params = DAGMonitor.set_params(start_date)
    context['ti'].xcom_push(key=dag_name, value=params)
    logging.info('Set Parameters Done!')


def ingestion(source_conn_id, target_conn_id, stg_tbl_name, **context):

    # init value sql and fill param for quert
    query_file = os.path.join(parent_dir, 'queries', f'{dag_name}.sql')
    sql = open(query_file, "r", encoding='utf-8').read()
    # params = DAGMonitor.set_params(datetime.datetime.now())
    params = context['ti'].xcom_pull(key=dag_name)
    filter_params = DAGMonitor.sql_get_filtered_params(sql=sql,parameters=params)
    
    # Get data from source
    qlts_hook = PostgresHook(postgres_conn_id=source_conn_id)
    for param, value in filter_params.items():
        sql = sql.replace(f':{param}', f"{value}")

    qlts_df = qlts_hook.get_pandas_df(sql=sql)
    number_records = qlts_df.shape[0]
    logging.info(f'Số lượng bản ghi: {number_records}')
    qlts_df = qlts_df.fillna('')
    # Assuming your DataFrame is named 'qlts_df'
    qlts_df['next_appointment'] = pd.to_datetime(qlts_df['next_appointment'], errors='coerce')
    qlts_df['promise_date'] = pd.to_datetime(qlts_df['promise_date'], errors='coerce')
    qlts_df['start_time'] = pd.to_datetime(qlts_df['start_time'], errors='coerce')
    qlts_df['end_time'] = pd.to_datetime(qlts_df['end_time'], errors='coerce')
    qlts_df['creation_date'] = pd.to_datetime(qlts_df['creation_date'], errors='coerce')
    qlts_df['last_modified_date'] = pd.to_datetime(qlts_df['last_modified_date'], errors='coerce')

    qlts_df = qlts_df.astype({
        'id': 'str',
        'loan_code': 'str',
        'customer_code': 'str',
        'product_type_hd': 'str',
        'created_by': 'str',
        'last_modified_by': 'str',
        'activity_code': 'str',
        'assignee': 'str',
        'call_code_map': 'str',
        'call_id': 'str',
        'call_info_updated': 'bool',
        'call_provider': 'str',
        'collection_type': 'str',
        'contact_name': 'str',
        'contact_number': 'str',
        'debt_id': 'str',
        'detail': 'str',
        'distance': 'str',
        'is_deleted': 'bool',
        'is_promised': 'bool',
        'location': 'str',
        'promise_money': 'str',
        'promise_money_payed': 'str',
        'promise_status': 'str',
        'relationship': 'str',
        'asset_type_name': 'str',
        'bom_bucket': 'str',
        'channel': 'str',
        'duration': 'str',
        'product_id': 'str',
        'tod_bucket': 'str',
        'transaction_office_name': 'str',
        'campaign_id': 'str',
        'source_app': 'str',
        'audio_url': 'str'
    })
    qlts_df = qlts_df.rename(columns={'location': 'address'}) # command test sendmail
    #ingest data to staging layer of dwh
    df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id,df=qlts_df)
    logging.info('Get data source to stg done!')

def merge_transform(conn_id, proc_name, **context):
    query_file = os.path.join(parent_dir, 'queries', f'{dag_name}.sql')    
    sql = open(query_file, "r", encoding='utf-8').read()
    params = context['ti'].xcom_pull(key=dag_name)
    filter_params = DAGMonitor.sql_get_filtered_params(sql=sql,parameters=params)
    processing_date = pd.Series(filter_params).min()
    proc_name_with_p_date = f"{proc_name}({processing_date})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)

def dagmonitor_on_fail_callback(**context):
    # dag_id = 'QTRR_MONTHLY_SHAREPOINT_RRHD_PGD_TO_DWH'
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    get_target_email = '''SELECT EMAIL FROM F88DWH.W_EMPLOYEE_D wed WHERE DEPARTMENT_NM = 'Phòng Quản trị dữ liệu' AND CRN_ROW_IND = 1 AND JOB_DATE_OUT IS NULL'''
    email_results = hook.get_records(get_target_email)
    users_callback = [row[0] for row in email_results]
    print(users_callback)
    if not users_callback:
        users_callback = ['<EMAIL>','<EMAIL>']
    execution_date = datetime.now()
    # change these as per use
    your_email = "<EMAIL>"
    your_password = "F88@6388"
    print('Send mail')
    # establishing connection with gmail
    session = smtplib.SMTP('smtp.f88.co', 587)
    session.starttls()
    session.login(your_email, your_password)

    html_content = '''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị Phòng Quản trị dữ liệu</p>
        <p></p>
        <p>Hiện tại dag có id là: {dag_name} đang bị lỗi vào ngày: {date}.</p>
        <P></P>
        <P>Trân trọng!</P>
    </body>
    </html>
    '''
        
    msg = MIMEMultipart()
    msg['From'] = your_email
    msg['To'] = ", ".join(users_callback)
    msg['Subject'] = f'THÔNG BÁO VỀ DAG CHẠY LỖI NGÀY {execution_date.strftime("%Y-%m-%d")}'
    content=html_content.format(dag_name = dag_name, date = execution_date, exception=context.get('exception'))

    html_part = MIMEText(content, 'html')
    msg.attach(html_part)

    session.sendmail(your_email, users_callback, msg.as_string())
    session.quit()
    print('Mail sent')

# dagmonitor_on_fail_callback()

with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         max_active_runs=1,
         default_args=default_args,
         on_failure_callback=dagmonitor_on_fail_callback,
         catchup=False,
         tags=tags,
         dagrun_timeout=timedelta(minutes=20) ) as dag:
    
    set_param = PythonOperator(task_id='set_param',
                            python_callable=validate_param,
                            provide_context=True)

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                                          python_callable=truncate_stg_tbl,
                                                          op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
                                                          provide_context=True)

    ingestion_qlts_data_to_stg = PythonOperator(
        task_id='ingestion_qlts_data_to_stg',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': qlts_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )

    merge_to_dwh = PythonOperator(
        task_id='merge_to_dwh',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )

set_param >> truncate_stg_table >> ingestion_qlts_data_to_stg >> merge_to_dwh