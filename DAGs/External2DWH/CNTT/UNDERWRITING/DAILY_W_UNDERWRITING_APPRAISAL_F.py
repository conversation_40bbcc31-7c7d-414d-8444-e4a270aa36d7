from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMonitor, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh,write_df_to_oracle
import os
import logging
from datetime import datetime, timedelta
from bson import ObjectId
import pandas as pd
from pandas import json_normalize
import json
from pymongo import MongoClient
import numpy as np
import pendulum
dag_name = 'DAILY_W_APPRAISALS_APPRAISAL_F'
description = 'Get data UNDERWRITING FROM APPRAISALS TO DWH'
underwriting_conn_id ='underwriting_appraisals'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.UNDERWRITING_APPRAISAL'
proc_name = 'F88DWH.PROC_W_APPRAISALS_APPRAISAL_F'
tags = ['Underwriting', 'Appraisals']
schedule_interval = '00 2 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
TODAY_PROC = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
YESTERDAY_PROC = TODAY_PROC - timedelta(days=1)
# test connect
def query(**context):
    # Định nghĩa múi giờ
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')

    # Lấy logical_date từ context và chuyển về múi giờ Asia/Ho_Chi_Minh
    logical_date = context['logical_date'].astimezone(tz)

    # Kiểm tra xem logical_date có trùng với ngày hiện tại không
    if logical_date.date() == pendulum.now(tz=tz).date():
        # Nếu trùng: YESTERDAY là ngày trước logical_date
        YESTERDAY = logical_date - timedelta(days=1)
    else:
        # Nếu không trùng: YESTERDAY là chính logical_date
        YESTERDAY = logical_date
    # TODAY là ngày sau YESTERDAY, đặt giờ về 00:00:00
    TODAY = (YESTERDAY + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    YESTERDAY = YESTERDAY.replace(hour=0, minute=0, second=0, microsecond=0)
    logging.info(f"Date: {TODAY}")
    # Chuyển ngày từ chuỗi sang datetime và đặt giờ về 00:00:00
    TODAY_PROC = TODAY
    YESTERDAY_PROC = YESTERDAY
    mongo_query = {
        "$or": [
            {
                "created_date": {
                    "$gte": YESTERDAY,
                    "$lt": TODAY
                }
            },
            {
                "modified_date": {
                    "$gte": YESTERDAY,
                    "$lt": TODAY
                }
            }
        ]
    }
    print(f"today:{TODAY} - yesterday:{YESTERDAY}")

    # Chuyển đổi truy vấn thành chuỗi JSON cho hook
    return mongo_query
def parse_value(x):
    if isinstance(x, dict):  # Đã là dictionary
        return x
    elif x is None:  # Nếu là None
        return {}
    else:
        print(f"Giá trị không hợp lệ: {x}")  # Ghi log nếu có giá trị bất thường
        return {}
def custom_underwiting_appraisal(df):
    data = df
    data = data.rename(columns=str.lower)  # Đổi tất cả tên cột thành chữ thuong
    datetime_columns = [
            'created_date', 'modified_date'
        ]
    # Duyệt qua từng cột ngày giờ và xử lý
    timestamp_value = pd.Timestamp('1970-01-01')
    for col in datetime_columns:
        if col in data.columns:
            # Kiểm tra nếu cột không phải kiểu datetime
            if not pd.api.types.is_datetime64_any_dtype(data[col]):
                # Chuyển đổi nếu cột không phải kiểu datetime
                data[col] = pd.to_datetime(data[col], errors='coerce')

            # Thay NaT bằng ngày mặc định (1970-01-01)
            data[col] = data[col].fillna(timestamp_value)
            data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')

            # Kiểm tra kiểu dữ liệu sau khi thay thế
            logging.info(f'Kiểu dữ liệu của cột {col} sau khi thay thế NaT: {data[col].dtype}')
            # In ra kết quả để kiểm tra

    # Kiểm tra và xử lý cột 'rule_set' (chuyển thành chuỗi JSON nếu là dictionary)
    if 'rule_set' in data.columns:
        print('Kiểm tra và xử lý cột rule_set')
        try:
            # Giải mã chuỗi JSON trong cột 'rule_set'
            request_data_json = data['rule_set'].apply(parse_value)

            # Chuyển JSON thành DataFrame phẳng
            request_data_normalized = json_normalize(request_data_json)

            # Kiểm tra các cột cần thiết có tồn tại không
            expected_cols = ['code', 'label', 'guide', 'status', 'assessment', 'note', 'items']
            missing_cols = [col for col in expected_cols if col not in request_data_normalized.columns]
            if missing_cols:
                raise Exception(f"Thiếu cột trong rule_set: {missing_cols}")

            # Giữ các cột cần thiết
            request_data_normalized = request_data_normalized[expected_cols]

            # Đổi tên cột
            request_data_normalized.rename(columns={
                'code': 'rule_set_code',
                'label': 'rule_set_label',
                'guide': 'rule_set_guide',
                'status': 'rule_set_status',
                'assessment': 'rule_set_assessment',
                'note': 'rule_set_note'
            }, inplace=True)

            # Nối dữ liệu vào DataFrame gốc
            data = pd.concat([data.reset_index(drop=True), request_data_normalized.reset_index(drop=True)], axis=1)

            print('Kiểm tra và xử lý cột rule_set.items')

            # Explode cột 'items' cấp 1
            data_exploded = data.explode('items').reset_index(drop=True)

            # Tạo cột mới cho các key trong 'items' cấp 1
            keys = ['code', 'label', 'required', 'status', 'type', 'complete_date']
            for key in keys:
                col_name = f'rule_set_items_{key}'
                data_exploded[col_name] = data_exploded['items'].apply(lambda x: x.get(key) if isinstance(x, dict) else None)
            
            print('Kiểm tra và xử lý cột rule_set.items.items')

            # Tạo cột riêng để giữ sub_items, KHÔNG ghi đè cột 'items'
            data_exploded['sub_items'] = data_exploded['items'].apply(lambda x: x.get('items', []) if isinstance(x, dict) else [])

            # Explode tiếp cột 'sub_items' cấp 2
            data_sub_exploded = data_exploded.explode('sub_items').reset_index(drop=True)

            # Tạo cột mới cho các key trong sub_items
            sub_keys = ['code', 'label', 'required', 'status', 'type', 'complete_date']
            for key in sub_keys:
                col_name = f'rule_set_items_items_{key}'
                data_sub_exploded[col_name] = data_sub_exploded['sub_items'].apply(lambda x: x.get(key) if isinstance(x, dict) else None)

            # Bỏ cột gốc 'items' và 'sub_items' nếu không cần
            data_sub_exploded.drop(columns=['items', 'sub_items'], inplace=True)

            # Cập nhật lại DataFrame gốc
            data = data_sub_exploded.copy()

        except Exception as e:
            print(f"Lỗi khi xử lý rule_set: {e}")

    if 'rule_auto' in data.columns:
        print('Kiểm tra và xử lý cột rule_auto')
        try:
            # Giải mã chuỗi JSON trong cột 'rule_auto'
            request_data_json = data['rule_auto'].apply(parse_value)

            # Chuyển JSON thành DataFrame phẳng
            request_data_normalized = json_normalize(request_data_json)

            # Kiểm tra các cột cần thiết có tồn tại không
            expected_cols = ['request', 'result', 'rule', 'status', 'message', 'error']
            missing_cols = [col for col in expected_cols if col not in request_data_normalized.columns]
            if missing_cols:
                raise Exception(f"Thiếu cột trong rule_auto: {missing_cols}")

            # Giữ các cột cần thiết
            request_data_normalized = request_data_normalized[expected_cols]

            # Đổi tên cột
            request_data_normalized.rename(columns={
                'status': 'rule_auto_status',
                'message': 'rule_auto_message',
                'error': 'rule_auto_error'
            }, inplace=True)

            # Nối dữ liệu vào DataFrame gốc
            data = pd.concat([data.reset_index(drop=True), request_data_normalized.reset_index(drop=True)], axis=1)

            print('Kiểm tra và xử lý cột rule_auto.request')

            # Explode cột 'request' cấp 1
            data_exploded = data.explode('request').reset_index(drop=True)

            # Tạo cột mới cho các key trong 'request' cấp 1
            keys = ['DifferentDays', 'RegistrationDate', 'IncomeProofCode', 'AssetCategoryCode', 'AssetCode', 'AssetName']
            for key in keys:
                col_name = f'rule_auto_request_{key}'
                data_exploded[col_name] = data_exploded['request'].apply(lambda x: x.get(key) if isinstance(x, dict) else None)
            
            print('Kiểm tra và xử lý cột rule_auto.result')
            # Explode cột 'result'
            data_exploded = data_exploded.explode('result').reset_index(drop=True)
            keys_result = ['AssetName', 'RegistrationDateResult']
            for key in keys_result:
                col_name = f'rule_auto_result_{key}'
                data_exploded[col_name] = data_exploded['result'].apply(lambda x: x.get(key) if isinstance(x, dict) else None)


            print('Kiểm tra và xử lý cột rule_auto.rule')
            # Explode cột 'rule'
            data_exploded = data_exploded.explode('rule').reset_index(drop=True)
            keys_rule = ['guide', 'parent_code', 'label', 'priority', 'status', 'required']
            for key in keys_rule:
                col_name = f'rule_auto_rule_{key}'
                data_exploded[col_name] = data_exploded['rule'].apply(lambda x: x.get(key) if isinstance(x, dict) else None)

            # Drop cột gốc nếu không cần
            data_exploded.drop(columns=['request', 'result', 'rule'], inplace=True)

            # Cập nhật lại DataFrame gốc
            data = data_exploded.copy()

        except Exception as e:
            print(f"Lỗi khi xử lý rule_auto: {e}")
    print(data.info())
    print(f"-------------------------------------------------")
    print(f"rename dataframe data")
    rename_mapping = {
        'id': 'ID',
        'is_deleted': 'IS_DELETED',
        'created_date': 'CREATED_DATE',
        'modified_date': 'MODIFIED_DATE',
        'created_by': 'CREATED_BY',
        'modified_by': 'MODIFIED_BY',
        'code_no': 'CODE_NO',
        'rule_set_code': 'RULE_SET_CODE',
        'rule_set_label': 'RULE_SET_LABEL',
        'rule_set_guide': 'RULE_SET_GUIDE',
        'rule_set_status': 'RULE_SET_STATUS',
        'rule_set_assessment': 'RULE_SET_ASSESSMENT',
        'rule_set_note': 'RULE_SET_NOTE',
        'rule_set_items_code': 'RULE_SET_ITEMS_CODE',
        'rule_set_items_label': 'RULE_SET_ITEMS_LABEL',
        'rule_set_items_required': 'RULE_SET_ITEMS_REQUIRED',
        'rule_set_items_status': 'RULE_SET_ITEMS_STATUS',
        'rule_set_items_type': 'RULE_SET_ITEMS_TYPE',
        'rule_set_items_complete_date': 'RULE_SET_ITEMS_COMPLETE_DT',
        'rule_set_items_items_code': 'RULE_SET_ITEMS_LV2_CODE',
        'rule_set_items_items_label': 'RULE_SET_ITEMS_LV2_LABEL',
        'rule_set_items_items_required': 'RULE_SET_ITEMS_LV2_REQUIRED',
        'rule_set_items_items_status': 'RULE_SET_ITEMS_LV2_STATUS',
        'rule_set_items_items_type': 'RULE_SET_ITEMS_LV2_TYPE',
        'rule_set_items_items_complete_date': 'RULE_SET_ITEMS_LV2_COMPLETE_DT',
        'rule_auto_request_DifferentDays': 'RULE_AUTO_REQ_DIFF_DAYS',
        'rule_auto_request_RegistrationDate': 'RULE_AUTO_REQ_REG_DT',
        'rule_auto_request_IncomeProofCode': 'RULE_AUTO_REQ_INCOMEPROOFCODE',
        'rule_auto_request_AssetCategoryCode': 'RULE_AUTO_REQ_ASSETCATEGORY_ID',
        'rule_auto_request_AssetCode': 'RULE_AUTO_REQ_ASSETCODE',
        'rule_auto_request_AssetName': 'RULE_AUTO_REQ_ASSETNAME',
        'rule_auto_result_AssetName': 'RULE_AUTO_RESULT_ASSETNAME',
        'rule_auto_result_RegistrationDateResult': 'RULE_AUTO_RESULT_REG_DT_RESULT',
        'rule_auto_rule_guide': 'RULE_AUTO_RULE_GUIDE',
        'rule_auto_rule_parent_code': 'RULE_AUTO_RULE_PARENT_CODE',
        'rule_auto_rule_label': 'RULE_AUTO_RULE_LABEL',
        'rule_auto_rule_priority': 'RULE_AUTO_RULE_PRIORITY',
        'rule_auto_rule_status': 'RULE_AUTO_RULE_STATUS',
        'rule_auto_rule_required': 'RULE_AUTO_RULE_REQUIRED',
        'rule_auto_status': 'RULE_AUTO_STATUS',
        'rule_auto_message': 'RULE_AUTO_MESSAGE',
        'rule_auto_error': 'RULE_AUTO_ERROR'
    }
    data.rename(columns=rename_mapping, inplace=True)
    return data

def ingestion(source_conn_id,target_conn_id, stg_tbl_name,  **context):
    # init value sql and fill param for quert
    # Get data from source
    # test connect
    appraisal_df = pd.DataFrame()

    try:
        hook = MongoHook(conn_id=source_conn_id)
        client = hook.get_conn()
        logging.info("connect mongodb underwiting susscess")
    except Exception as e:
        logging.error(str(e))
    try:
        try:
            db = client.underwriting
            collection = db.appraisals
        except Exception as e:
            print(str(e))
        # Thực hiện truy vấn find trên Collection
        results = collection.find(query(**context))
        logging.info("underwiting - results susscess")

    except Exception as e:
        logging.error(str(e))
    try:
        appraisal_df = pd.DataFrame(results)
        logging.info("underwiting - dataframe susscess")
    except Exception as e:
        logging.error(str(e))

    if not appraisal_df.empty:
        print(appraisal_df.dtypes)
    else:
        logging.error("Dataframe is empty, stopping process!")
    appraisal_df = appraisal_df.fillna('')
    # Assuming your DataFrame 
    appraisal_df.rename(columns={'_id': 'id'}, inplace=True)
    # Chuyển đổi ObjectId thành chuỗi nếu có
    for col in appraisal_df.columns:
        if appraisal_df[col].dtype == 'object':  # Nếu là kiểu object (bao gồm ObjectId)
            appraisal_df[col] = appraisal_df[col].apply(lambda x: str(x) if isinstance(x, ObjectId) else x)
        # Thay thế NaN bằng chuỗi rỗng
            appraisal_df[col] = appraisal_df[col].fillna("")
        elif np.issubdtype(appraisal_df[col].dtype, np.number):  # Cột kiểu số
            # Thay thế NaN bằng 0 cho cột số
            appraisal_df[col] = appraisal_df[col].fillna(0)
    # ingest data to staging layer of dwh
    write_df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id, df=custom_underwiting_appraisal(appraisal_df))
    logging.info('Get data source underwiting to stg done!')

def merge_transform(conn_id, proc_name, **context):
    today =int(YESTERDAY_PROC.strftime('%Y%m%d'))
    proc_name_with_p_date = f"{proc_name}({today})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)
with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                        python_callable=truncate_stg_tbl,
                                        op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
                                        provide_context=True)


    ingestion_underwriting_appraisal = PythonOperator(
        task_id='ingestion_underwriting_appraisal',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': underwriting_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )


    merge_data_stag_to_dwh_underwriting_appraisal = PythonOperator(
        task_id='merge_data_stage_to_dwh_underwriting_appraisal',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )
    #
#set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
truncate_stg_table >> ingestion_underwriting_appraisal >> merge_data_stag_to_dwh_underwriting_appraisal
