import pendulum
import datetime
import os
from airflow.operators.python import Python<PERSON><PERSON><PERSON>
from airflow.hooks.mysql_hook import MySqlHook
import datetime
import logging
import os
from airflow import DAG
from airflow.models import TaskInstance
import json
from DAGs.utils import merge_data_stag_to_dwh, truncate_stg_tbl, df_to_oracle
from airflow.providers.oracle.hooks.oracle import OracleHook

DAG_ID = 'GL_ACCT_TRANS_HIS'
parent_dir = os.path.dirname(os.path.abspath(__file__))
query_file = os.path.join(parent_dir, 'queries', f'{DAG_ID}.sql')
start_date = datetime.datetime(2024, 4, 5)
schedule_interval = '0 5 * * *'
sql = open(query_file, "r", encoding='utf-8').read()
STG_TBL_NAME = "stgprod.mifos_gl_acct_transactions_his"
PROC_STG_DWH = "F88DWH.PROC_W_GL_ACCT_TRANS_ODS_HIS_F"
mifos_conn_id = "mysql_mifos"
oracle_conn_id = "oracle_f88_dwh"


def set_params(today: datetime.datetime):
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')
    today = today.astimezone(tz)
    yesterday = today - datetime.timedelta(1)
    four_thirty_today = today.replace(hour=4, minute=30, second=0, microsecond=0)
    four_thirty_yesterday = yesterday.replace(hour=4, minute=30, second=0, microsecond=0)
    params = {}
    params.update({'FOUR_THIRTY_TODAY': four_thirty_today.strftime("%Y-%m-%d %H:%M:%S")})
    params.update({'FOUR_THIRTY_YESTERDAY': four_thirty_yesterday.strftime("%Y-%m-%d %H:%M:%S")})
    return params


def validate_params(**kwargs):
    date_wid_cfg = ""
    date_wid_cfg = kwargs['dag_run'].conf.get('date_wid')
    date_wid = ""
    if date_wid_cfg:
        logging.info("Trigger by config...")
        logging.info(f"date_wid: {date_wid_cfg}")
        date_wid = datetime.datetime.strptime(date_wid_cfg, "%Y-%m-%d")
        params = set_params(date_wid)
    else:
        logging.info("Performing incremental load...")
        params = set_params(datetime.datetime.now())

    logging.info(f'Params: {params}')
    kwargs['ti'].xcom_push(key='params', value=params)


def extract_load_oracle(table_name: str, oracle_conn_id: str, **context):
    mysqlHook = MySqlHook(mysql_conn_id=mifos_conn_id)
    parameters = context['ti'].xcom_pull(key='params', task_ids='validate_params')
    sql_statement = sql.format(**parameters)
    df = mysqlHook.get_pandas_df(sql=sql_statement)
    df['ACCOUNTED_DR'] = df['ACCOUNTED_DR'].astype(str)
    df['ACCOUNTED_CR'] = df['ACCOUNTED_CR'].astype(str)
    df_to_oracle(table_name=table_name, oracle_conn_id=oracle_conn_id, df=df)


with DAG(dag_id=DAG_ID,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'none_skipped'
         },
         start_date=start_date,
         catchup=False,
         schedule_interval=schedule_interval,
         max_active_runs=1
         ) as dag:
    validate_params = PythonOperator(task_id='validate_params',
                                     python_callable=validate_params,
                                     provide_context=True)

    truncate_stg_mifos_gl_acct_transactions_his = PythonOperator(task_id='truncate_stg_mifos_gl_acct_transactions_his',
                                                                 python_callable=truncate_stg_tbl,
                                                                 op_kwargs={'tbl_name': STG_TBL_NAME,
                                                                            'conn_id': oracle_conn_id},
                                                                 provide_context=True)
    get_data_from_mifos_gl_acct_trans_ods_his = PythonOperator(task_id='get_data_from_mifos_gl_acct_trans_ods_his',
                                                               python_callable=extract_load_oracle,
                                                               op_kwargs={'table_name': STG_TBL_NAME,
                                                                          'oracle_conn_id': oracle_conn_id},
                                                               provide_context=True)

    ingest_data_from_stg_to_dwh = PythonOperator(
        task_id='ingest_data_from_stg_to_dwh',
        python_callable=merge_data_stag_to_dwh,
        op_kwargs={'proc_name': PROC_STG_DWH, 'conn_id': oracle_conn_id},
        provide_context=True,
    )

validate_params >> truncate_stg_mifos_gl_acct_transactions_his >> get_data_from_mifos_gl_acct_trans_ods_his >> ingest_data_from_stg_to_dwh
