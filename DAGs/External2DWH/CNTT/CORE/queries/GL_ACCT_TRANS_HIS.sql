select
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.id as ID ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.imported_at as IMPORTED_AT ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.last_modified_date as LAST_MODIFIED_DATE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.line_id as LINE_ID ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.accounting_date as ACCOUNTING_DATE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.transaction_date as TRANSACTION_DATE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.source_system as SOURCE_SYSTEM ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.source_id as SOURCE_ID ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.source_name as SOURCE_NAME ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.transaction_type as TRANSACTION_TYPE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.document_num as DOCUMENT_NUM ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.customer_code as CUSTOMER_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.customer_name as CUSTOMER_NAME ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.asset_code as ASSET_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.asset_name as ASSET_NAME ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.concatsegment as CONCATSEGMENT ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.branch_code as BRANCH_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.dept_code as DEPT_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.shop_code as SHOP_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.account_code as ACCOUNT_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.subaccount_code as SUBACCOUNT_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.product_code as PRODUCT_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.chanel_code as CHANEL_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.dept_budget as DEPT_BUDGET ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.internal_branch as INTERNAL_BRANCH ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.reserve_10 as RESERVE_10 ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.reserve_11 as RESERVE_11 ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.accounted_dr as ACCOUNTED_DR ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.accounted_cr as ACCOUNTED_CR ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.status as STATUS ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.accounted_gl as ACCOUNTED_GL ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.description as DESCRIPTION ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.create_by as CREATE_BY ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.loan_id as LOAN_ID ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.loan_code as LOAN_CODE ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.cash_flow as CASH_FLOW ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.receipt_method as RECEIPT_METHOD ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.customer_id as CUSTOMER_ID ,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.line_group as LINE_GROUP,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.BATCH ,
    GL_ACCT_TRANSACTIONS_ODS_HISTORY.INVOICE_NUMBER,
 	GL_ACCT_TRANSACTIONS_ODS_HISTORY.INVOICE_DATE,
 	GL_ACCT_TRANSACTIONS_ODS_HISTORY.INVOICE_CODE,
 	GL_ACCT_TRANSACTIONS_ODS_HISTORY.SELLER_NAME,
 	GL_ACCT_TRANSACTIONS_ODS_HISTORY.TAX_CODE,
 	GL_ACCT_TRANSACTIONS_ODS_HISTORY.TAX_PERCENT,
 	GL_ACCT_TRANSACTIONS_ODS_HISTORY.CODE_MASTER_DATA,
	GL_ACCT_TRANSACTIONS_ODS_HISTORY.POSTED_DATE
from
	`mifostenant-default`.gl_acct_transactions_ods_history as GL_ACCT_TRANSACTIONS_ODS_HISTORY
where
	(GL_ACCT_TRANSACTIONS_ODS_HISTORY.last_modified_date >= "{FOUR_THIRTY_YESTERDAY}"
		and GL_ACCT_TRANSACTIONS_ODS_HISTORY.last_modified_date < "{FOUR_THIRTY_TODAY}")
