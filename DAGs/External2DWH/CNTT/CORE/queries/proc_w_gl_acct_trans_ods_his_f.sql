CREATE
TABLE
STGPROD.GL_ACCT_TRANS_HIS_DEL
(
    LINE_ID                   NUMBER,
SOURCE_ID                 NUMBER,
SOURCE_NAME               VARCHAR2(100),
SOURCE
VARCHAR2(50)
);

CREATE
OR
REPLACE
PROCEDURE
F88DWH.PROC_W_GL_ACCT_TRANS_ODS_HIS_F
AS
/ *
-- Author: QUANNV2
-- Created: 0
8 / 04 / 2024
-- Purpose: Phuc
vu
GL_ACCT_TRANS_ODS_HISTORY
* /
BEGIN

-- Delete
ban
ghi
EXECUTE
IMMEDIATE
'TRUNCATE TABLE STGPROD.GL_ACCT_TRANS_HIS_DEL';

INSERT
INTO
STGPROD.GL_ACCT_TRANS_HIS_DEL(LINE_ID, SOURCE_ID, SOURCE_NAME, SOURCE)
SELECT
GL_STG.LINE_ID, GL_STG.SOURCE_ID, GL_STG.SOURCE_NAME, 'MIFOS'
SOURCE
FROM
F88DWH.W_GL_ACCT_TRANSACTIONS_ODS_F
GL_DWH
JOIN
STGPROD.MIFOS_GL_ACCT_TRANSACTIONS_HIS
GL_STG
ON
GL_DWH.LINE_ID = GL_STG.LINE_ID
AND
GL_DWH.SOURCE_ID = GL_STG.SOURCE_ID
AND
GL_DWH.SOURCE_NAME = GL_STG.SOURCE_NAME
WHERE
SOURCE = 'MIFOS'
;

COMMIT;

DELETE
FROM
F88DWH.W_GL_ACCT_TRANSACTIONS_ODS_F
GL_DWH
WHERE
EXISTS(SELECT
1
FROM
STGPROD.GL_ACCT_TRANS_HIS_DEL
GL_STG
WHERE
GL_DWH.LINE_ID = GL_STG.LINE_ID
AND
GL_DWH.SOURCE_ID = GL_STG.SOURCE_ID
AND
GL_DWH.SOURCE_NAME = GL_STG.SOURCE_NAME
AND
GL_DWH.SOURCE = GL_STG.SOURCE)
;
COMMIT;
-- Insert
ban
ghi
tu
2
nguon
ERP, MIFOS
INSERT
INTO
F88DWH.W_GL_ACCT_TRANSACTIONS_ODS_F
(
    DATE_WID,
    YEAR_NUM,
    MONTH_NUM,
    ID,
    IMPORTED_AT,
    LAST_MODIFIED_DATE,
    LINE_ID,
    ACCOUNTING_DATE,
    --        TRANSACTION_DATE,
    SOURCE_SYSTEM,
    SOURCE_ID,
    SOURCE_NAME,
    TRANSACTION_TYPE,
    LINE_GROUP,
    DOCUMENT_NUM,
    CUSTOMER_ID,
    CUSTOMER_CODE,
    CUSTOMER_NAME,
    ASSET_CODE,
    ASSET_NAME,
    CONCATSEGMENT,
    BRANCH_CODE,
    DEPT_CODE,
    SHOP_CODE,
    ACCOUNT_CODE,
    SUBACCOUNT_CODE,
    PRODUCT_CODE,
    CHANEL_CODE,
    DEPT_BUDGET,
    INTERNAL_BRANCH,
    RESERVE_10,
    RESERVE_11,
    ACCOUNTED_DR,
    ACCOUNTED_CR,
    STATUS,
    ACCOUNTED_GL,
    DESCRIPTION,
    CREATE_BY,
    LOAN_ID,
    LOAN_CODE,
    CASH_FLOW,
    RECEIPT_METHOD,
    BATCH,
    INVOICE_NUMBER,
    INVOICE_DATE,
    INVOICE_CODE,
    SELLER_NAME,
    TAX_CODE,
    TAX_PERCENT,
    CODE_MASTER_DATA,
    POSTED_DATE,
    SOURCE
)

SELECT
(TO_CHAR(MIFOS_GL_ACCT_TRANSACTIONS_HIS.ACCOUNTING_DATE, 'YYYYMMDD'))
DATE_WID,
(TO_CHAR(MIFOS_GL_ACCT_TRANSACTIONS_HIS.ACCOUNTING_DATE, 'YYYY'))
YEAR_NUM,
(TO_CHAR(MIFOS_GL_ACCT_TRANSACTIONS_HIS.ACCOUNTING_DATE, 'MM'))
MONTH_NUM,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.ID,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.IMPORTED_AT,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.LAST_MODIFIED_DATE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.LINE_ID,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.ACCOUNTING_DATE,
--      MIFOS_GL_ACCT_TRANSACTIONS_HIS.TRANSACTION_DATE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.SOURCE_SYSTEM,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.SOURCE_ID,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.SOURCE_NAME,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.TRANSACTION_TYPE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.LINE_GROUP,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.DOCUMENT_NUM,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CUSTOMER_ID,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CUSTOMER_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CUSTOMER_NAME,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.ASSET_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.ASSET_NAME,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CONCATSEGMENT,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.BRANCH_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.DEPT_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.SHOP_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.ACCOUNT_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.SUBACCOUNT_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.PRODUCT_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CHANEL_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.DEPT_BUDGET,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.INTERNAL_BRANCH,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.RESERVE_10,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.RESERVE_11,
CASE
WHEN
REGEXP_REPLACE(ACCOUNTED_DR, '^nan$')
IS
NULL
THEN
NULL
ELSE
TO_NUMBER(ACCOUNTED_DR)
END
AS
ACCOUNTED_DR,
CASE
WHEN
REGEXP_REPLACE(ACCOUNTED_CR, '^nan$')
IS
NULL
THEN
NULL
ELSE
TO_NUMBER(ACCOUNTED_CR)
END
AS
ACCOUNTED_CR,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.STATUS,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.ACCOUNTED_GL,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.DESCRIPTION,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CREATE_BY,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.LOAN_ID,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.LOAN_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CASH_FLOW,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.RECEIPT_METHOD,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.BATCH,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.INVOICE_NUMBER,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.INVOICE_DATE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.INVOICE_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.SELLER_NAME,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.TAX_CODE,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.TAX_PERCENT,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.CODE_MASTER_DATA,
MIFOS_GL_ACCT_TRANSACTIONS_HIS.POSTED_DATE,
'MIFOS'
FROM
STGPROD.MIFOS_GL_ACCT_TRANSACTIONS_HIS
MIFOS_GL_ACCT_TRANSACTIONS_HIS
;

COMMIT;

END
PROC_W_GL_ACCT_TRANS_ODS_HIS_F;