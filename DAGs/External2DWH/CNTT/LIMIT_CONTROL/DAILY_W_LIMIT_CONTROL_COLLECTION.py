from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMonitor, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh, write_df_to_oracle
import os
import logging
from datetime import datetime, timedelta
from bson import ObjectId
import pandas as pd
from pandas import json_normalize
import json
from pymongo import MongoClient
import numpy as np
import pendulum
dag_name = 'DAILY_W_LIMIT_CONTROL_COLLECTION'
description = 'Get data CALL DETAIL FROM LIMIT CONTROL TO DWH'
notify_conn_id = 'prod_limit_control_db'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.LIMIT_CONTROL_COLLECTION'
proc_name = 'F88DWH.PROC_W_LIMIT_CT_COL_F'
#######
tags = ['limit', 'limit control']
schedule_interval = '45 1 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
TODAY_PROC = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
YESTERDAY_PROC = TODAY_PROC - timedelta(days=1)
# test connect


def query(**context):
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')
    logical_date_tz = context['logical_date'].astimezone(tz)
    logical_date = context['logical_date']

    if logical_date_tz.date() == pendulum.now(tz=tz).date():
        target_date = (logical_date - timedelta(days=1)).date()
    else:
        target_date = logical_date.date()

    start_time = datetime.combine(target_date, datetime.min.time())
    end_time = datetime.combine(
        target_date, datetime.max.time()).replace(microsecond=999000)

    # ✅ Quan trọng: bổ sung match theo ngày ngay trong SubLitmits
    mongo_query = {
        "$or": [
            {"CreatedDate":  {"$gte": start_time, "$lt": end_time}},
            {"ModifiedDate": {"$gte": start_time, "$lt": end_time}},
            {"SecureLitmit.SubLitmits": {
                "$elemMatch": {
                    "$or": [
                        {"CreatedDate":  {"$gte": start_time, "$lt": end_time}},
                        {"ModifiedDate": {"$gte": start_time, "$lt": end_time}}
                    ]
                }
            }}
        ]
    }

    logging.info("=== MongoDB Query Builder ===")
    logging.info(f"Airflow logical_date (UTC): {logical_date.isoformat()}Z")
    logging.info(f"Target date for Mongo query: {target_date}")
    logging.info(f"Query FROM: {start_time.isoformat()}Z")
    logging.info(f"Query TO  : {end_time.isoformat()}Z")
    logging.info(f"Mongo Query: {mongo_query}")

    return mongo_query


def parse_value(x):
    if isinstance(x, dict):  # Đã là dictionary
        return x
    elif x is None:  # Nếu là None
        return {}
    else:
        # Ghi log nếu có giá trị bất thường
        print(f"Giá trị không hợp lệ: {x}")
        return {}


def parse_datetime_safe(x):
    try:
        if not x or str(x).startswith("0001"):
            return None
        return pd.to_datetime(x)
    except Exception:
        return None


def custom_app_my88(df):
    """
    Fixed version - đảm bảo mỗi SubLimit tạo ra 1 record riêng biệt
    """
    data = df
    # Đổi tất cả tên cột thành chữ thường
    data = data.rename(columns=str.lower)

    datetime_columns = [
        'birthday', 'identitydate', 'lastchangepasswordat', 'activeat',
        'create_date', 'modify_date', 'createddate', 'modifieddate', 'repaymentsstartingfromdate'
    ]

    # Xử lý datetime columns
    timestamp_value = pd.Timestamp('1970-01-01')
    for col in datetime_columns:
        if col in data.columns:
            if not pd.api.types.is_datetime64_any_dtype(data[col]):
                data[col] = pd.to_datetime(data[col], errors='coerce')
            data[col] = data[col].fillna(timestamp_value)
            data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')
            logging.info(
                f'Kiểu dữ liệu của cột {col} sau khi thay thế NaT: {data[col].dtype}')

    # ✅ FIX CHÍNH: Xử lý SecureLitmit và SubLimits - TẠO NHIỀU RECORDS
    if 'securelitmit' in data.columns:
        logging.info("=== FIXED VERSION: xu ly securelimit ===")
        try:
            # Parse JSON nếu cần
            request_data_json = data['securelitmit'].apply(
                lambda x: x if isinstance(x, dict) else (
                    json.loads(x) if isinstance(x, str) else {})
            )

            # Tạo danh sách để chứa các record được tách ra
            expanded_records = []

            # Lặp qua từng row trong DataFrame gốc
            for idx, row in data.iterrows():
                securelimit = request_data_json.iloc[idx]
                sublist = securelimit.get('SubLitmits', []) if isinstance(
                    securelimit, dict) else []

                # Debug log chi tiết
                cif_code = str(row.get('cifcode', ''))
                logging.info(f"Processing CIF: {cif_code}, Row index: {idx}")
                logging.info(
                    f"SubLitmits count: {len(sublist) if isinstance(sublist, list) else 0}")

                # ✅ KEY FIX: Đảm bảo TẤT CẢ SubLimits được xử lý thành records riêng biệt
                if isinstance(sublist, list) and len(sublist) > 0:
                    logging.info(
                        f"  -> Expanding {len(sublist)} SubLimits for CIF {cif_code}")

                    for item_idx, item in enumerate(sublist):
                        # Tạo bản copy hoàn toàn mới của row gốc cho mỗi SubLimit
                        new_row = row.copy().to_dict() if hasattr(row, 'to_dict') else dict(row)

                        # Thêm thông tin từ SubLimits item
                        new_row['assetcode'] = item.get('AssetCode')
                        new_row['pricemax'] = item.get('PriceMax')
                        new_row['ltv'] = item.get('LTV')
                        new_row['categorycode'] = item.get('CategoryCode')
                        new_row['assetcategorycode'] = item.get(
                            'AssetCategoryCode')
                        new_row['usedsublimitamount'] = item.get(
                            'UsedSublimitAmount')
                        new_row['avaiablesublimitamount'] = item.get(
                            'AvaiableSublimitAmount')
                        new_row['isassetblacklist'] = item.get(
                            'IsAssetBlackList')
                        new_row['isborrowmore'] = item.get('IsBorrowMore')
                        new_row['isstore'] = item.get('IsStore')
                        new_row['subidentifycode'] = item.get(
                            'SubIdentifyCode')
                        new_row['onlinelimit'] = item.get('OnlineLimit')
                        new_row['holdinglimitamount'] = item.get(
                            'HoldingLimitAmount')
                        new_row['avaiablesublimitamountlast'] = item.get(
                            'AvaiableSublimitAmountLast')
                        new_row['manualavailablelimit'] = item.get(
                            'ManualAvailableLimit')
                        new_row['manualpricemax'] = item.get('ManualPriceMax')
                        new_row['autoexport'] = item.get('AutoExport')
                        new_row['spreadamount'] = item.get('SpreadAmount')
                        new_row['priceafterattr'] = item.get('PriceAfterAttr')
                        new_row['priceappraisal'] = item.get('PriceAppraisal')

                        # Xử lý datetime cho SubLimit
                        sublimit_created = item.get('CreatedDate')
                        sublimit_modified = item.get('ModifiedDate')

                        if sublimit_created:
                            try:
                                if isinstance(sublimit_created, str):
                                    new_row['sublimit_created_date'] = pd.to_datetime(
                                        sublimit_created).strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    new_row['sublimit_created_date'] = sublimit_created.strftime(
                                        '%Y-%m-%d %H:%M:%S') if hasattr(sublimit_created, 'strftime') else str(sublimit_created)
                            except:
                                new_row['sublimit_created_date'] = None
                        else:
                            new_row['sublimit_created_date'] = None

                        if sublimit_modified:
                            try:
                                if isinstance(sublimit_modified, str):
                                    new_row['sublimit_modified_date'] = pd.to_datetime(
                                        sublimit_modified).strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    new_row['sublimit_modified_date'] = sublimit_modified.strftime(
                                        '%Y-%m-%d %H:%M:%S') if hasattr(sublimit_modified, 'strftime') else str(sublimit_modified)
                            except:
                                new_row['sublimit_modified_date'] = None
                        else:
                            new_row['sublimit_modified_date'] = None

                        new_row['sublimit_status'] = item.get('Status')
                        new_row['sublimit_item_index'] = item_idx
                        new_row['sublimit_createdby'] = item.get('CreatedBy')
                        new_row['sublimit_modifiedby'] = item.get('ModifiedBy')

                        # Log chi tiết cho từng SubLimit
                        logging.info(
                            f"    SubLimit {item_idx}: AssetCode={item.get('AssetCode')}, PriceMax={item.get('PriceMax')}")
                        if cif_code == '2314287134':
                            logging.info(
                                f"    *** CIF 2314287134 SubLimit {item_idx} details:")
                            logging.info(
                                f"        AssetCode: {item.get('AssetCode')}")
                            logging.info(
                                f"        ModifiedDate: {sublimit_modified}")
                            logging.info(
                                f"        Status: {item.get('Status')}")

                        expanded_records.append(new_row)

                else:
                    # Nếu không có SubLitmits, tạo record với các giá trị SubLimit = None
                    logging.info(
                        f"  -> No SubLimits found for CIF {cif_code}, creating record with NULL SubLimit fields")
                    new_row = row.copy().to_dict() if hasattr(row, 'to_dict') else dict(row)

                    # Thêm tất cả các trường SubLimit với giá trị None
                    sublimit_fields = [
                        'assetcode', 'pricemax', 'ltv', 'categorycode', 'assetcategorycode',
                        'usedsublimitamount', 'avaiablesublimitamount', 'isassetblacklist',
                        'isborrowmore', 'isstore', 'subidentifycode', 'onlinelimit',
                        'holdinglimitamount', 'avaiablesublimitamountlast', 'manualavailablelimit',
                        'manualpricemax', 'autoexport', 'spreadamount', 'priceafterattr', 'priceappraisal',
                        'sublimit_created_date', 'sublimit_modified_date', 'sublimit_status',
                        'sublimit_item_index', 'sublimit_createdby', 'sublimit_modifiedby'
                    ]

                    for field in sublimit_fields:
                        new_row[field] = None

                    expanded_records.append(new_row)

            # Tạo DataFrame mới từ danh sách expanded_records
            if expanded_records:
                data = pd.DataFrame(expanded_records).reset_index(drop=True)
                logging.info(
                    f"✅ SUCCESS: Expanded data from {len(df)} to {len(data)} records")

                # Kiểm tra kết quả cho CIF 2314287134
                if 'cifcode' in data.columns:
                    cif_data = data[data['cifcode'] == '2314287134']
                    if not cif_data.empty:
                        logging.info(f"=== RESULT for CIF 2314287134 ===")
                        logging.info(f"Total records: {len(cif_data)}")

                        asset_codes = cif_data['assetcode'].unique()
                        logging.info(f"Unique AssetCodes: {list(asset_codes)}")

                        for asset_code in asset_codes:
                            if asset_code is not None:
                                asset_records = cif_data[cif_data['assetcode']
                                                         == asset_code]
                                logging.info(
                                    f"  AssetCode {asset_code}: {len(asset_records)} records")
                                for _, record in asset_records.iterrows():
                                    logging.info(
                                        f"    PriceMax: {record.get('pricemax')}, ModifiedDate: {record.get('sublimit_modified_date')}")

                # Sample log
                logging.info("Sample expanded data:")
                sample_cols = ['id', 'cifcode', 'assetcode', 'pricemax']
                available_cols = [
                    col for col in sample_cols if col in data.columns]
                if len(data) > 0 and available_cols:
                    logging.info(data[available_cols].head(10))
            else:
                # Fallback: tạo DataFrame rỗng với các cột cần thiết
                logging.warning("No expanded records created, using fallback")
                sublimit_fields = [
                    'assetcode', 'pricemax', 'ltv', 'categorycode', 'assetcategorycode',
                    'usedsublimitamount', 'avaiablesublimitamount', 'isassetblacklist',
                    'isborrowmore', 'isstore', 'subidentifycode', 'onlinelimit',
                    'holdinglimitamount', 'avaiablesublimitamountlast', 'manualavailablelimit',
                    'manualpricemax', 'autoexport', 'spreadamount', 'priceafterattr', 'priceappraisal',
                    'sublimit_created_date', 'sublimit_modified_date', 'sublimit_status',
                    'sublimit_item_index', 'sublimit_createdby', 'sublimit_modifiedby'
                ]

                for field in sublimit_fields:
                    data[field] = None

        except Exception as e:
            logging.error(f"❌ ERROR khi xử lý securelitmit: {e}")
            import traceback
            logging.error(f"Stack trace: {traceback.format_exc()}")

            # Fallback: thêm cột mặc định nếu có lỗi
            sublimit_fields = [
                'assetcode', 'pricemax', 'ltv', 'categorycode', 'assetcategorycode',
                'usedsublimitamount', 'avaiablesublimitamount', 'isassetblacklist',
                'isborrowmore', 'isstore', 'subidentifycode', 'onlinelimit',
                'holdinglimitamount', 'avaiablesublimitamountlast', 'manualavailablelimit',
                'manualpricemax', 'autoexport', 'spreadamount', 'priceafterattr', 'priceappraisal',
                'sublimit_created_date', 'sublimit_modified_date', 'sublimit_status',
                'sublimit_item_index', 'sublimit_createdby', 'sublimit_modifiedby'
            ]

            for field in sublimit_fields:
                data[field] = None

            raise  # Re-raise exception để debug

    return data


def ingestion(source_conn_id, target_conn_id, stg_tbl_name, **context):
    """
    Fixed version của hàm ingestion
    """
    # Khởi tạo biến để tránh UnboundLocalError
    notify_df = pd.DataFrame()
    results = []

    try:
        # Kết nối MongoDB
        logging.info("=== CONNECTING TO MONGODB ===")
        hook = MongoHook(conn_id=source_conn_id)
        client = hook.get_conn()
        logging.info("✅ MongoDB connection successful")

        # Lấy database và collection
        db = client.LimitControl
        collection = db.LimitCollection
        logging.info("✅ Got collection successfully")

        # Thực hiện truy vấn find trên Collection
        mongo_query = query(**context)
        results = list(collection.find(mongo_query))
        logging.info(
            f"✅ Query executed successfully, found {len(results)} records")

        # Tạo DataFrame
        notify_df = pd.DataFrame(results)
        logging.info("✅ DataFrame created successfully")

    except Exception as e:
        logging.error(f"❌ Error in MongoDB operations: {str(e)}")
        # Tạo DataFrame rỗng nếu có lỗi để tránh crash
        notify_df = pd.DataFrame()

    # Kiểm tra nếu DataFrame rỗng
    if notify_df.empty:
        logging.warning(
            "⚠️  No data found or error occurred. Creating empty DataFrame.")
        return

    try:
        logging.info(f"📊 DataFrame shape: {notify_df.shape}")
        logging.info(f"📊 DataFrame dtypes: {notify_df.dtypes}")

        # Fill NaN values
        notify_df = notify_df.fillna('')

        # Rename columns
        notify_df.rename(
            columns={'type': 'type_name', '_id': 'id'}, inplace=True)

        # Chuyển đổi ObjectId thành chuỗi nếu có
        for col in notify_df.columns:
            if notify_df[col].dtype == 'object':
                notify_df[col] = notify_df[col].apply(
                    lambda x: str(x) if isinstance(x, ObjectId) else x)
                notify_df[col] = notify_df[col].fillna("")
            elif np.issubdtype(notify_df[col].dtype, np.number):
                notify_df[col] = notify_df[col].fillna(0)

        # ✅ SỬ DỤNG HÀMIN FIXED
        logging.info("=== PROCESSING DATA WITH FIXED FUNCTION ===")
        processed_df = custom_app_my88(
            notify_df)

        # Kiểm tra kết quả cuối cùng
        logging.info(
            f"📊 Final processed DataFrame shape: {processed_df.shape}")
        if 'assetcode' in processed_df.columns:
            unique_assets = processed_df['assetcode'].value_counts()
            logging.info(f"📊 AssetCode distribution: {dict(unique_assets)}")

        write_df_to_oracle(table_name=stg_tbl_name,
                           oracle_conn_id=target_conn_id, df=processed_df)
        logging.info('✅ Get data source to stg done!')

    except Exception as e:
        logging.error(f"❌ Error in data processing: {str(e)}")
        import traceback
        logging.error(f"Stack trace: {traceback.format_exc()}")
        raise


def merge_transform(conn_id, proc_name, **context):
    today = int(YESTERDAY_PROC.strftime('%Y%m%d'))
    proc_name_with_p_date = f"{proc_name}({today})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)


with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                        python_callable=truncate_stg_tbl,
                                        op_kwargs={'tbl_name': stg_table,
                                                   'conn_id': oracle_conn_id},
                                        provide_context=True)

    ingestion_app_my88_pawn = PythonOperator(
        task_id='ingestion_limit_collection',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': notify_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )

    merge_data_stag_to_dwh_app_my88_pawn = PythonOperator(
        task_id='merge_data_stag_to_dwh_limit_collection',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )
    #
# set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
truncate_stg_table >> ingestion_app_my88_pawn >> merge_data_stag_to_dwh_app_my88_pawn
