from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMonitor, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh,write_df_to_oracle
import os
import logging
from datetime import datetime, timedelta
from bson import ObjectId
import pandas as pd
from pandas import json_normalize
import json
from pymongo import MongoClient
import numpy as np
import pendulum
dag_name = 'DAILY_W_LIMIT_CONTROL_HISTORY'
description = 'Get data CALL DETAIL FROM LIMIT CONTROL HISTORY TO DWH'
notify_conn_id = 'prod_limit_control_db'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.MONGODB_LIMITHISTORY'
proc_name = 'F88DWH.PROC_W_MONGODB_LIMITHISTORY_F'
#######
tags = ['limit','limit control']
schedule_interval = '50 7 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
TODAY_PROC = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
YESTERDAY_PROC = TODAY_PROC - timedelta(days=1)
# test connect
def query(**context):
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')
    logical_date_tz = context['logical_date'].astimezone(tz)
    logical_date = context['logical_date']  # kiểu datetime.datetime
    if logical_date_tz.date() == pendulum.now(tz=tz).date():
        target_date = logical_date - timedelta(days=1).date()
    else:
        target_date = logical_date
    
    # Dải thời gian từ 00:00:00 đến 23:59:59.999 (UTC)
    start_time = datetime.combine(target_date, datetime.min.time())
    end_time = datetime.combine(target_date, datetime.max.time()).replace(microsecond=999000)
   
    mongo_query = {
    "ModifyDate": {
        "$gte": start_time,
        "$lt": end_time
        }
    }
    logging.info("=== MongoDB Query Builder ===")
    logging.info(f"Airflow logical_date (UTC): {logical_date.isoformat()}Z")
    logging.info(f"Target date for Mongo query: {target_date}")
    logging.info(f"Query FROM: {start_time.isoformat()}Z")
    logging.info(f"Query TO  : {end_time.isoformat()}Z")
    logging.info(f"Mongo Query: {mongo_query}")

    # Chuyển đổi truy vấn thành chuỗi JSON cho hook
    return mongo_query
def parse_value(x):
    try:
        if isinstance(x, str):
            x = json.loads(x)
            if isinstance(x, str):  # Nếu lớp đầu vẫn là chuỗi → parse tiếp
                x = json.loads(x)
        return x
    except Exception as e:
        print(f"[!] Lỗi khi parse JSON: {e}")
        return {}
def sync_sublimit_with_securelimit(x):
    try:
        secure = x.get('SecureLitmit', {})
        avaiable_amt = secure.get('AvaiableSecureLitmitAmount', None)
        sublimits = secure.get('SubLitmits', [])
        if avaiable_amt is not None and isinstance(sublimits, list):
            for sub in sublimits:
                sub['AvaiableSublimitAmount'] = avaiable_amt
        secure['SubLitmits'] = sublimits
        x['SecureLitmit'] = secure
        return x
    except Exception:
        return x

def get_latest_sublimit(subs):
    try:
        if not isinstance(subs, list) or not subs:
            return None
        subs = [s for s in subs if s.get('ModifiedDate')]
        subs.sort(key=lambda x: x['ModifiedDate'], reverse=True)
        return subs[0]
    except Exception:
        return None
def custom_app_my88(df):
    data = df
    data = data.rename(columns=str.lower)  # Đổi tất cả tên cột thành chữ thường
    datetime_columns = [
            'birthday', 'identitydate', 'lastchangepasswordat', 'activeat',
            'create_date', 'modify_date', 'createddate', 'modifieddate','repaymentsstartingfromdate'
        ]
    # Duyệt qua từng cột ngày giờ và xử lý
    timestamp_value = pd.Timestamp('1970-01-01')
    for col in datetime_columns:
        if col in data.columns:
            # Kiểm tra nếu cột không phải kiểu datetime
            if not pd.api.types.is_datetime64_any_dtype(data[col]):
                # Chuyển đổi nếu cột không phải kiểu datetime
                data[col] = pd.to_datetime(data[col], errors='coerce')

            # Thay NaT bằng ngày mặc định (1970-01-01)
            data[col] = data[col].fillna(timestamp_value)
            data[col] = data[col].dt.strftime('%Y-%m-%d %H:%M:%S')

            # Kiểm tra kiểu dữ liệu sau khi thay thế
            logging.info(f'Kiểu dữ liệu của cột {col} sau khi thay thế NaT: {data[col].dtype}')
            # In ra kết quả để kiểm tra

    # Kiểm tra và xử lý cột 'securelitmit' (chuyển thành chuỗi JSON nếu là dictionary)
     # Parse requesttransaction
        if 'requesttransaction' in data.columns:
            try:
                request_data_json = data['requesttransaction'].apply(parse_value)
                request_data_normalized = json_normalize(request_data_json)
                request_data_normalized = request_data_normalized[['CodeNo', 'LoanId']]
                request_data_normalized.rename(columns={
                    'CodeNo': 'requesttransaction_codeno',
                    'LoanId': 'requesttransaction_loanid'
                }, inplace=True)
                data = pd.concat([data, request_data_normalized], axis=1)
            except Exception as e:
                print(f"Lỗi khi xử lý RequestTransaction: {e}")

        # Normalize DataOld
        if 'dataold' in data.columns:
            try:
                parsed_dataold = data['dataold'].apply(parse_value)

                def extract_dataold_fields(x):
                    result = {}
                    try:
                        secure = x.get('SecureLitmit', {})
                        sub = get_latest_sublimit(secure.get('SubLitmits', [])) or {}

                        result = {
                            'dataold_totallimit': x.get('TotalLimit'),
                            'dataold_avaiablelimit': x.get('AvaiableLimit'),
                            'dataold_usedlimit': x.get('UsedLimit'),
                            'dataold_status': x.get('Status'),
                            'dataold_modifieddate': x.get('ModifiedDate'),
                            'dataold_avaiablelimitlast': x.get('AvaiableLimitLast'),
                            'dataold_used_sec_lmt_amt': secure.get('UsedSecureLitmitAmount'),
                            'dataold_avl_sec_lmt_amt': secure.get('AvaiableSecureLitmitAmount'),
                            'dataold_avl_sec_lmt_amt_last': secure.get('AvaiableSecureLitmitAmountLast'),
                            'dataold_used_sublmt_amt': sub.get('UsedSublimitAmount'),
                            'dataold_avl_sublmt_amt': sub.get('AvaiableSublimitAmount'),
                            'dataold_avl_sublmt_amt_last': sub.get('AvaiableSublimitAmountLast'),
                            'dataold_onlinelmt': sub.get('OnlineLimit'),
                            'loanlimits_old': json.dumps(sub.get('LoanLitmits', []), ensure_ascii=False)
                        }
                    except Exception as e:
                        print(f"[!] Lỗi extract DataOld: {e}")
                    return pd.Series(result)

                extracted_old = parsed_dataold.apply(extract_dataold_fields)
                data = pd.concat([data, extracted_old], axis=1)

            except Exception as e:
                print(f"❌ Lỗi khi xử lý DataOld: {e}")



        # Normalize DataNew
        if 'datanew' in data.columns:
            try:
                parsed_datanew = data['datanew'].apply(parse_value)

                def extract_datanew_fields(x):
                    result = {}
                    try:
                        secure = x.get('SecureLitmit', {})
                        sub = get_latest_sublimit(secure.get('SubLitmits', [])) or {}

                        result = {
                            'datanew_totallimit': x.get('TotalLimit'),
                            'datanew_avaiablelimit': x.get('AvaiableLimit'),
                            'datanew_usedlimit': x.get('UsedLimit'),
                            'datanew_status': x.get('Status'),
                            'datanew_modifieddate': x.get('ModifiedDate'),
                            'datanew_avaiablelimitlast': x.get('AvaiableLimitLast'),
                            'datanew_used_sec_lmt_amt': secure.get('UsedSecureLitmitAmount'),
                            'datanew_avl_sec_lmt_amt': secure.get('AvaiableSecureLitmitAmount'),
                            'datanew_avl_sec_lmt_amt_last': secure.get('AvaiableSecureLitmitAmountLast'),
                            'datanew_used_sublmt_amt': sub.get('UsedSublimitAmount'),
                            'datanew_avl_sublmt_amt': sub.get('AvaiableSublimitAmount'),
                            'datanew_avl_sublmt_amt_last': sub.get('AvaiableSublimitAmountLast'),
                            'datanew_onlinelmt': sub.get('OnlineLimit'),
                            'loanlimits_new': json.dumps(sub.get('LoanLitmits', []), ensure_ascii=False)
                        }
                    except Exception as e:
                        print(f"[!] Lỗi extract DataNew: {e}")
                    return pd.Series(result)

                extracted_new = parsed_datanew.apply(extract_datanew_fields)
                data = pd.concat([data, extracted_new], axis=1)

            except Exception as e:
                print(f"❌ Lỗi khi xử lý DataNew: {e}")
    column_mapping = {
        '_id': 'id',
        'source': 'source',
        'cifcode': 'cifcode',
        'assetcode': 'assetcode',
        'username': 'username',
        'requesttransaction_codeno': 'requesttransaction_codeno',
        'requesttransaction_loanid': 'requesttransaction_loanid',

        # DataOld
        'dataold_totallimit': 'dataold_totallimit',
        'dataold_avaiablelimit': 'dataold_avaiablelimit',
        'dataold_usedlimit': 'dataold_usedlimit',
        'dataold_status': 'dataold_status',
        'dataold_modifieddate': 'dataold_modifieddate',
        'dataold_avaiablelimitlast': 'dataold_avaiablelimitlast',
        'dataold_used_sec_lmt_amt': 'dataold_used_sec_lmt_amt',
        'dataold_avl_sec_lmt_amt': 'dataold_avl_sec_lmt_amt',
        'dataold_avl_sec_lmt_amt_last': 'dataold_avl_sec_lmt_amt_last',
        'dataold_used_sublmt_amt': 'dataold_used_sublmt_amt',
        'dataold_avl_sublmt_amt': 'dataold_avl_sublmt_amt',
        'dataold_avl_sublmt_amt_last': 'dataold_avl_sublmt_amt_last',
        'dataold_onlinelmt': 'dataold_onlinelmt',
        'loanlimits_old': 'loanlimits_old',

        # DataNew
        'datanew_totallimit': 'datanew_totallimit',
        'datanew_avaiablelimit': 'datanew_avaiablelimit',
        'datanew_usedlimit': 'datanew_usedlimit',
        'datanew_status': 'datanew_status',
        'datanew_modifieddate': 'datanew_modifieddate',
        'datanew_avaiablelimitlast': 'datanew_avaiablelimitlast',
        'datanew_used_sec_lmt_amt': 'datanew_used_sec_lmt_amt',
        'datanew_avl_sec_lmt_amt': 'datanew_avl_sec_lmt_amt',
        'datanew_avl_sec_lmt_amt_last': 'datanew_avl_sec_lmt_amt_last',
        'datanew_used_sublmt_amt': 'datanew_used_sublmt_amt',
        'datanew_avl_sublmt_amt': 'datanew_avl_sublmt_amt',
        'datanew_avl_sublmt_amt_last': 'datanew_avl_sublmt_amt_last',
        'datanew_onlinelmt': 'datanew_onlinelmt',
        'loanlimits_new': 'loanlimits_new',

        'modifydate': 'modifieddate'
    }



    data.rename(columns=column_mapping, inplace=True)
    data.columns = data.columns.str.upper()
    data = data.loc[:, ~data.columns.duplicated()]
    return data
        
def ingestion(source_conn_id,target_conn_id, stg_tbl_name,  **context):
    # init value sql and fill param for quert
    # Get data from source
    # test connect

    try:
        hook = MongoHook(conn_id=source_conn_id)
        client = hook.get_conn()
        logging.info("connect mongodb susscess")
    except Exception as e:
        logging.error(str(e))
    try:
        try:
            db = client.LimitControl
            collection = db.LimitHistory
        except Exception as e:
            print(str(e))
        # Thực hiện truy vấn find trên Collection
        results = collection.find(query(**context))
        logging.info("results susscess")

    except Exception as e:
        logging.error(str(e))
    try:
        notify_df = pd.DataFrame(results)
        logging.info("dataframe susscess")
    except Exception as e:
        logging.error(str(e))
    notify_df = notify_df.fillna('')
    # Assuming your DataFrame is named 'autocall_df'
    notify_df.rename(columns={'type': 'type_name', '_id': 'id'}, inplace=True)
    # Chuyển đổi ObjectId thành chuỗi nếu có
    for col in notify_df.columns:
        if notify_df[col].dtype == 'object':  # Nếu là kiểu object (bao gồm ObjectId)
            notify_df[col] = notify_df[col].apply(lambda x: str(x) if isinstance(x, ObjectId) else x)
        # Thay thế NaN bằng chuỗi rỗng
            notify_df[col] = notify_df[col].fillna("")
        elif np.issubdtype(notify_df[col].dtype, np.number):  # Cột kiểu số
            # Thay thế NaN bằng 0 cho cột số
            notify_df[col] = notify_df[col].fillna(0)
    # ingest data to staging layer of dwh
    write_df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id, df=custom_app_my88(notify_df))
    logging.info('Get data source to stg done!')

def merge_transform(conn_id, proc_name, **context):
    today =int(YESTERDAY_PROC.strftime('%Y%m%d'))
    proc_name_with_p_date = f"{proc_name}({today})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)
with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                        python_callable=truncate_stg_tbl,
                                        op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
                                        provide_context=True)


    ingestion_limit_history = PythonOperator(
        task_id='ingestion_limit_history',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': notify_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )


    merge_data_stag_to_dwh_limit_history = PythonOperator(
        task_id='merge_data_stag_to_dwh_limit_history',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )
    #
#set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
truncate_stg_table >> ingestion_limit_history >> merge_data_stag_to_dwh_limit_history
