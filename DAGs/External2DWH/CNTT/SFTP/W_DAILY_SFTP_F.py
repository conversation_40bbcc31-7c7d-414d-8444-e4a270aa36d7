from airflow.providers.oracle.hooks.oracle import <PERSON><PERSON><PERSON>
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import <PERSON><PERSON><PERSON>oni<PERSON>, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh,write_df_to_oracle
import os
import logging
from datetime import datetime, timedelta
from bson import ObjectId
import pandas as pd
import numpy as np
import pendulum
from DAGs.External2DWH.CNTT.SFTP.Trans_form_SFTP import get_data,delete_all_files_in_data
dag_name = 'DAILY_W_SFTP_MBank'
description = 'Get data SFTP TO DWH'
oracle_conn_id = 'oracle_f88_dwh'
stg_table = 'STGPROD.sFTP_Du_lieu_MB'
proc_name = 'F88DWH.PROC_W_SFTP_GIAO_DICH_MB_F'

tags = ['daily','sftp']
schedule_interval = '1 8 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
def ingestion(target_conn_id, stg_tbl_name,  **context):
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')
    logical_date = context['logical_date'].astimezone(tz)
    if logical_date.date() == pendulum.now(tz=tz).date():
        YESTERDAY = logical_date - timedelta(days=1)
    else:
        YESTERDAY = logical_date
    TODAY = (YESTERDAY + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    YESTERDAY = YESTERDAY.replace(hour=0, minute=0, second=0, microsecond=0)
    DATE_WID = YESTERDAY.strftime('%Y%m%d')
    logging.info(f"Date: {TODAY}")
    df = get_data(DATE_WID)
    write_df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id, df=df)
    logging.info('Get data source to stg done!')
def merge_transform(conn_id, proc_name, **context):
    logging.info(f"proc_name_with_p_date: {proc_name}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name)
    print("delete file ->>>>>>>>>>>>>>>>>>>>>>>>>")
    delete_all_files_in_data()
with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:

    truncate_stg_table = PythonOperator(task_id='truncate_stg_table',
                                        python_callable=truncate_stg_tbl,
                                        op_kwargs={'tbl_name': stg_table, 'conn_id': oracle_conn_id},
                                        provide_context=True)


    ingestion_sftp_stg = PythonOperator(
        task_id='ingestion_notify_stg',
        python_callable=ingestion,
        op_kwargs={
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table},
        dag=dag
    )


    merge_data_stag_to_dwh_sftp = PythonOperator(
        task_id='merge_data_stag_to_dwh_notify',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name},
        dag=dag
    )

    #
#set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
truncate_stg_table >> ingestion_sftp_stg >> merge_data_stag_to_dwh_sftp
