import paramiko
import os
import pandas as pd
import oracledb
from datetime import datetime
import sys
from airflow.models import Variable
import json
get_variable = json.loads(Variable.get("VAR_SFTP"))

#report_name = get_variable['report_name']
sys.stdout.reconfigure(encoding='utf-8')

# ====== Cau hinh ket noi sFTP ======
SFTP_HOST = get_variable['SFTP_HOST']
SFTP_PORT = get_variable['SFTP_PORT']
SFTP_USERNAME = get_variable['SFTP_USERNAME']
SFTP_KEY_PATH = get_variable['SFTP_KEY_PATH']
SFTP_FOLDER = get_variable['SFTP_FOLDER']
LOCAL_DOWNLOAD_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'Data'))
os.makedirs(LOCAL_DOWNLOAD_DIR, exist_ok=True)

# ====== Cac cot can lay ======
FIELDS_NEEDED = [
    "Loai giao dich", "Request id", "Bank trans id", "FT", "Ngay giao dich",
    "So tien giao dich", "Loai tien te", "Noi dung giao dich", "Trang thai giao dich",
    "Tai khoan ghi no", "Ten chu tai khoan ghi no",
    "Tai khoan ghi co", "Ten chu tai khoan ghi co",
    "So tai khoan don vi thu huong", "Ten tai khoan nhan",
    "Key doi soat napas", "Add_Info"
]

def normalize_column_name(col: str) -> str:
    return col.strip().upper().replace(" ", "_")

def download_txt_by_date(DATE_WID: int):
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(
            hostname=SFTP_HOST,
            port=SFTP_PORT,
            username=SFTP_USERNAME,
            key_filename=SFTP_KEY_PATH,
            look_for_keys=False,
            allow_agent=False
        )

        sftp = ssh.open_sftp()
        sftp.chdir(SFTP_FOLDER)

        # Convert ngày định dạng int (vd: ********) thành datetime.date
        target_date = datetime.strptime(str(DATE_WID), "%Y%m%d").date()

        # Lọc các file thỏa điều kiện
        matching_files = []
        for file in sftp.listdir_attr():
            filename = file.filename
            modified_time = datetime.fromtimestamp(file.st_mtime).date()

            # In ra thông tin file và ngày sửa
            # print(f"{filename} - Last Modified: {modified_time}")
            # 2025-07-23. bo phan biet _CL -> lay full
            if filename.endswith(".txt") and modified_time == target_date:
                matching_files.append(file)

        if not matching_files:
            print(f"Không tìm thấy file nào có last_modified = {target_date}")
            return None

        # Nếu có nhiều file đúng ngày → chỉ lấy file đầu tiên
        selected_file = matching_files[0]
        remote_file = f"{SFTP_FOLDER}/{selected_file.filename}"
        local_file = os.path.join(LOCAL_DOWNLOAD_DIR, selected_file.filename)

        sftp.get(remote_file, local_file)
        sftp.close()
        ssh.close()

        print(f"Đã tải về file: {local_file}")
        return local_file

    except Exception as e:
        print("Lỗi khi tải file từ SFTP:", str(e))
        return None
    
def read_txt_to_df(file_path: str, fields: list) -> pd.DataFrame:
    try:
        df = pd.read_csv(file_path, delimiter='|', encoding='utf-8', dtype=str)  # <--- ép kiểu chuỗi
        df.columns = [normalize_column_name(c) for c in df.columns]
        needed_cols = [normalize_column_name(c) for c in fields]
        df = df[[col for col in needed_cols if col in df.columns]]
        return df
    except Exception as e:
        print("Loi khi doc file txt:", e)
        return pd.DataFrame()

def get_data(DATE_WID):
    txt_path = download_txt_by_date(DATE_WID)
    if not txt_path:
        return

    print("Dang doc file va chuyen thanh DataFrame...")
    df = read_txt_to_df(txt_path, FIELDS_NEEDED)
    if df.empty:
        print("Khong co du lieu sau khi doc file.")
        return
    # 
    #Xu ly ngay giao dich
    if "NGAY_GIAO_DICH" in df.columns:
        df["NGAY_GIAO_DICH"] = pd.to_datetime(df["NGAY_GIAO_DICH"], format="%d/%m/%Y %H:%M:%S", errors="coerce")
        print("Cac dong co NGAY_GIAO_DICH loi:")
        print(df[df["NGAY_GIAO_DICH"].isna()])
        df = df[df["NGAY_GIAO_DICH"].notna()]
    return df
def delete_all_files_in_data():
    try:
        print("Start deleting all files in Data/")
        folder_path = os.path.join(os.path.dirname(__file__), 'Data')

        if not os.path.exists(folder_path):
            print(f"Không tìm thấy thư mục: {folder_path}")
            return

        deleted = False
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                os.remove(file_path)
                print(f"Đã xóa: {file_path}")
                deleted = True

        if not deleted:
            print("Không có file nào để xóa.")

    except Exception as e:
        print(f"Error: cannot delete files — {str(e)}")
