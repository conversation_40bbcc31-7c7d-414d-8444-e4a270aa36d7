from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.operators.python import PythonOperator
from airflow import DAG
from DAGs.utils import DAGMonitor, df_to_oracle, truncate_stg_tbl, merge_data_stag_to_dwh,write_df_to_oracle
import os
import logging
from datetime import datetime, timedelta
from bson import ObjectId
import pandas as pd
import numpy as np
from pandas.api.types import is_datetime64_any_dtype
import pendulum

dag_name = 'DAILY_W_NOTIFY_PROJECT_TEMPLATE_D'
description = 'Get data CALL DETAIL FROM AUTO CALL TO DWH'
notify_conn_id = 'notificationhub'
oracle_conn_id = 'oracle_f88_dwh'
stg_table_project = 'STGPROD.NOTIFY_PROJECT_DETAIL'
proc_name_project = 'F88DWH.PROC_W_NOTIFY_PROJECT_D'

stg_table_template = 'STGPROD.NOTIFY_TEMPLATE_DETAIL'
proc_name_template = 'F88DWH.PROC_W_NOTIFY_TEMPLATE_D'

tags = ['notify_log']
schedule_interval = '10 7 * * *'
default_args = {
    'owner': 'F88-DE',
    'retries': 5,
    'retry_delay': timedelta(minutes=2),
}
def query(**context):
    tz = pendulum.timezone('Asia/Ho_Chi_Minh')

    # Lấy logical_date từ context và chuyển về múi giờ Asia/Ho_Chi_Minh
    logical_date = context['logical_date'].astimezone(tz)

    # Kiểm tra xem logical_date có trùng với ngày hiện tại không
    if logical_date.date() == pendulum.now(tz=tz).date():
        # Nếu trùng: YESTERDAY là ngày trước logical_date
        YESTERDAY = logical_date - timedelta(days=1)
    else:
        # Nếu không trùng: YESTERDAY là chính logical_date
        YESTERDAY = logical_date
    # TODAY là ngày sau YESTERDAY, đặt giờ về 00:00:00
    TODAY = (YESTERDAY + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    YESTERDAY = YESTERDAY.replace(hour=0, minute=0, second=0, microsecond=0)
    logging.info(f"Date: {TODAY}")

    # TODAY = execution_date_str.replace(hour=0, minute=0, second=0, microsecond=0)
    TODAY_PROC = TODAY
    YESTERDAY = TODAY - timedelta(days=1)
    YESTERDAY_PROC = YESTERDAY
    mongo_query = {
        "$or": [
            {
                "create_date": {
                    "$gte": YESTERDAY,
                    "$lt": TODAY
                }
            },
            {
                "modify_date": {
                    "$gte": YESTERDAY,
                    "$lt": TODAY
                }
            }
        ]
    }

    # Chuyển đổi truy vấn thành chuỗi JSON cho hook
    return mongo_query

def ingestion(source_conn_id,target_conn_id, stg_tbl_name,colection, **context):
    # Get data from source
    # Get data from sourcef
    try:
        hook = MongoHook(conn_id=source_conn_id)
        client = hook.get_conn()
        logging.info("connect mongodb susscess")
    except Exception as e:
        logging.error(str(e))
    try:

        db = client.notificationhub_db
        collection = db[colection]
        # Thực hiện truy vấn find trên Collection
        results = collection.find({})
        logging.info("results susscess")
    except Exception as e:
        logging.error(str(e))
    try:
        notify_df = pd.DataFrame(list(results))
        logging.info("dataframe susscess")
    except Exception as e:
        logging.error(str(e))
    if 'create_date' in notify_df.columns:
        notify_df['create_date'] = notify_df['create_date'].fillna(pd.Timestamp('1970-01-01'))

    if 'modify_date' in notify_df.columns:
        notify_df['modify_date'] = notify_df['modify_date'].fillna(pd.Timestamp('1970-01-01'))
    notify_df = notify_df.fillna('')
    print(notify_df.dtypes)
    print(notify_df.head(10))
    # Assuming your DataFrame is named 'autocall_df'

    notify_df.rename(columns={'type': 'type_name', '_id': 'id'}, inplace=True)
    # Chuyển đổi ObjectId thành chuỗi nếu có
    for col in notify_df.columns:
        if notify_df[col].dtype == 'object':  # Nếu là kiểu object (bao gồm ObjectId)
            notify_df[col] = notify_df[col].apply(lambda x: str(x) if isinstance(x, ObjectId) else x)
            # Thay thế NaN bằng chuỗi rỗng
            notify_df[col] = notify_df[col].fillna("")
        elif is_datetime64_any_dtype(notify_df[col]):  # Kiểm tra kiểu datetime
            # Chuyển đổi datetime64[ns, UTC] thành datetime64[ns] nếu cần
            notify_df[col] = notify_df[col].dt.tz_localize(None)
            # Thay thế NaT bằng giá trị mặc định, ví dụ: "1970-01-01"
            notify_df[col] = notify_df[col].fillna(pd.Timestamp("1970-01-01"))
        elif np.issubdtype(notify_df[col].dtype, np.number):  # Cột kiểu số
            # Thay thế NaN bằng 0 cho cột số
            notify_df[col] = notify_df[col].fillna(0)
    # ingest data to staging layer of dwh
    write_df_to_oracle(table_name=stg_tbl_name, oracle_conn_id=target_conn_id, df=notify_df)
    logging.info('Get data source to stg done!')
    #
def merge_transform(conn_id, proc_name, **context):
    logging.info(f"run {proc_name}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name)

with DAG(dag_id=dag_name, start_date=datetime(2024, 6, 1),
         schedule_interval=schedule_interval,
         default_args=default_args,
         catchup=False,
         tags=tags) as dag:

    truncate_stg_table_project = PythonOperator(task_id='truncate_stg_table_project',
                                        python_callable=truncate_stg_tbl,
                                        op_kwargs={'tbl_name': stg_table_project, 'conn_id': oracle_conn_id},
                                        provide_context=True)
    truncate_stg_table_template = PythonOperator(task_id='truncate_stg_table_template',
                                               python_callable=truncate_stg_tbl,
                                               op_kwargs={'tbl_name': stg_table_template, 'conn_id': oracle_conn_id},
                                               provide_context=True)


    ingestion_notify_stg_project = PythonOperator(
        task_id='ingestion_notify_stg_project',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': notify_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table_project,
                   'colection':'project'},

        dag=dag
    )
    ingestion_notify_stg_template = PythonOperator(
        task_id='ingestion_notify_stg_template',
        python_callable=ingestion,
        op_kwargs={'source_conn_id': notify_conn_id,
                   'target_conn_id': oracle_conn_id,
                   'stg_tbl_name': stg_table_template,
                   'colection': 'template'},

        dag=dag
    )


    merge_data_stag_to_dwh_notify_project = PythonOperator(
        task_id='merge_data_stag_to_dwh_notify_project',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name_project},
        dag=dag
    )
    merge_data_stag_to_dwh_notify_template = PythonOperator(
        task_id='merge_data_stag_to_dwh_notify_template',
        python_callable=merge_transform,
        op_kwargs={'conn_id': oracle_conn_id,
                   'proc_name': proc_name_template},
        dag=dag
    )
    #
#set_param >> truncate_stg_table >> ingestion_autocall_data_to_stg >> merge_to_dwh
truncate_stg_table_project >> truncate_stg_table_template >> ingestion_notify_stg_project >> ingestion_notify_stg_template >> merge_data_stag_to_dwh_notify_project >> merge_data_stag_to_dwh_notify_template
