from datetime import datetime,<PERSON>el<PERSON>
import logging
import os
from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow import DAG
from airflow.utils.context import Context
from airflow.operators.python import PythonOperator
import pandas as pd
import json
import re
import json
import pickle
import shutil
from DAGs.utils import DAGMonitor,write_df_to_oracle,merge_data_stag_to_dwh
from Provider.OracleProvider.operators.DDLOperators import DDLOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import TableMetaData

parent_dir = os.path.dirname(os.path.abspath(__file__))
downloaded_folder = os.path.join(parent_dir, 'Downloaded')
mongo_conn_id = 'f88_aws_documentdb_voicebot'
oracle_conn_id = 'oracle_f88_dwh'
table_metadata = TableMetaData(**{'stg_table_schema':{'table_name': 'STGPROD.LOS_VOICEBOT_LOANS', 'columns_datatype': {"INTEGRATION_ID": "VARCHAR2(100)","FIRST_NAME": "VARCHAR2(100)","LAST_NAME": "VARCHAR2(100)","FULL_NAME": "VARCHAR2(200)","GENDER": "VARCHAR2(20)","CIF_CODE": "VARCHAR2(50)","CLIENT_ID": "VARCHAR2(50)","LOAN_ID": "VARCHAR2(50)","LOAN_CODE": "VARCHAR2(50)","DUE_AMOUNT": "VARCHAR2(50)","DUE_DATE": "VARCHAR2(50)","DPD": "VARCHAR2(50)","FUND_SOURCE": "VARCHAR2(200)","DISBURSEDON_DATE": "VARCHAR2(50)","OFFICE_NAME": "VARCHAR2(500)","OFFICE_CODE": "VARCHAR2(50)","VOICEBOT_DATE": "VARCHAR2(50)","BANK_CODE": "VARCHAR2(50)","ACCOUNT_NUMBER": "VARCHAR2(50)","PHONE": "VARCHAR2(50)","RESPONSE": "VARCHAR2(1000)","RESPONSE_STATUS": "VARCHAR2(50)","VOICEBOT_TIME": "VARCHAR2(50)","CONVERSATION_ID": "VARCHAR2(100)", "AREA": "VARCHAR2(10)"}}, 'sql_transform_stg_to_dwh': """SELECT 
to_number(to_char(to_date(REGEXP_REPLACE(VOICEBOT_DATE, '^nan$|^$|^,$'), 'YYYY-MM-DD'),'YYYY')) AS YEAR_NUM,
to_number(to_char(to_date(REGEXP_REPLACE(VOICEBOT_DATE, '^nan$|^$|^,$'), 'YYYY-MM-DD'),'MM')) AS MONTH_NUM,
to_number(to_char(to_date(REGEXP_REPLACE(VOICEBOT_DATE, '^nan$|^$|^,$'), 'YYYY-MM-DD'),'YYYYMMDD')) AS DATE_WID,                                  
REGEXP_REPLACE(INTEGRATION_ID, '^nan$|^$|^,$') AS INTEGRATION_ID,
REGEXP_REPLACE(FIRST_NAME, '^nan$|^$|^,$') AS FIRST_NAME,
REGEXP_REPLACE(LAST_NAME, '^nan$|^$|^,$') AS LAST_NAME,
REGEXP_REPLACE(FULL_NAME, '^nan$|^$|^,$') AS FULL_NAME,
REGEXP_REPLACE(GENDER, '^nan$|^$|^,$') AS GENDER,
REGEXP_REPLACE(CIF_CODE, '^nan$|^$|^,$') AS CIF_CODE,
to_number(REGEXP_REPLACE(CLIENT_ID, '^nan$|^$|^,$')) AS CLIENT_ID,
to_number(REGEXP_REPLACE(LOAN_ID, '^nan$|^$|^,$')) AS LOAN_ID,
REGEXP_REPLACE(LOAN_CODE, '^nan$|^$|^,$') AS LOAN_CODE,
to_number(REGEXP_REPLACE(DUE_AMOUNT, '^nan$|^$|^,$')) AS DUE_AMOUNT,
to_date(REGEXP_REPLACE(DUE_DATE, '^nan$|^$|^,$'), 'DD-MM-YYYY') AS DUE_DATE,
to_number(REGEXP_REPLACE(DPD, '^nan$|^$|^,$')) AS DPD,
REGEXP_REPLACE(FUND_SOURCE, '^nan$|^$|^,$') AS FUND_SOURCE,
to_date(REGEXP_REPLACE(DISBURSEDON_DATE, '^nan$|^$|^,$'), 'DD-MM-YYYY') AS DISBURSEDON_DATE,
REGEXP_REPLACE(OFFICE_NAME, '^nan$|^$|^,$') AS OFFICE_NAME,
REGEXP_REPLACE(OFFICE_CODE, '^nan$|^$|^,$') AS OFFICE_CODE,
to_date(REGEXP_REPLACE(VOICEBOT_DATE, '^nan$|^$|^,$'), 'YYYY-MM-DD') AS VOICEBOT_DATE,
REGEXP_REPLACE(BANK_CODE, '^nan$|^$|^,$') AS BANK_CODE,
REGEXP_REPLACE(ACCOUNT_NUMBER, '^nan$|^$|^,$') AS ACCOUNT_NUMBER,
REGEXP_REPLACE(PHONE, '^nan$|^$|^,$') AS PHONE,
REGEXP_REPLACE(RESPONSE, '^nan$|^$|^,$') AS RESPONSE,
to_number(REGEXP_REPLACE(RESPONSE_STATUS, '^nan$|^$|^,$')) AS RESPONSE_STATUS,
to_timestamp(REGEXP_REPLACE(VOICEBOT_TIME, '^NaT$|^$|^,$'), 'YYYY-MM-DD HH24:MI:SS.FF3') AS VOICEBOT_TIME,
REGEXP_REPLACE(CONVERSATION_ID, '^nan$|^$|^,$') AS CONVERSATION_ID,
'LOS' AS SOURCE,
REGEXP_REPLACE(AREA, '^nan$|^$|^,$') AS AREA
FROM STGPROD.LOS_VOICEBOT_LOANS""", 'dwh_table_schema': {'table_name': 'F88DWH.W_VOICEBOT_LOANS', 'columns_datatype': {"YEAR_NUM": "NUMBER", "MONTH_NUM": "NUMBER", "DATE_WID": "NUMBER", "INTEGRATION_ID": "VARCHAR2(400)", "FIRST_NAME": "VARCHAR2(400)", "LAST_NAME": "VARCHAR2(400)", "FULL_NAME": "VARCHAR2(800)", "GENDER": "VARCHAR2(80)", "CIF_CODE": "VARCHAR2(200)", "CLIENT_ID": "NUMBER", "LOAN_ID": "NUMBER", "LOAN_CODE": "VARCHAR2(200)", "DUE_AMOUNT": "NUMBER", "DUE_DATE": "DATE", "DPD": "NUMBER", "FUND_SOURCE": "VARCHAR2(800)", "DISBURSEDON_DATE": "DATE", "OFFICE_NAME": "VARCHAR2(2000)", "OFFICE_CODE": "VARCHAR2(200)", "VOICEBOT_DATE": "DATE", "BANK_CODE": "VARCHAR2(200)", "ACCOUNT_NUMBER": "VARCHAR2(200)", "PHONE": "VARCHAR2(200)", "RESPONSE": "VARCHAR2(4000)", "RESPONSE_STATUS": "NUMBER", "VOICEBOT_TIME": "TIMESTAMP (9)", "CONVERSATION_ID": "VARCHAR2(400)", "SOURCE": "VARCHAR2(20)", "AREA": "VARCHAR2(10)"}, 'merge_key': ["INTEGRATION_ID"]}})

query ='{"voicebot_date": { "$eq": ":YESTERDAY" }}'
# query='{}'
TODAY_PROC = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
YESTERDAY_PROC = TODAY_PROC - timedelta(days=1)
proc_name = 'F88DWH.PROC_W_VOICEBOT_LOANS'

def fetch_data_from_mongo(mongo_conn_id:str, query: str, local_saved_file_path:str, **context: Context):
    if not os.path.exists(local_saved_file_path):
        os.makedirs(local_saved_file_path)
    yesterday = DAGMonitor.set_params_operator(**context)['YESTERDAY']
    yesterday_formated = datetime.strptime(yesterday, '%Y%m%d').strftime('%Y-%m-%d')
    query = json.loads(query.replace(':YESTERDAY', yesterday_formated))
    
    hook = MongoHook(conn_id=mongo_conn_id)

    ##Create a MongoDB client, open a connection to Amazon DocumentDB as a replica set and specify the read preference as secondary preferred
    # client = pymongo.MongoClient('*********************************************************************************************************************************') 
    client = hook.get_conn()

    ##Specify the database to be used
    # db = client.voicebot_db
    db = client.voicebot_db

    ##Specify the collection to be used
    collection = db.voicebot_loans
    logging.info('Fetching raw data from Mongo with query: %s', str(query))
    ##Find all documents
    x = list(collection.find(query))

    for doc in x:
        doc['_id'] = str(doc['_id'])

    ##Close the connection
    client.close()
    
    pickle_file_path = os.path.join(local_saved_file_path, f'raw_mongo_data_{yesterday}.pickle')
    logging.info('Save raw data to file: %s', pickle_file_path)
    with open(pickle_file_path, 'wb') as file:
        pickle.dump(x, file, protocol=pickle.HIGHEST_PROTOCOL)
    return pickle_file_path
    
def process_extracted_data(oracle_conn_id: str, parent_result_task_name: str, target_table: str, **context: Context):
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    logging.info('Truncate target table')
    hook.run(f'Truncate table {target_table}', autocommit=True)
    raw_data_file_path = context['ti'].xcom_pull(task_ids=parent_result_task_name)
    with open(raw_data_file_path, 'rb') as file:
        raw_data = pickle.load(file)
    if not isinstance(raw_data, pd.DataFrame):
        df = pd.DataFrame(raw_data)
    else:
        df = raw_data
    if not df.empty:

        df['conversation_id'] = df.response.fillna('nan').apply(lambda x: re.findall('"\d+-.*-\w+|\d+"', x))
        df = df.explode(column='conversation_id')
        df.conversation_id = df.conversation_id.str.replace('"','')
        df.columns = df.columns.str.upper()
        df.rename(columns={'_ID':'INTEGRATION_ID'}, inplace=True)
        if 'LOAN_CODE' not in df.columns:
            df.rename(columns={'CONTRACT_CODE': 'LOAN_CODE'}, inplace=True)
        if 'CLIENT_ID' not in df.columns:
            if 'CIF_CODE' in df.columns:
                df['CLIENT_ID'] = df['CIF_CODE']
            else:
                print("Cột 'CIF_CODE' không tồn tại. Không thể tạo 'CLIENT_ID'.")
        #
        # target_fields = ["INTEGRATION_ID","FIRST_NAME","LAST_NAME","FULL_NAME","GENDER","CIF_CODE","CLIENT_ID","LOAN_ID","LOAN_CODE","DUE_AMOUNT","DUE_DATE","DPD","FUND_SOURCE","DISBURSEDON_DATE","OFFICE_NAME","OFFICE_CODE","VOICEBOT_DATE","BANK_CODE","ACCOUNT_NUMBER","PHONE","RESPONSE","RESPONSE_STATUS","VOICEBOT_TIME","CONVERSATION_ID","AREA"]
        # df = df[target_fields]
        # df = df.astype(str)
        # df.fillna('nan', inplace=True)
        # df = df.itertuples(index=False)
        # hook.bulk_insert_rows(table=target_table, rows=df, target_fields=target_fields)
        #Kienpv: 2025-01-20 dung ham moi
        write_df_to_oracle(table_name='STGPROD.LOS_VOICEBOT_LOANS',oracle_conn_id='oracle_f88_dwh',df=df)
def merge_transform(conn_id, proc_name, **context):
    today =int(YESTERDAY_PROC.strftime('%Y%m%d'))
    proc_name_with_p_date = f"{proc_name}({today})"
    logging.info(f"proc_name_with_p_date: {proc_name_with_p_date}")
    merge_data_stag_to_dwh(conn_id=conn_id, proc_name=proc_name_with_p_date)
def delete_downloaded() -> None:
    shutil.rmtree(downloaded_folder, ignore_errors=True)
    
with DAG(dag_id='DAILY_DOCDB_VOICEBOT_LOANS_TO_DWH', description='Kéo dữ liệu Voicebot Loan từ DOCDB về DWH', schedule_interval='0 3 * * *', start_date=datetime(2023, 12, 28), catchup=False, max_active_runs=1, default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'all_success'
         }) as dag:
    task_fetch_data_from_mongo = PythonOperator(task_id='task_fetch_data_from_mongo', python_callable=fetch_data_from_mongo, op_kwargs={'mongo_conn_id':mongo_conn_id, 'query': query, 'local_saved_file_path': downloaded_folder}, provide_context=True)
    
    task_process_extracted_data = PythonOperator(task_id='task_process_extracted_data', python_callable=process_extracted_data, op_kwargs={'oracle_conn_id': oracle_conn_id, 'parent_result_task_name': 'task_fetch_data_from_mongo', 'target_table': table_metadata.stg_table_schema.table_name})

    #kienpv: 2025-01-20 khong dung cac task ben duoi
    # create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
    #                                                 table_schema=table_metadata.stg_table_schema,
    #                                                 oracle_conn_id=oracle_conn_id,
    #                                                 insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)
    #
    # create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}',
    #                                         table_schema=table_metadata.dwh_table_schema,
    #                                         oracle_conn_id=oracle_conn_id,
    #                                         insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)
    #
    # transform_table = STG2DWHOperators(
    #     task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',
    #     oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema,
    #     target_table_schema=table_metadata.dwh_table_schema,
    #     select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)
    merge_data_staging_to_dwh = PythonOperator(task_id='merge_data_stag_to_dwh',python_callable=merge_transform, op_kwargs={'conn_id': oracle_conn_id, 'proc_name': proc_name})

    task_delete_downloaded = PythonOperator(task_id='task_delete_downloaded', python_callable=delete_downloaded)

    task_fetch_data_from_mongo >> task_process_extracted_data >> merge_data_staging_to_dwh >>  task_delete_downloaded # create_and_truncate_stg_table >> task_process_extracted_data >> create_and_merge_dwh_table >> transform_table >>