from datetime import datetime, <PERSON>el<PERSON>
import logging
import os
from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow import DAG
from airflow.utils.context import Context
from airflow.operators.python import PythonOperator
import pandas as pd
import pickle
import shutil
from DAGs.utils import DAGM<PERSON>tor
import pendulum
from Provider.OracleProvider.operators.DDLOperators import DDLOperator
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import TableMetaData

parent_dir = os.path.dirname(os.path.abspath(__file__))
downloaded_folder = os.path.join(parent_dir, 'Downloaded')
mongo_conn_id = 'f88_aws_documentdb_ecm'
database_name ='ECM_PROD'
collection_name = "FileManagement"
oracle_conn_id = 'oracle_f88_dwh'

proc_W_LOAN_IMG_F = 'BEGIN F88DWH.PROC_W_LOAN_IMG_F(:YESTERDAY); END;'

proc_W_LOAN_TRANS_IMAGE_F = 'BEGIN F88DWH.PROC_W_LOAN_TRANS_IMAGE_F(:YESTERDAY); END;'

proc_W_REVENUEEXPENDITUREIMAGE_F = 'BEGIN F88DWH.PROC_W_REVENUEEXPENDIMAGE_F(:YESTERDAY); END;'

table_metadata = TableMetaData(**{'rename_columns':{"_id":'FILE_ID', 'Domain': 'FOLDER_DOMAIN'},'list_columns_to_ingest_to_stg':["INTEGRATION_ID", "FOLDER_DOMAIN", "FILETYPE", "REFCODE", "CIFCODE", "ASSETCODE", "CHILDREN", 'FOLDERTYPE', 'FOLDERCODE', 'FILE_ID',
       'EXTERNALID', 'NAME', 'FILEPATH', 'STATUS', 'CREATEDUSERNAME',
       'CREATEDDATE', 'CREATEDROLE', 'MODIFIEDUSERNAME', 'MODIFIEDDATE',
       'MODIFIEDROLE'],'stg_table_schema':{'table_name': 'STGPROD.ECM_FILEMANAGEMENT', 'columns_datatype': {'INTEGRATION_ID': 'VARCHAR2(100)', 'FOLDER_DOMAIN': 'VARCHAR2(100)', 'FILETYPE': 'VARCHAR2(100)', 'REFCODE': 'VARCHAR2(100)', 'CIFCODE': 'VARCHAR2(100)', 'ASSETCODE': 'VARCHAR2(100)', "CHILDREN": "CLOB", "FOLDERTYPE":"VARCHAR2(50)", "FOLDERCODE":"VARCHAR2(50)", 'FILE_ID':'VARCHAR2(100)',
       'EXTERNALID': 'VARCHAR2(100)', 'NAME': 'VARCHAR2(500)', 'FILEPATH': 'VARCHAR2(1000)', 'STATUS':"VARCHAR2(50)", 'CREATEDUSERNAME': 'VARCHAR2(100)',
       'CREATEDDATE':"VARCHAR2(50)", 'CREATEDROLE': 'VARCHAR2(100)', 'MODIFIEDUSERNAME': 'VARCHAR2(100)', 'MODIFIEDDATE':"VARCHAR2(50)",
       'MODIFIEDROLE': 'VARCHAR2(100)'}}, 'sql_transform_stg_to_dwh': """SELECT INTEGRATION_ID, FOLDER_DOMAIN, FILETYPE, REFCODE, CIFCODE, ASSETCODE, FOLDERTYPE, FOLDERCODE, FILE_ID, EXTERNALID, NAME, FILEPATH, STATUS, CREATEDUSERNAME, CAST(TO_TIMESTAMP_TZ(REGEXP_REPLACE(CREATEDDATE, '^nan$|^$|^,$'), 'YYYY-MM-DD HH24:MI:SS.FF3') AS DATE) CREATEDDATE, CREATEDROLE, MODIFIEDUSERNAME, CAST(TO_TIMESTAMP_TZ(REGEXP_REPLACE(MODIFIEDDATE, '^nan$|^$|^,$'), 'YYYY-MM-DD HH24:MI:SS.FF3') AS DATE) MODIFIEDDATE, MODIFIEDROLE, EXTRACT(YEAR FROM TO_TIMESTAMP_TZ(REGEXP_REPLACE(CREATEDDATE, '^nan$|^$|^,$'), 'YYYY-MM-DD HH24:MI:SS.FF3')) AS YEAR_NUM, EXTRACT(MONTH FROM TO_TIMESTAMP_TZ(REGEXP_REPLACE(CREATEDDATE, '^nan$|^$|^,$'), 'YYYY-MM-DD HH24:MI:SS.FF3')) AS MONTH_NUM, TO_NUMBER(TO_CHAR(TO_TIMESTAMP_TZ(REGEXP_REPLACE(CREATEDDATE, '^nan$|^$|^,$'), 'YYYY-MM-DD HH24:MI:SS.FF3'), 'YYYYMMDD')) AS DATE_WID FROM STGPROD.ECM_FILEMANAGEMENT""", 'dwh_table_schema': {'table_name': 'F88DWH.W_ECM_FILEMANAGEMENT_F', 'columns_datatype': {'INTEGRATION_ID': 'VARCHAR2(100)', 'FOLDER_DOMAIN': 'VARCHAR2(100)', 'FILETYPE': 'VARCHAR2(100)', 'REFCODE': 'VARCHAR2(100)', 'CIFCODE': 'VARCHAR2(100)', 'ASSETCODE': 'VARCHAR2(100)', "FOLDERTYPE":"VARCHAR2(50)", "FOLDERCODE":"VARCHAR2(50)", 'FILE_ID':'VARCHAR2(100)',
       'EXTERNALID': 'VARCHAR2(100)', 'NAME': 'VARCHAR2(500)', 'FILEPATH': 'VARCHAR2(1000)', 'STATUS':"VARCHAR2(50)", 'CREATEDUSERNAME': 'VARCHAR2(100)',
       'CREATEDDATE':"DATE", 'CREATEDROLE': 'VARCHAR2(100)', 'MODIFIEDUSERNAME': 'VARCHAR2(100)', 'MODIFIEDDATE':"DATE",
       'MODIFIEDROLE': 'VARCHAR2(100)', 'YEAR_NUM': "NUMBER", 'MONTH_NUM': 'NUMBER', 'DATE_WID': 'NUMBER'}, 'merge_key': ["INTEGRATION_ID", "FILE_ID"]}})

def run_proc(oracle_conn_id:str, sql:str, **context) -> None:
    parameters = DAGMonitor.set_params_operator(**context)
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    filtered_parameters = DAGMonitor.sql_get_filtered_params(parameters, sql)
    logging.info('Start running sql code:')
    logging.info(sql)
    hook.run(sql=sql, autocommit=True, parameters=filtered_parameters)

def fetch_data_from_mongo(mongo_conn_id:str, local_saved_file_path:str, **context: Context):
    yesterday = datetime.strptime(DAGMonitor.set_params_operator(**context)['YESTERDAY'], '%Y%m%d')
    # yesterday = datetime.strptime('20240229', '%Y%m%d')
    today = yesterday + timedelta(days=1)
    yesterday = pendulum.datetime(year=yesterday.year,month=yesterday.month,day=yesterday.day,tz='Asia/Ho_Chi_Minh')
    today = pendulum.datetime(year=today.year,month=today.month,day=today.day,tz='Asia/Ho_Chi_Minh')
    query = {
    "$and": [
    {'Domain': 'LOS'},
    # {"FileType": {"$nin": ["aaa", "bbb"]}},
    {"$or": [
        {
            "CreatedDate": {
                "$gte": yesterday,
                "$lt": today
            }
        },
        {
            "ModifiedDate": {
                "$gte": yesterday,
                "$lt": today
            }
        }
        ]
        }
    ]
    }
    if not os.path.exists(local_saved_file_path):
        os.makedirs(local_saved_file_path)
    
    hook = MongoHook(conn_id=mongo_conn_id)

    client = hook.get_conn()
    
    db = client[database_name]
    
    collection = db[collection_name]
    logging.info('Fetching raw data from Mongo with query: %s', str(query))
    ##Find all documents
    x = pd.DataFrame(tuple(collection.find(query)))
    if '_id' in x:
        x['_id'] = x['_id'].apply(lambda x: str(x))
    ##Close the connection
    client.close()
    pickle_file_path = os.path.join(local_saved_file_path, f'raw_mongo_data_{yesterday}.pickle')
    logging.info('Save raw data to file: %s', pickle_file_path)
    with open(pickle_file_path, 'wb') as file:
        pickle.dump(x, file, protocol=pickle.HIGHEST_PROTOCOL)
    return pickle_file_path
    
def process_extracted_filemanagement_data(oracle_conn_id: str, parent_result_task_name: str, table_metadata: TableMetaData, **context: Context):
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    raw_data_file_path = context['ti'].xcom_pull(task_ids=parent_result_task_name)
    with open(raw_data_file_path, 'rb') as file:
        raw_data = pickle.load(file)
    if not isinstance(raw_data, pd.DataFrame):
        df = pd.DataFrame(raw_data)
        if '_id' in df:
            df['_id'] = df['_id'].apply(lambda x: str(x))
    else:
        df = raw_data
    if not df.empty:
        df.rename(columns={'_id':'INTEGRATION_ID'}, inplace=True)
        df = df.drop(columns=['CreatedDate', 'ModifiedDate'])
        df = df.explode(['FileDetails'])
        df = pd.concat([df.drop(columns='FileDetails'), df.FileDetails.apply(pd.Series)], axis=1)
        df = df.explode('FileInformations')
        df = pd.concat([df.drop(columns='FileInformations'), df.FileInformations.apply(pd.Series)], axis=1)
        df.rename(columns=table_metadata.rename_columns, inplace=True)
        df.columns = df.columns.str.upper()
        target_fields = table_metadata.list_columns_to_ingest_to_stg
        df = df[target_fields]
        df = df.astype(str)
        df.fillna('nan', inplace=True)
        df = df.itertuples(index=False)
        hook.bulk_insert_rows(table=table_metadata.stg_table_schema.table_name, rows=df, target_fields=target_fields)
        
def delete_downloaded() -> None:
    shutil.rmtree(downloaded_folder, ignore_errors=True)
    
with DAG(dag_id='DAILY_DOCDB_ECM_ETL_TO_DWH', description='Kéo dữ liệu ECM DOCDB về DWH', schedule='30 3 * * *', start_date=datetime(2020, 12, 28), catchup=False, max_active_runs=1, default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'all_success'
         }) as dag:
    task_fetch_data_from_mongo = PythonOperator(task_id='task_fetch_data_from_mongo', python_callable=fetch_data_from_mongo, op_kwargs={'mongo_conn_id':mongo_conn_id, 'local_saved_file_path': downloaded_folder})
    
    task_process_extracted_data = PythonOperator(task_id='task_process_extracted_data', python_callable=process_extracted_filemanagement_data, op_kwargs={'oracle_conn_id': oracle_conn_id, 'parent_result_task_name': 'task_fetch_data_from_mongo', 'table_metadata': table_metadata})
    
    create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
                                                    table_schema=table_metadata.stg_table_schema,
                                                    oracle_conn_id=oracle_conn_id,
                                                    insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)
    
    task_delete_downloaded = PythonOperator(task_id='task_delete_downloaded', python_callable=delete_downloaded)
    
    dag_proc_W_LOAN_IMG_F = PythonOperator(task_id='run_proc_W_LOAN_IMG_F', python_callable=run_proc, op_kwargs={'oracle_conn_id': oracle_conn_id, 'sql': proc_W_LOAN_IMG_F})

    dag_proc_W_LOAN_TRANS_IMAGE_F = PythonOperator(task_id='run_proc_W_LOAN_TRANS_IMAGE_F', python_callable=run_proc, op_kwargs={'oracle_conn_id': oracle_conn_id, 'sql': proc_W_LOAN_TRANS_IMAGE_F})

    dag_proc_W_REVENUEEXPENDITUREIMAGE_F = PythonOperator(task_id='run_proc_W_REVENUEEXPENDITUREIMAGE_F', python_callable=run_proc, op_kwargs={'oracle_conn_id': oracle_conn_id, 'sql': proc_W_REVENUEEXPENDITUREIMAGE_F})
    
    task_fetch_data_from_mongo >> create_and_truncate_stg_table >> task_process_extracted_data >> [dag_proc_W_LOAN_IMG_F, dag_proc_W_LOAN_TRANS_IMAGE_F, dag_proc_W_REVENUEEXPENDITUREIMAGE_F] >> task_delete_downloaded
    