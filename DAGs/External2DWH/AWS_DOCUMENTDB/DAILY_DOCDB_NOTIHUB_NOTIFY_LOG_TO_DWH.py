from datetime import datetime
import logging
import os
from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow import DAG
from airflow.utils.context import Context
from airflow.operators.python import PythonOperator
import pandas as pd
import json
import re
import json
import pickle
import shutil
from DAGs.utils import DAGMonitor
from Provider.OracleProvider.operators.DDLOperators import DDLOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import TableMetaData

parent_dir = os.path.dirname(os.path.abspath(__file__))
downloaded_folder = os.path.join(parent_dir, 'Downloaded')
mongo_conn_id = 'f88_aws_documentdb_voicebot'
oracle_conn_id = 'oracle_f88_dwh'
table_metadata = TableMetaData(**{'stg_table_schema': {'table_name': 'STGPROD.NOTIHUB_NOTIFY_LOG',
                                                       'columns_datatype': {"INTEGRATION_ID": "VARCHAR2(100)",
                                                                            "CREATE_DATE": "VARCHAR2(50)",
                                                                            "MESSAGE": "VARCHAR2(255)",
                                                                            "PHONE_NO": "VARCHAR2(100)",
                                                                            "PROJECT_ID": "NUMBER",
                                                                            "REQUEST_MSG": "CLOB",
                                                                            "RESPONSE_CODE": "VARCHAR2(20)",
                                                                            "SENT_TIME": "VARCHAR2(100)",
                                                                            "TEMPLATE_ID": "NUMBER",
                                                                            "TYPE": "VARCHAR2(20)"}},
                                  'sql_transform_stg_to_dwh': """
""", 'dwh_table_schema': {'table_name': 'F88DWH.W_NOTIHUB_NOTIFY_LOG_F', 'columns_datatype': {}}})

# query ='{"create_date": { "$eq": ":YESTERDAY" }}'
query = '{}'


def fetch_data_from_mongo(mongo_conn_id: str, query: str, local_saved_file_path: str, **context: Context):
    if not os.path.exists(local_saved_file_path):
        os.makedirs(local_saved_file_path)
    yesterday = DAGMonitor.set_params_operator(**context)['YESTERDAY']
    yesterday_formated = datetime.strptime(yesterday, '%Y%m%d').strftime('%Y-%m-%d')
    query = json.loads(query.replace(':YESTERDAY', yesterday_formated))

    hook = MongoHook(conn_id=mongo_conn_id)

    ##Create a MongoDB client, open a connection to Amazon DocumentDB as a replica set and specify the read preference as secondary preferred
    # client = pymongo.MongoClient('*********************************************************************************************************************************')
    client = hook.get_conn()

    ##Specify the database to be used
    db = client.notificationhub_db

    ##Specify the collection to be used
    collection = db.notify_log
    logging.info('Fetching raw data from Mongo with query: %s', str(query))
    ##Find all documents
    x = list(collection.find(query))

    for doc in x:
        doc['_id'] = str(doc['_id'])

    ##Close the connection
    client.close()

    pickle_file_path = os.path.join(local_saved_file_path, f'raw_mongo_data_{yesterday}.pickle')
    logging.info('Save raw data to file: %s', pickle_file_path)
    with open(pickle_file_path, 'wb') as file:
        pickle.dump(x, file, protocol=pickle.HIGHEST_PROTOCOL)
    return pickle_file_path


def process_extracted_data(oracle_conn_id: str, parent_result_task_name: str, target_table: str, **context: Context):
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    logging.info('Truncate target table')
    hook.run(f'Truncate table {target_table}', autocommit=True)
    raw_data_file_path = context['ti'].xcom_pull(task_ids=parent_result_task_name)
    with open(raw_data_file_path, 'rb') as file:
        raw_data = pickle.load(file)
    if not isinstance(raw_data, pd.DataFrame):
        df = pd.DataFrame(raw_data)
    else:
        df = raw_data
    if not df.empty:
        df['conversation_id'] = df.response.fillna('nan').apply(lambda x: re.findall('"\d+-.*-\w+|\d+"', x))
        df = df.explode(column='conversation_id')
        df.conversation_id = df.conversation_id.str.replace('"', '')
        df.columns = df.columns.str.upper()
        df.rename(columns={'_ID': 'INTEGRATION_ID'}, inplace=True)
        target_fields = ["INTEGRATION_ID", "CREATE_DATE", "MESSAGE", "PHONE_NO", "PROJECT_ID", "REQUEST_MSG",
                         "RESPONSE_CODE", "SENT_TIME", "TEMPLATE_ID", "TYPE"]
        df = df[target_fields]
        df = df.astype(str)
        df.fillna('nan', inplace=True)
        df = df.itertuples(index=False)
        hook.bulk_insert_rows(table=target_table, rows=df, target_fields=target_fields)


def delete_downloaded() -> None:
    shutil.rmtree(downloaded_folder, ignore_errors=True)


with DAG(dag_id='DAILY_DOCDB_NOTIHUB_NOTIFY_LOG_TO_DWH', description='Kéo dữ liệu Notify_log từ DOCDB về DWH',
         schedule_interval='0 3 * * *', start_date=datetime(2023, 12, 28), catchup=False, max_active_runs=1,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'all_success'
         }) as dag:
    task_fetch_data_from_mongo = PythonOperator(task_id='task_fetch_data_from_mongo',
                                                python_callable=fetch_data_from_mongo,
                                                op_kwargs={'mongo_conn_id': mongo_conn_id, 'query': query,
                                                           'local_saved_file_path': downloaded_folder},
                                                provide_context=True)

    task_process_extracted_data = PythonOperator(task_id='task_process_extracted_data',
                                                 python_callable=process_extracted_data,
                                                 op_kwargs={'oracle_conn_id': oracle_conn_id,
                                                            'parent_result_task_name': 'task_fetch_data_from_mongo',
                                                            'target_table': table_metadata.stg_table_schema.table_name})

    create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
                                                table_schema=table_metadata.stg_table_schema,
                                                oracle_conn_id=oracle_conn_id,
                                                insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

    task_delete_downloaded = PythonOperator(task_id='task_delete_downloaded', python_callable=delete_downloaded)

    task_fetch_data_from_mongo >> create_and_truncate_stg_table >> task_process_extracted_data >> task_delete_downloaded