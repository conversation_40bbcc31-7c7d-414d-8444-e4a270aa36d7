import os
import datetime
import pandas as pd
import logging
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.operators.oracle import OracleStoredProcedureOperator, OracleOperator
from airflow.providers.oracle.hooks.oracle import <PERSON>Hook
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'GAPO_ID_TO_DATAWAREHOUSE'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
description = 'Import dữ liệu gapo id vào DWH'
tags = ['gapo_id', 'sharepoint2dwh', 'bot-gapo']
department = 'Project - GapoID'
oracle_conn_id = 'oracle_f88_dwh'
report_name = 'ExportedFile'
freq = None
saved_folder = os.path.join(parent_dir, 'Downloaded')
_type = 'SharePoint to Data Warehouse'

columns = ['GAPO_ID', 'STATUS', 'PERMISSION', 'PHONE', 'EMAIL']
dwh_columns = ", ".join([str(elem) for elem in columns])


def import_to_dwh():
    file = next(os.walk(os.path.join(saved_folder, report_name)))[2][0]
    df = pd.read_excel(os.path.join(saved_folder, report_name, file), index_col=None, header=0)
    source_data = df.filter(['GAPO User ID', 'Trạng thái', 'Quyền', 'Số điện thoại', 'Email'])
    logging.info(f'Read excel file {file}')
    logging.info(f'Excel file has {len(source_data)} records')
    source_data = source_data.fillna(value='')
    source_data['Số điện thoại'] = source_data['Số điện thoại'].apply(lambda x: str(x)[:-2] if x != '0' else '')
    logging.info(f'All Excel files has {len(source_data)} records in total')
    rows = source_data.values.tolist()
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    hook.insert_rows(table='F88DWH.W_GAPO_USER_TEMP', target_fields=columns, commit_every=500, rows=rows)


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags,
         catchup=False
         ) as dag:
    start = EmptyOperator(task_id='Start')
    download = DownloadSharePointOperator(task_id='Download_Data_from_SharePoint',
                                          sharepoint_conn_id='sharepoint_f88_data',
                                          department=department,
                                          report_name=report_name,
                                          local_saved_folder=saved_folder,
                                          freq=freq,
                                          _type=_type
                                          )
    delete_table = OracleOperator(task_id='Delete_W_GAPO_ID_and_W_GAPO_USER_TEMP',
                                  oracle_conn_id=oracle_conn_id,
                                  sql=['DELETE FROM F88DWH.W_GAPO_ID', 'DELETE FROM F88DWH.W_GAPO_USER_TEMP'],
                                  autocommit=True)
    import_to_temp_table_dwh = PythonOperator(task_id='Import_Downloaded_Data_to_Temp_Table_DWH',
                                              python_callable=import_to_dwh)
    copy_temp_tp_w_gapo_id = OracleOperator(task_id='COPY_FROM_W_GAPO_USER_TEMP_TO_W_GAPO_ID',
                                            oracle_conn_id=oracle_conn_id,
                                            sql=f"INSERT INTO F88DWH.W_GAPO_ID({dwh_columns}) "
                                                f"SELECT GAPO_ID, STATUS, PERMISSION, PHONE, EMAIL FROM F88DWH.W_GAPO_USER_TEMP",
                                            autocommit=True)
    update_by_call_procedure = OracleStoredProcedureOperator(task_id='Call_Procedure_to_Update_Email',
                                                             oracle_conn_id=oracle_conn_id,
                                                             procedure='F88DWH.UPDATE_GAPO_EMAIL')
    end = EmptyOperator(task_id='End')
    start >> [download, delete_table] >> import_to_temp_table_dwh >> copy_temp_tp_w_gapo_id >> update_by_call_procedure >> end
