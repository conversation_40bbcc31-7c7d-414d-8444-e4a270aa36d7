import logging

from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.DDLOperators import DD<PERSON><PERSON>perator
from airflow import DAG
import os
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaData
from DAGs.utils import DAGMonitor
from datetime import  datetime,timedelta
from airflow.models import Variable
import json
parent_dir = os.path.dirname(os.path.abspath(__file__))
#dag_name = 'DAILY_TTKH_SP2DWH_FACEBOOK_1'
start_date = datetime(2022, 9, 1)
schedule_interval = None
description = 'Lấy dữ liệu từ Excel rồi đưa lên DB'
tags = ['sharepoint2dwh']
overwrite = True
freq = None
oracle_conn_id = 'oracle_f88_dwh'
_type = 'SharePoint to Data Warehouse'
#department='Project - TTKH'
saved_folder = os.path.join(parent_dir, 'Downloaded')
#report_name='TTKH_DATA'
get_variable = json.loads(Variable.get("SHAREFILE2DWH_TTKH_TIKTOK"))

#report_name = get_variable['report_name']
dag_name = get_variable['dag_name']
#department = get_variable['department']
schedule_interval = get_variable['schedule_interval']
### Trong một cột nào đấy ở Dag Monitor
##
get_tables_metadata, department, report_name, schedule_interval = DAGMonitor.get_table_metadata(dag_id=dag_name)
logging.info(f'get table metadata from query: {get_tables_metadata}')
tables_metadata = ListTableMetaData(list=get_tables_metadata)
logging.info(f'tables_metadata: {tables_metadata}')
with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         catchup=False,
         tags=tags) as dag:

    download_files = DownloadSharePointOperator(task_id='download_files',
                                            sharepoint_conn_id='sharepoint_f88_data',
                                            department=department,
                                            report_name=report_name,
                                            local_saved_folder=saved_folder,
                                            freq=freq,
                                            _type=_type
                                            )

    for table_metadata in tables_metadata.list:

        insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=oracle_conn_id)
        
        create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}', table_schema=table_metadata.stg_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

        create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}', table_schema=table_metadata.dwh_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)

        transform_table = STG2DWHOperators(task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema, target_table_schema=table_metadata.dwh_table_schema, select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)

        create_and_truncate_stg_table >> download_files >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table