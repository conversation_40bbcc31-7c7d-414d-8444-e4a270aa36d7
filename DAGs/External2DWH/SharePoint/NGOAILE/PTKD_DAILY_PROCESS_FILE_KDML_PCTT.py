
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from datetime import datetime, timed<PERSON><PERSON>
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
import smtplib
import logging
import shutil
from Provider.OracleProvider.hooks.DDLHooks import DDLHook
from Provider.OracleProvider.operators.DDLOperators import <PERSON><PERSON><PERSON><PERSON><PERSON>
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.hooks.DeleteHook import DeleteFileSharePointHook
from Provider.SharePointProvider.hooks.DownloadHook import DownloadSharePointHook
from Provider.SharePointProvider.hooks.UploadHooks import UploadSharePointHook
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaD<PERSON>
from Provider.utils import set_saved_folder_and_file_name
import pickle
from typing import Any, Dict, List, Tuple
import os
from airflow.hooks.base import BaseHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.models.baseoperator import BaseOperator
from airflow.models import Variable
from airflow.utils.context import Context
from airflow import DAG
from airflow.operators.python import PythonOperator
from DAGs.utils import DAGMonitor
import subprocess
import re
from office365.sharepoint.files.file import File
from office365.runtime.client_request_exception import ClientRequestException
from tenacity import retry, wait_fixed, stop_after_attempt
import pendulum
from itertools import chain
import glob
import csv
from io import StringIO
import shutil

def pass_client_request_exception(error: ClientRequestException):
    if '404 Client Error: Not Found for url' not in str(error):
        raise error
    else:
        logging.info(str(error))

def send_email(to_email: str, employee_failed_with_error_msg: List[str|Tuple[str]] | Tuple[str|Tuple[str]]):
    email_var = Variable.get('BI_EMAIL', deserialize_json=True)
    your_email = email_var.get('email')
    your_password = email_var.get('password')

    # establishing connection with gmail
    server = smtplib.SMTP_SSL('smtp.f88.co', 465)
    server.ehlo()
    server.login(your_email, your_password)

    html_content = '''
    <html>
    <head><style>
        html {font-family: arial;}
        table {border-collapse: collapse; font-size: 10pt;}
        th, td {border: 1px solid #999; text-align: left; padding: 3px 5px;}
    </style></head>
    <body>
        <p>Dear anh Anh/Chị,</p>
        <p></p>
        <p>Mail này có mục đích thông báo tới anh chị rằng anh chị đã upload file sai format lên Sharepoint dự án "Hồ sơ pháp lý cho Pháp Chế" cụ thể như sau:</p>
        <p></p>
    '''
    html_content += """<table style="width: 100%">
        <tr>
        <th>Mã Nhân Viên</th>
        <th>User Name</th>
        <th>Chi tiết lỗi</th>
        </tr>
    """

    for employee_code, employee_user_name, errror_msg in employee_failed_with_error_msg:
        html_content += "<tr>"
        html_content += "<td>" + employee_code + "</td>"
        html_content += "<td>" + employee_user_name + "</td>"
        html_content += "<td>" + errror_msg + "</td>"
        html_content += "</tr>"

    html_content += "</table>"
    html_content += '''
        <p></p>
        <p>Chúng tôi đã xóa tất cả các file excel và PDF của anh chị, anh chị vui lòng làm  lại cho pháp chế.</p>
        <p></p>
        <p>Anh chị vui lòng đọc hướng dẫn file word trong thư mục của mình!</p>
        <p></p>
        <p>Mọi thắc mắc liên hệ Nguyễn Hải Long: https://www.gapowork.vn/profile/1761459657</p>
        <P></P>
        <P>Trân trọng!</P>
    </body>
    </html>
    '''
        

    msg = MIMEMultipart()
    msg['From'] = your_email
    msg['To'] = to_email
    msg['Subject'] = 'THÔNG BÁO VỀ USER NHẬP SAI DỰ ÁN HỒ SƠ PHÁP LÝ CHO PHÁP CHẾ'
    content=html_content

    html_part = MIMEText(content, 'html')
    msg.attach(html_part)

    server.send_message(msg)
    server.close()

### Create hooks for dag
class ManipulateFoldersAndFilesHook(DownloadSharePointHook):
    def __init__(self, sharepoint_conn_id: str, oracle_conn_id: str, folder_destination_url: str) -> None:
        super().__init__(sharepoint_conn_id)
        self.oracle_conn_id = oracle_conn_id
        self.folder_destination_url = folder_destination_url
    
    def run(self, relative_url_folder_path: str, local_saved_folder: str, process_date: str) -> Tuple[str]:
        @retry(wait=wait_fixed(10), stop=stop_after_attempt(5))
        def _get_child_files_relative_url_by_parent_folder_and_download(employee_code_and_folder_path):
            """Lấy tất cả URL của file excel và pdf về, đồng thời tải file excel nếu tìm thấy.
            Thứ tự trả về: (Mã nv, [list file excel url], {shop_code: [list file pdf url]}, [list file exel downloaded])

            Args:
                employee_code_and_folder_path (_type_): _description_

            Returns:
                _type_: _description_
            """    
            session = self.get_conn()
            employee_code, folder_path = employee_code_and_folder_path
            self.log.info(f'Process {str(employee_code)}')
            files = session.web.get_folder_by_server_relative_path(folder_path).files
            session.load(files).execute_query()
            share_point_files = [f.properties["ServerRelativeUrl"] for f in files if not re.findall('.docx$', f.properties["Name"])]
            employee_code = employee_code.split('_')[-1]
            
            @retry(wait=wait_fixed(10), stop=stop_after_attempt(5))
            def _download_share_point_file(file: File | str) -> str:
                """Tải file excel bởi File object

                Args:
                    file (File): _description_
                    local_saved_file (str): _description_

                Returns:
                    str: _description_
                """
                file = file.properties["ServerRelativeUrl"]
                file_name = os.path.basename(file)
                if file_name.endswith('.pdf') or re.findall('.xls\w{0,1}$', file_name):
                ### Only get excel files
                #if re.findall('.xls\w{0,1}$', file_name):
                    download_file_path = os.path.join(local_saved_folder, '{0}_{1}_{2}'.format(employee_code, process_date, file_name))
                    if os.path.exists(download_file_path):
                        if os.path.getsize(download_file_path) > 0:
                            self.log.info('File has already downloaded. Skip and continue another file.')
                            return download_file_path
                    self.log.info("Start download file: {0}".format(os.path.basename(download_file_path))) 
                    ### Download file with new session
                    session = self.get_conn()
                    with open(download_file_path, "wb") as local_file:
                        session.web.get_file_by_server_relative_path(file).download(local_file).execute_query()
                    self.log.info("[Ok] file has been downloaded into: {0}".format(download_file_path))
                    return download_file_path
                else:
                    self.log.info('Pass %s because it is not as expected format.', file)
    
            local_excel_files = []
            pdf_files_map = {}
            
            with ThreadPoolExecutor(max_workers=2) as pool:
                list_downloaded_file = tuple(pool.map(_download_share_point_file, files))

            list_downloaded_file = tuple([i for i in list_downloaded_file if i])
            
            for file_path in list_downloaded_file:
                if re.findall('.xls\w{0,1}$', os.path.basename(file_path)):
                    ### Get excel then
                    local_excel_files.append(file_path)
                elif os.path.basename(file_path).endswith('.pdf'):
                    shop_code = re.findall(r'[A-Z]{3}\d{5}', os.path.basename(file_path))
                    if shop_code:
                        shop_code = shop_code[0]
                    else: 
                        shop_code = 'NONE'
                    if shop_code in pdf_files_map:
                        pdf_files_map[shop_code].append(file_path)
                    else:
                        pdf_files_map[shop_code] = [file_path]
            return employee_code, share_point_files, pdf_files_map, local_excel_files
        
        session = self.get_conn()
            
        relative_url_folder_path = relative_url_folder_path.replace("\\", "/").removeprefix("/").removesuffix("/")
        if 'sites' in relative_url_folder_path:
            target_folder_url = f'/{relative_url_folder_path}'
        else:
            target_folder_url = f'{self.download_path}/{relative_url_folder_path}'
        folders = session.web.get_folder_by_server_relative_path(target_folder_url).folders
        session.load(folders).execute_query()
        
        employee_code_folder_map = {i.properties["Name"]: i.properties["ServerRelativeUrl"] for i in folders}
        with ThreadPoolExecutor(max_workers=5) as pool:
            employee_excel_and_pdf_files_relative_url = tuple(pool.map(_get_child_files_relative_url_by_parent_folder_and_download, employee_code_folder_map.items()))
        
        oracle_hook = OracleHook(oracle_conn_id=self.oracle_conn_id)
        shop_by_province_df = oracle_hook.get_pandas_df("SELECT SHORT_NM, SHOP_NM, PROVICE FROM BACHPX.PHAP_CHE_ORG_CHART")
        shop_by_province_map = shop_by_province_df.set_index('SHORT_NM').apply(tuple, axis=1).to_dict()
    
        excel_localfile_path_pickle_file = os.path.join(local_saved_folder,f'{process_date}_excel_localfile_path.pickle')
        with open(excel_localfile_path_pickle_file, 'wb') as file:
            pickle.dump(tuple(j for i in employee_excel_and_pdf_files_relative_url for j in i[-1]), file, protocol=pickle.HIGHEST_PROTOCOL)
        
        sharepoint_file_relative_url = tuple(j for i in employee_excel_and_pdf_files_relative_url for j in i[1])
        ### Loại những file pdf không có shopcode chuẩn
        filtered_sharepoint_file_relative_url = sharepoint_file_relative_url
                
        sharepoint_file_relative_url_pickle_file = os.path.join(local_saved_folder,f'{process_date}_sharepoint_files_relative_url.pickle')
        with open(sharepoint_file_relative_url_pickle_file, 'wb') as file:
            pickle.dump(tuple(filtered_sharepoint_file_relative_url), file, protocol=pickle.HIGHEST_PROTOCOL)
            
        ### Gather pdfs to Shopcode
        shop_map_pdf_file_local_path = {}
        failed_shop_code = set()
        for _, _, pdf_files, _ in employee_excel_and_pdf_files_relative_url:
            for shop_code, list_pdf_files in pdf_files.items():
                # if not list_pdf_files:
                #     continue
                a = shop_by_province_map.get(shop_code, None)
                if a:
                    target_shop_url = os.path.join(self.folder_destination_url, a[1], a[0])
                    for pdf_file_url in list_pdf_files:
                        if target_shop_url in shop_map_pdf_file_local_path:
                            shop_map_pdf_file_local_path[target_shop_url].append(pdf_file_url)
                        else:
                            shop_map_pdf_file_local_path[target_shop_url] = [pdf_file_url]
                else:
                    failed_shop_code.add(shop_code)
        self.log.info('List Shop code %s not exists. Please check your province table...', str(list(failed_shop_code)))
        
        shop_map_pdf_file_local_path_pickle_file = os.path.join(local_saved_folder,f'{process_date}_shop_map_pdf_file_local_path.pickle')
        with open(shop_map_pdf_file_local_path_pickle_file, 'wb') as file:
            pickle.dump(shop_map_pdf_file_local_path, file, protocol=pickle.HIGHEST_PROTOCOL)

        return shop_map_pdf_file_local_path_pickle_file, excel_localfile_path_pickle_file, sharepoint_file_relative_url_pickle_file
    
class Excel2CSVHook(BaseHook):
    def __init__(self, file_path: str | List[str] | Tuple[str], sheet_name: str | List[str] | Tuple[str], *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_path = [file_path] if isinstance(file_path, str) else file_path
        self.sheet_name = [sheet_name] if isinstance(sheet_name, str) else sheet_name
        
    def _convert_excel_to_csv(self, file_path):
        for sheet in self.sheet_name:
            self.log.info(f'Starting to convert file: {os.path.dirname(file_path)} sheet name: {sheet} to csv')
            new_csv_file_path = os.path.join(os.path.dirname(file_path), re.sub('.xls\w{0,1}$', '', os.path.basename(file_path)) + '_' + sheet + '.csv')
            ### Use in2csv written in Python
            # convert_cmd = f"in2csv --format xls -e 'utf-8' --encoding-xls 'utf-8' --sheet '{sheet}' '{file_path}' > '{new_csv_file_path}'"
            
            # ### Use xlsx-to-csv written in Go
            convert_cmd = f"xlsx-to-csv -s '{sheet}' -o '{new_csv_file_path}' '{file_path}'"
            self.log.info('Command: {0}'.format(convert_cmd))
            res = subprocess.run(convert_cmd, shell=True, capture_output=True, text=True, env=dict(PATH='/opt/python/bin:/usr/bin'))
            self.log.info('Error message: {0}'.format(res.stderr))
        
    def run(self):
        with ThreadPoolExecutor(max_workers=20) as pool:
            res = tuple(pool.map(self._convert_excel_to_csv, self.file_path))

class ConcurrentUploadHook(UploadSharePointHook):
    def __init__(self, sharepoint_conn_id: str) -> None:
        super().__init__(sharepoint_conn_id)
        
    def _concurrent_upload_file(self, upload_path_and_local_file_path: Tuple[Tuple[str, List[str]]]):
        upload_path, local_file_path = upload_path_and_local_file_path
        
        @retry(wait=wait_fixed(10), stop=stop_after_attempt(5))
        def _upload_file(file_path: str):
            if os.path.exists(file_path):
                session = self.get_conn()
                try:
                    folder = session.web.get_folder_by_server_relative_path(upload_path)
                    with open(file_path, 'rb') as file: 
                        folder.files.create_upload_session(file, 1000000).execute_query()
                except ClientRequestException as e:
                    if '404 Client Error: Not Found for url' not in str(e):
                        raise e
                    else:
                        self.log.info(str(e))
                        self.log.info('404 Client Error: Not Found for url. Start create folder and upload file...')
                        folder = session.web.ensure_folder_path(upload_path).execute_query()
                        with open(file_path, 'rb') as file: 
                            folder.files.create_upload_session(file, 1000000).execute_query()
                self.log.info("File {0} has been uploaded successfully".format(os.path.basename(file_path)))
            self.log.info('No such file %s, skipping...' % file_path)
        
        with ThreadPoolExecutor(max_workers=3) as pool:
            res = list(pool.map(_upload_file, local_file_path))
            
    def run(self, target_folders_map_list_local_files: Dict[str, List[str]]):
        with ThreadPoolExecutor(max_workers=3) as pool:
            res = list(pool.map(self._concurrent_upload_file, target_folders_map_list_local_files.items()))

### Create operators
class ManipulateFoldersAndFilesOperator(BaseOperator):
    def __init__(self, sharepoint_conn_id: str, oracle_conn_id: str, local_saved_folder: str, folder_destination_url: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.local_saved_folder = local_saved_folder
        self.folder_destination_url = folder_destination_url
        self.oracle_conn_id = oracle_conn_id
    
    def execute(self, context: Context) -> Any:
        self.parameters = DAGMonitor.set_params_operator(**context)
        process_date = self.parameters['TODAY']
        
        sp_download = ManipulateFoldersAndFilesHook(sharepoint_conn_id=self.sharepoint_conn_id, oracle_conn_id=self.oracle_conn_id, folder_destination_url=self.folder_destination_url)
        shop_map_pdf_file_local_path_pickle_file, excel_localfile_path_pickle_file, sharepoint_file_relative_url_pickle_file = sp_download.run(relative_url_folder_path=relative_url_folder_path, local_saved_folder=local_saved_folder, process_date=process_date)
        
        return shop_map_pdf_file_local_path_pickle_file, excel_localfile_path_pickle_file, sharepoint_file_relative_url_pickle_file
        
class ConcurrentUploadOperator(BaseOperator):
    def __init__(self, sharepoint_conn_id: str, parent_xcom_task_id: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.parent_xcom_task_id = parent_xcom_task_id

    def execute(self, context: Context) -> Any:
        self.parameters = DAGMonitor.set_params_operator(**context)
        
        shop_map_pdf_file_local_path_pickle_file = context['ti'].xcom_pull(task_ids=self.parent_xcom_task_id)[0]
        
        with open(shop_map_pdf_file_local_path_pickle_file, 'rb') as f:
            shop_map_pdf_file_local_path_pickle_file = pickle.load(f)
            
        ConcurrentUploadHook(sharepoint_conn_id=self.sharepoint_conn_id).run(shop_map_pdf_file_local_path_pickle_file)

class DeleteFileSharePointOperator(BaseOperator):
    def __init__(self, sharepoint_conn_id: str, parent_xcom_task_id: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.parent_xcom_task_id = parent_xcom_task_id

    def execute(self, context: Context) -> Any:
        
        sharepoint_file_relative_url_pickle_file = context['ti'].xcom_pull(task_ids=self.parent_xcom_task_id)[2]
        
        with open(sharepoint_file_relative_url_pickle_file, 'rb') as f:
            sharepoint_file_relative_url_pickle_file = pickle.load(f)
    
        DeleteFileSharePointHook(sharepoint_conn_id=self.sharepoint_conn_id).run(sharepoint_file_relative_url_pickle_file)

class Excel2CSVOperator(BaseOperator):
    def __init__(self, sheet_name: str | List[str] | Tuple[str], parent_xcom_task_id: str, local_saved_folder: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sheet_name = sheet_name
        self.parent_xcom_task_id = parent_xcom_task_id
        self.local_saved_folder = local_saved_folder

    def execute(self, context: Context) -> Any:
        self.log.info('Remove all csv files before convert...')
        cmd = f"rm '{os.path.join(self.local_saved_folder, '*.csv')}'"
        self.log.info('Command: {0}'.format(cmd))
        subprocess.run(cmd, shell=True)
        
        excel_localfile_path_pickle_file = context['ti'].xcom_pull(task_ids=self.parent_xcom_task_id)[1]
        
        with open(excel_localfile_path_pickle_file, 'rb') as f:
            excel_localfile_path_pickle_file = pickle.load(f)
        
        excel_to_csv = Excel2CSVHook(file_path=excel_localfile_path_pickle_file, sheet_name=self.sheet_name)
        
        excel_to_csv.run()

def delete_downloaded(folder_path: str) -> None:
    shutil.rmtree(folder_path, ignore_errors=True)
    logging.info(f"Deleted folder: {folder_path}")

def get_file_url_in_folders():
    oracle_conn_id='oracle_f88_dwh'
    def get_file_url_in_folder(folder_url: str):
        session = DownloadSharePointHook(sharepoint_conn_id='sharepoint_f88_data').get_conn()
        files = session.web.get_folder_by_server_relative_path(folder_url).files
        session.load(files).execute_query()
        share_point_files = [f.properties["ServerRelativeUrl"].split('/')[-3:] for f in files]
        return share_point_files
    oracle_hook = OracleHook(oracle_conn_id=oracle_conn_id)
    folder_url = oracle_hook.get_pandas_df("SELECT '/sites/F88-DATA/Shared Documents/SharePoint to Data Warehouse/Project - PCTT/Hồ sơ pháp lý PGD' || '/' || PROVICE || '/' || SHOP_NM as FOLDER_URL FROM BACHPX.PHAP_CHE_ORG_CHART")['FOLDER_URL']
    with ThreadPoolExecutor(max_workers=1) as pool:
        res = pool.map(get_file_url_in_folder, folder_url)
    DDLHook(oracle_conn_id=oracle_conn_id).truncate_table(table_name='F88DWH.W_PCTT_PDF_FILES')
    oracle_hook.bulk_insert_rows(table='F88DWH.W_PCTT_PDF_FILES', rows=tuple(chain.from_iterable(res)), target_fields=['PROVINCE', 'SHOP_NAME', 'FILE_NAME'])

class CheckFileBeforeProcessOperator(BaseOperator):
    def __init__(self, local_saved_folder: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.local_saved_folder = local_saved_folder
        self.xlsx_version = OracleHook(oracle_conn_id='oracle_f88_dwh').get_first('SELECT VERSION_NUMBER FROM BACHPX.W_VERSION_PHAP_CHE')[0]
        
    def check_file_excel_pdf_before_process(self, xlsx_file_path: List[str]):
        parent_dir = self.local_saved_folder
        sheet_name = 'Ten_File'
        employee_code = os.path.basename(xlsx_file_path[0]).split('_')[0]
        actual_pdf_file_name = set(['_'.join(os.path.basename(i).split('_')[2:]) for i in glob.glob(os.path.join(parent_dir, f'{employee_code}*.pdf'))])
        expected_pdf_file_name = set()
        for f in xlsx_file_path:
            convert_cmd = f"xlsx-to-csv -s '{sheet_name}' '{f}'"
            res = subprocess.run(convert_cmd, shell=True, capture_output=True, text=True, env=dict(PATH='/opt/python/bin:/usr/bin'))
            if res.stderr:
                if res.stderr.strip() == f"sheet '{sheet_name}' does not exist":
                    # Remove all files with employee_code
                    self.log.info('Need to remove employee %s files:' % employee_code)
                    cmd = "find '%s' -name '%s*' -exec rm {} \;" % (parent_dir, employee_code)
                    self.log.info(cmd)
                    res = subprocess.run(cmd, shell=True)
                    return (employee_code, 'Không tồn tại sheet "File_Name" trong file excel')
            else:
                data = csv.reader(StringIO(res.stdout), delimiter=',')
                file_version = next(data, [None, None, None])[1]
                if file_version.strip() != self.xlsx_version:
                    self.log.info('Need to remove employee %s files:' % employee_code)
                    cmd = "find '%s' -name '%s*' -exec rm {} \;" % (parent_dir, employee_code)
                    self.log.info(cmd)
                    res = subprocess.run(cmd, shell=True)
                    return (employee_code, 'Sử dụng file excel cũ, không đúng version mới nhất')
                for row in data:
                    if row[-1] != employee_code:
                        # Remove all files with employee_code
                        self.log.info('Need to remove employee %s files:' % employee_code)
                        cmd = "find '%s' -name '%s*' -exec rm {} \;" % (parent_dir, employee_code)
                        self.log.info(cmd)
                        res = subprocess.run(cmd, shell=True)
                        return (employee_code, 'Mã nhân viên trong file excel không khớp với mã nhân viên trên thư mục SharePoint')

                    expected_pdf_file_name.add(row[0])
        if (actual_pdf_file_name-expected_pdf_file_name) or (expected_pdf_file_name-actual_pdf_file_name):
            self.log.info('Need to remove employee %s files:' % employee_code)
            cmd = "find '%s' -name '%s*' -exec rm {} \;" % (parent_dir, employee_code)
            self.log.info(cmd)
            res = subprocess.run(cmd, shell=True)
            return (employee_code, 'Số lượng file PDF liệt kê trong file excel không khớp với file thực tế')
    
    def execute(self, context: Context) -> Any:
        xlsx_map = {}
        xlsx_file_path = glob.glob(os.path.join(self.local_saved_folder, '*.xls*'))
        pdf_file_path = glob.glob(os.path.join(self.local_saved_folder, '*.pdf'))
        employee_set_xlsx = set()
        employee_set_pdf = set()
        
        for path in xlsx_file_path:
            employee_code = os.path.basename(path).split('_')[0]
            employee_set_xlsx.add(employee_code)
            if employee_code in xlsx_map:
                xlsx_map[employee_code].append(path)
            else:
                xlsx_map[employee_code] = [path]
        
        for path in pdf_file_path:
            employee_code = os.path.basename(path).split('_')[0]
            employee_set_pdf.add(employee_code)

        with ThreadPoolExecutor(max_workers=3) as pool:
            res = set(pool.map(self.check_file_excel_pdf_before_process, xlsx_map.values()))
        
        not_pass_employee_code = employee_set_pdf - employee_set_xlsx
        
        for emp in not_pass_employee_code:
            self.log.info('Need to remove employee %s files:' % emp)
            cmd = "find '%s' -name '%s*' -exec rm {} \;" % (self.local_saved_folder, emp)
            self.log.info(cmd)
            subprocess.run(cmd, shell=True)
        
        res = dict([i for i in res if i] + list((i, 'Có upload file PDF nhưng không upload file excel') for i in not_pass_employee_code))
        
        employee_wrong_pdf_name_format = set()
        pdf_file_path = glob.glob(os.path.join(self.local_saved_folder, '*.pdf'))
        for path in pdf_file_path:
            employee_code = os.path.basename(path).split('_')[0]
            if not re.findall(r'[A-Z]{3}\d{5}', os.path.basename(path)):
                employee_wrong_pdf_name_format.add(employee_code)
        for employee_code in employee_wrong_pdf_name_format:
            if employee_code in res:
                res[employee_code] = res[employee_code] + ', ' + 'Tên file PDF không chứa mã phòng giao dịch'
            else:
                res.update({employee_code: 'Tên file PDF không chứa mã phòng giao dịch'})
        
        employee_email_sql = "SELECT EMPLOYEE_CODE, EMPLOYEE_USER_NM, email FROM F88DWH.W_EMPLOYEE_D WHERE EMPLOYEE_CODE IN ('%s') AND CRN_ROW_IND = 1"  % "', '".join(res.keys())
        
        employee_email_result = OracleHook(oracle_conn_id='oracle_f88_dwh').get_records(employee_email_sql)
        
        employee_email =[i[-1] for i in employee_email_result] + ['<EMAIL>', '<EMAIL>', '<EMAIL>'] #<EMAIL> <EMAIL>
        
        if len(employee_email) > 3:
            send_email(to_email=','.join(employee_email), employee_failed_with_error_msg=[(i[0], i[1], res[i[0]]) for i in employee_email_result])

with DAG(dag_id='PTKD_DAILY_PROCESS_FILE_KDML_PCTT', description='Xử lý dữ liệu báo cáo PCTT và xử lý đẩy file pdf của các PGD',
         default_args={
             'owner': 'F88-DE',
             'retries': 3,
            'retry_delay': timedelta(minutes=1),
             'trigger_rule': 'all_success'
         },
         start_date=datetime(2023, 12, 15),
         catchup=False,
         schedule_interval="@daily",
         max_active_runs=1,
         tags=['PCTT','daily','sharepoint2dwh','ngoaile']
         ) as dag:
    process_date = pendulum.now(tz='Asia/Ho_Chi_Minh').strftime('%Y%m%d')
    local_saved_folder = set_saved_folder_and_file_name(False, None, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Downloaded'), None, 'Project - PTKD/PCTT_Hồ sơ pháp lý PGD', process_date[:4], process_date[4:6], process_date[6:])[0]
    
    if not os.path.exists(local_saved_folder):
        os.makedirs(local_saved_folder)
    ### Prod
    folder_destination_url = '/sites/F88-DATA/Shared Documents/SharePoint to Data Warehouse/Project - PCTT/Hồ sơ pháp lý PGD'
    ### Prod
    relative_url_folder_path = '/sites/F88-DATA/Shared Documents/SharePoint to Data Warehouse/Project - PTKD/PCTT_Hồ sơ pháp lý PGD/'
    
    # ### Test
    # folder_destination_url = '/sites/F88-DATA/Shared Documents/Test/SharePoint to Data Warehouse/Project - PCTT/Hồ sơ pháp lý PGD'
    # ### Test
    # relative_url_folder_path = '/sites/F88-DATA/Shared Documents/Test/SharePoint to Data Warehouse/Project - PTKD/PCTT_Hồ sơ pháp lý PGD/'
    
    sheet_name = ('DS_Checklist', 'Database_DS_DKKD', 'Database_DS_ANTT', 'Database_DS_CC_PCCC', 'Database_BB_KTRA', 'Database_QD_XUPHAT')
    oracle_conn_id = 'oracle_f88_dwh'
    sharepoint_conn_id = 'sharepoint_f88_data'
    parent_xcom_task_id = 'download_excel_and_pdf'
    table_metadata_list = ListTableMetaData(list=[{"xlsx_sheet_name":"DS_Checklist","xlsx_read_options":{"skiprows":1},"rename_columns":{"Mã":"STT","Tên phòng giao dịch":"SHOP_NAME","Mã phòng giao dịch":"SHOP_CODE","Loại giấy tờ":"LOAI_GIAY_TO","Mô tả chi tiết":"MO_TA_CHI_TIET","Trạng thái\n(Có, Không)":"TRANG_THAI","Ten_File":"TEN_FILE"},"list_columns_to_ingest_to_stg":['STT', 'SHOP_NAME', 'SHOP_CODE', 'LOAI_GIAY_TO', 'MO_TA_CHI_TIET', 'TRANG_THAI', 'TEN_FILE'],"stg_table_schema":{"table_name":"STGPROD.PCTT_DS_CHECKLIST","columns_datatype":{'STT': 'VARCHAR2(1000)', 'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'LOAI_GIAY_TO': 'VARCHAR2(1000)', 'MO_TA_CHI_TIET': 'VARCHAR2(1000)', 'TRANG_THAI': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT DISTINCT TO_NUMBER(REGEXP_REPLACE(STT, '^nan$|^$|^,$')) AS STT, SHOP_NAME, SHOP_CODE, LOAI_GIAY_TO, MO_TA_CHI_TIET, TRANG_THAI, TEN_FILE, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE from STGPROD.PCTT_DS_CHECKLIST WHERE SHOP_CODE <> 'Ten_File'","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_DS_CHECKLIST","columns_datatype":{'STT': 'NUMBER', 'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'LOAI_GIAY_TO': 'VARCHAR2(1000)', 'MO_TA_CHI_TIET': 'VARCHAR2(1000)', 'TRANG_THAI': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)', 'PROCESS_DATE': 'DATE'},"merge_key":['STT', 'SHOP_NAME', 'SHOP_CODE', 'LOAI_GIAY_TO', 'MO_TA_CHI_TIET', 'TRANG_THAI', 'TEN_FILE']}},{'xlsx_sheet_name': 'Database_DS_DKKD', 'rename_columns': {'Tên phòng giao dịch': 'SHOP_NAME', 'Mã': 'SHOP_CODE', 'Số PGD': 'SHOP_NUM', 'Mã số ĐKKD': 'MA_DKKD', 'Lần cấp': 'LAN_CAP', 'Ngày cấp': 'NGAY_CAP', 'Tên ĐKKD': 'TEN_DKKD', 'Miền': 'MIEN', 'Cấp tỉnh': 'TINH', 'Cấp huyện': 'HUYEN', 'Địa chỉ chi tiết theo ĐKKD': 'DIACHI_THEO_DKKD', 'Người đứng tên': 'NGUOI_DUNG_TEN', 'Ngày sinh': 'NGAY_SINH', 'Số CCCD/CMT': 'CCCD_CMT', 'Ngày cấp CMT': 'NGAYCAP_CMT', 'Nơi cấp': 'NOI_CAP', 'Cty/Ngoài': 'CTY_NGOAI', 'Chức vụ': 'CHUC_VU', 'ID': 'ID', 'Ten_File': 'TEN_FILE', 'Ngay_Cap_Nhat': 'NGAY_CAP_NHAT'}, 'list_columns_to_ingest_to_stg': ['SHOP_NAME', 'SHOP_CODE', 'SHOP_NUM', 'MA_DKKD', 'LAN_CAP', 'NGAY_CAP', 'TEN_DKKD', 'MIEN', 'TINH', 'HUYEN', 'DIACHI_THEO_DKKD', 'NGUOI_DUNG_TEN', 'NGAY_SINH', 'CCCD_CMT', 'NGAYCAP_CMT', 'NOI_CAP', 'CTY_NGOAI', 'CHUC_VU', 'ID', 'TEN_FILE', 'NGAY_CAP_NHAT'], 'stg_table_schema': {'table_name': 'STGPROD.PCTT_DS_DKKD', 'columns_datatype': {'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'SHOP_NUM': 'VARCHAR2(1000)', 'MA_DKKD': 'VARCHAR2(1000)', 'LAN_CAP': 'VARCHAR2(1000)', 'NGAY_CAP': 'VARCHAR2(1000)', 'TEN_DKKD': 'VARCHAR2(1000)', 'MIEN': 'VARCHAR2(1000)', 'TINH': 'VARCHAR2(1000)', 'HUYEN': 'VARCHAR2(1000)', 'DIACHI_THEO_DKKD': 'VARCHAR2(1000)', 'NGUOI_DUNG_TEN': 'VARCHAR2(1000)', 'NGAY_SINH': 'VARCHAR2(1000)', 'CCCD_CMT': 'VARCHAR2(1000)', 'NGAYCAP_CMT': 'VARCHAR2(1000)', 'NOI_CAP': 'VARCHAR2(1000)', 'CTY_NGOAI': 'VARCHAR2(1000)', 'CHUC_VU': 'VARCHAR2(1000)', 'ID': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)'}}, 'sql_transform_stg_to_dwh': """SELECT distinct SHOP_NAME, SHOP_CODE, SHOP_NUM, MA_DKKD, LAN_CAP, to_date(REGEXP_REPLACE(NGAY_CAP, '^nan$|^$|^,$'), 'DD-MON-YYYY') AS NGAY_CAP, TEN_DKKD, MIEN, TINH, HUYEN, DIACHI_THEO_DKKD, NGUOI_DUNG_TEN, to_date(REGEXP_REPLACE(NGAY_SINH, '^nan$|^$|^,$'), 'DD-MON-YYYY') AS NGAY_SINH, CCCD_CMT, to_date(REGEXP_REPLACE(NGAYCAP_CMT, '^nan$|^$|^,$'), 'DD-MON-YYYY') AS NGAYCAP_CMT, NOI_CAP, CTY_NGOAI, CHUC_VU, TO_NUMBER(REGEXP_REPLACE(ID, '^nan$|^$|^,$')) AS ID, TEN_FILE, NGAY_CAP_NHAT, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE from STGPROD.PCTT_DS_DKKD""", 'dwh_table_schema': {'table_name': 'F88DWH.W_PCTT_DS_DKKD', 'columns_datatype': {'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'SHOP_NUM': 'VARCHAR2(1000)', 'MA_DKKD': 'VARCHAR2(1000)', 'LAN_CAP': 'VARCHAR2(1000)', 'NGAY_CAP': 'DATE', 'TEN_DKKD': 'VARCHAR2(1000)', 'MIEN': 'VARCHAR2(1000)', 'TINH': 'VARCHAR2(1000)', 'HUYEN': 'VARCHAR2(1000)', 'DIACHI_THEO_DKKD': 'VARCHAR2(1000)', 'NGUOI_DUNG_TEN': 'VARCHAR2(1000)', 'NGAY_SINH': 'DATE', 'CCCD_CMT': 'VARCHAR2(1000)', 'NGAYCAP_CMT': 'DATE', 'NOI_CAP': 'VARCHAR2(1000)', 'CTY_NGOAI': 'VARCHAR2(1000)', 'CHUC_VU': 'VARCHAR2(1000)', 'ID': 'NUMBER', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)', 'PROCESS_DATE': 'DATE'}, 'merge_key': ['SHOP_CODE','NGAY_CAP_NHAT','TEN_FILE']}},{"xlsx_sheet_name":"Database_DS_CC_PCCC","rename_columns":{'Đơn vị': 'SHOP_NAME', 'Mã đơn vị': 'SHOP_CODE', 'Họ và tên': 'HO_VA_TEN', 'Mã nhân viên': 'MA_NV', 'Ngày cấp': 'NGAY_CAP',
       'Ngày hết hạn': 'NGAY_HET_HAN', 'Ghi chú': 'GHI_CHU', 'ID': 'ID', 'Ten_File': 'TEN_FILE', 'Ngay_Cap_Nhat': 'NGAY_CAP_NHAT'},"list_columns_to_ingest_to_stg":['SHOP_NAME', 'SHOP_CODE', 'HO_VA_TEN', 'MA_NV', 'NGAY_CAP', 'NGAY_HET_HAN', 'GHI_CHU', 'ID', 'TEN_FILE', 'NGAY_CAP_NHAT'],"stg_table_schema":{"table_name":"STGPROD.PCTT_DS_CC_PCCC","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'HO_VA_TEN': 'VARCHAR2(1000)', 'MA_NV': 'VARCHAR2(1000)', 'NGAY_CAP': 'VARCHAR2(1000)', 'NGAY_HET_HAN': 'VARCHAR2(1000)', 'GHI_CHU': 'VARCHAR2(1000)', 'ID': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"""SELECT distinct SHOP_NAME, SHOP_CODE, HO_VA_TEN, MA_NV, TO_DATE(NGAY_CAP, 'DD-MON-YYYY') as NGAY_CAP, TO_DATE(NGAY_HET_HAN, 'DD-MON-YYYY') as NGAY_HET_HAN, REGEXP_REPLACE(GHI_CHU, '^nan$|^$|^,$') AS GHI_CHU, TO_NUMBER(REGEXP_REPLACE(ID, '^nan$|^$|^,$')) AS ID, TEN_FILE, NGAY_CAP_NHAT, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE from STGPROD.PCTT_DS_CC_PCCC""","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_DS_CC_PCCC","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'HO_VA_TEN': 'VARCHAR2(1000)', 'MA_NV': 'VARCHAR2(1000)', 'NGAY_CAP': 'DATE', 'NGAY_HET_HAN': 'DATE', 'GHI_CHU': 'VARCHAR2(1000)', 'ID': 'NUMBER', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)', 'PROCESS_DATE': 'DATE'},"merge_key":["SHOP_CODE","TEN_FILE","NGAY_CAP_NHAT"]}},{"xlsx_sheet_name":"Database_DS_ANTT","rename_columns":{'Tên phòng giao dịch': 'SHOP_NAME', 'Mã': 'SHOP_CODE', 'Ngày cấp': 'NGAY_CAP', 'Tên cơ sở kinh doanh': 'TEN_CO_SO_KD', 'Miền': 'MIEN',
       'Cấp tỉnh': 'TINH', 'Cấp huyện': 'HUYEN', 'Địa chỉ đầy đủ': 'DIA_CHI', 'Người đứng tên': 'NGUOI_DUNG_TEN',
       'Ngày sinh': 'NGAY_SINH', 'Số CCCD/CMT': 'CCCD_CMT', 'Ngày cấp CMT': 'NGAYCAP_CMT', 'Nơi cấp': 'NOI_CAP', 'Cty/Ngoài': 'CTY_NGOAI',
       'Chức vụ': 'CHUC_VU', 'ID': 'ID', 'Ten_File': 'TEN_FILE', 'Ngay_Cap_Nhat': 'NGAY_CAP_NHAT'},"list_columns_to_ingest_to_stg":['SHOP_NAME', 'SHOP_CODE', 'NGAY_CAP', 'TEN_CO_SO_KD', 'MIEN', 'TINH', 'HUYEN', 'DIA_CHI', 'NGUOI_DUNG_TEN', 'NGAY_SINH', 'CCCD_CMT', 'NGAYCAP_CMT', 'NOI_CAP', 'CTY_NGOAI', 'CHUC_VU', 'ID', 'TEN_FILE', 'NGAY_CAP_NHAT'],"stg_table_schema":{"table_name":"STGPROD.PCTT_DS_ANTT","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'NGAY_CAP': 'VARCHAR2(1000)', 'TEN_CO_SO_KD': 'VARCHAR2(1000)', 'MIEN': 'VARCHAR2(1000)', 'TINH': 'VARCHAR2(1000)', 'HUYEN': 'VARCHAR2(1000)', 'DIA_CHI': 'VARCHAR2(1000)', 'NGUOI_DUNG_TEN': 'VARCHAR2(1000)', 'NGAY_SINH': 'VARCHAR2(1000)', 'CCCD_CMT': 'VARCHAR2(1000)', 'NGAYCAP_CMT': 'VARCHAR2(1000)', 'NOI_CAP': 'VARCHAR2(1000)', 'CTY_NGOAI': 'VARCHAR2(1000)', 'CHUC_VU': 'VARCHAR2(1000)', 'ID': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT DISTINCT SHOP_NAME, SHOP_CODE, TO_DATE(NGAY_CAP, 'DD-MON-YYYY') as NGAY_CAP, TEN_CO_SO_KD, MIEN, TINH, HUYEN, DIA_CHI, NGUOI_DUNG_TEN, TO_DATE(NGAY_SINH, 'DD-MON-YYYY') as NGAY_SINH, CCCD_CMT, TO_DATE(NGAYCAP_CMT, 'DD-MON-YYYY') as NGAYCAP_CMT, NOI_CAP, CTY_NGOAI, CHUC_VU, TO_NUMBER(REGEXP_REPLACE(ID, '^nan$|^$|^,$')) AS ID, TEN_FILE, NGAY_CAP_NHAT, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE from STGPROD.PCTT_DS_ANTT","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_DS_ANTT","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'NGAY_CAP': 'DATE', 'TEN_CO_SO_KD': 'VARCHAR2(1000)', 'MIEN': 'VARCHAR2(1000)', 'TINH': 'VARCHAR2(1000)', 'HUYEN': 'VARCHAR2(1000)', 'DIA_CHI': 'VARCHAR2(1000)', 'NGUOI_DUNG_TEN': 'VARCHAR2(1000)', 'NGAY_SINH': 'DATE', 'CCCD_CMT': 'VARCHAR2(1000)', 'NGAYCAP_CMT': 'DATE', 'NOI_CAP': 'VARCHAR2(1000)', 'CTY_NGOAI': 'VARCHAR2(1000)', 'CHUC_VU': 'VARCHAR2(1000)', 'ID': 'NUMBER', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)', 'PROCESS_DATE': 'DATE'},"merge_key":["SHOP_CODE","NGAY_CAP_NHAT","TEN_FILE"]}},{"xlsx_sheet_name":"Database_BB_KTRA","rename_columns":{'Tên phòng giao dịch': 'SHOP_NAME', 'Mã': 'SHOP_CODE', 'Đơn vị ktra': 'DONVI_KTRA', 'Trưởng đoàn ktra': 'TRUONGDOAN_KTRA', 'Số ĐT': 'SO_DTDT',
       'Ngày kiểm tra': 'NGAY_KIEM_TRA', 'Hành vi Vi phạm hành chính/lỗi (nếu có)': 'HANHVI_VIPHAM',
       'Giải trình (nếu có)': 'GIAI_TRINH', 'ID': 'ID', 'Ten_File': 'TEN_FILE', 'Ngay_Cap_Nhat': 'NGAY_CAP_NHAT'},"list_columns_to_ingest_to_stg":['SHOP_NAME', 'SHOP_CODE', 'DONVI_KTRA', 'TRUONGDOAN_KTRA', 'SO_DTDT', 'NGAY_KIEM_TRA', 'HANHVI_VIPHAM', 'GIAI_TRINH', 'ID', 'TEN_FILE', 'NGAY_CAP_NHAT'],"stg_table_schema":{"table_name":"STGPROD.PCTT_BB_KTRA","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'DONVI_KTRA': 'VARCHAR2(1000)', 'TRUONGDOAN_KTRA': 'VARCHAR2(1000)', 'SO_DTDT': 'VARCHAR2(1000)', 'NGAY_KIEM_TRA': 'VARCHAR2(1000)', 'HANHVI_VIPHAM': 'VARCHAR2(4000)', 'GIAI_TRINH': 'VARCHAR2(1000)', 'ID': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT DISTINCT SHOP_NAME, SHOP_CODE, DONVI_KTRA, TRUONGDOAN_KTRA, SO_DTDT, TO_DATE(NGAY_KIEM_TRA, 'DD-MON-YYYY') as NGAY_KIEM_TRA, HANHVI_VIPHAM, GIAI_TRINH, TO_NUMBER(REGEXP_REPLACE(ID, '^nan$|^$|^,$')) AS ID, TEN_FILE, NGAY_CAP_NHAT, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE from STGPROD.PCTT_BB_KTRA","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_BB_KTRA","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'DONVI_KTRA': 'VARCHAR2(1000)', 'TRUONGDOAN_KTRA': 'VARCHAR2(1000)', 'SO_DTDT': 'VARCHAR2(1000)', 'NGAY_KIEM_TRA': 'DATE', 'HANHVI_VIPHAM': 'VARCHAR2(4000)', 'GIAI_TRINH': 'VARCHAR2(1000)', 'ID': 'NUMBER', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)', 'PROCESS_DATE': 'DATE'},"merge_key":["SHOP_CODE", 'NGAY_CAP_NHAT', 'TEN_FILE']}},{"xlsx_sheet_name":"Database_QD_XUPHAT","rename_columns":{'Tên phòng giao dịch': 'SHOP_NAME', 'Mã': 'SHOP_CODE', 'Đơn vị xử phạt': 'DONVI_XUPHAT', 'Ngày xử phạt': 'NGAY_XUPHAT',
       'Ngày hiệu lực':'NGAY_HIEU_LUC', 'Ngày hết hiệu lực (nếu có)': 'NGAY_HET_HIEU_LUC',
       'Hành vi Vi phạm hành chính': 'HANHVI_VIPHAM', 'Số tiền phạt': 'SO_TIEN_PHAT',
       'Hình phạt bổ sung (nếu có)':'HINHPHAT_BOSUNG', 'ID': 'ID', 'Ten_File': 'TEN_FILE', 'Ngay_Cap_Nhat': 'NGAY_CAP_NHAT'},"list_columns_to_ingest_to_stg":['SHOP_NAME', 'SHOP_CODE', 'DONVI_XUPHAT', 'NGAY_XUPHAT', 'NGAY_HIEU_LUC', 'NGAY_HET_HIEU_LUC', 'HANHVI_VIPHAM', 'SO_TIEN_PHAT', 'HINHPHAT_BOSUNG', 'ID', 'TEN_FILE', 'NGAY_CAP_NHAT'],"stg_table_schema":{"table_name":"STGPROD.PCTT_QD_XUPHAT","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'DONVI_XUPHAT': 'VARCHAR2(1000)', 'NGAY_XUPHAT': 'VARCHAR2(1000)', 'NGAY_HIEU_LUC': 'VARCHAR2(1000)', 'NGAY_HET_HIEU_LUC': 'VARCHAR2(1000)', 'HANHVI_VIPHAM': 'VARCHAR2(4000)', 'SO_TIEN_PHAT': 'VARCHAR2(1000)', 'HINHPHAT_BOSUNG': 'VARCHAR2(1000)', 'ID': 'VARCHAR2(1000)', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT distinct SHOP_NAME, SHOP_CODE, DONVI_XUPHAT, TO_DATE(NGAY_XUPHAT, 'DD-MON-YYYY') as NGAY_XUPHAT, TO_DATE(NGAY_HIEU_LUC, 'DD-MON-YYYY') as NGAY_HIEU_LUC, TO_DATE(REGEXP_REPLACE(NGAY_HET_HIEU_LUC, '^nan$|^$|^,$'), 'DD-MON-YYYY') as NGAY_HET_HIEU_LUC, HANHVI_VIPHAM, to_number(REGEXP_REPLACE(REPLACE(SO_TIEN_PHAT,'.',''), '^nan$|^$|^,$')) AS SO_TIEN_PHAT, HINHPHAT_BOSUNG, TO_NUMBER(REGEXP_REPLACE(ID, '^nan$|^$|^,$')) AS ID, TEN_FILE, NGAY_CAP_NHAT, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE from STGPROD.PCTT_QD_XUPHAT","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_QD_XUPHAT","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'DONVI_XUPHAT': 'VARCHAR2(1000)', 'NGAY_XUPHAT': 'DATE', 'NGAY_HIEU_LUC': 'DATE', 'NGAY_HET_HIEU_LUC': 'DATE', 'HANHVI_VIPHAM': 'VARCHAR2(4000)', 'SO_TIEN_PHAT': 'NUMBER', 'HINHPHAT_BOSUNG': 'VARCHAR2(1000)', 'ID': 'NUMBER', 'TEN_FILE': 'VARCHAR2(1000)', 'NGAY_CAP_NHAT': 'VARCHAR2(1000)', 'PROCESS_DATE': 'DATE'},"merge_key":["SHOP_CODE", 'TEN_FILE', 'NGAY_CAP_NHAT']}}])
    
    ### Task get all excel, pdf url and download excel file to local:
    download_excel_and_pdf = ManipulateFoldersAndFilesOperator(task_id = parent_xcom_task_id, sharepoint_conn_id = sharepoint_conn_id, oracle_conn_id=oracle_conn_id, local_saved_folder = local_saved_folder, folder_destination_url=folder_destination_url)
    
    ### Task cross check file excel and pdf
    
    check_file_before_process_task = CheckFileBeforeProcessOperator(task_id='check_file_before_process_task', local_saved_folder=local_saved_folder)
    
    ### Task upload all pdf files
    concurrent_upload_pdf_files = ConcurrentUploadOperator(task_id = 'concurrent_upload_pdf_files', sharepoint_conn_id = sharepoint_conn_id, parent_xcom_task_id=parent_xcom_task_id)
    
    ### Task convert excel files to csv
    convert_excel_to_csv = Excel2CSVOperator(task_id = 'convert_excel_to_csv', sheet_name=sheet_name, parent_xcom_task_id=parent_xcom_task_id, local_saved_folder=local_saved_folder)
    
    # Delete downloaded
    delete_all_downloaded_local_file = PythonOperator(task_id='delete_all_downloaded_local_file', python_callable=delete_downloaded, op_kwargs={'folder_path': local_saved_folder})

    ### Delete files on share point
    delete_files_on_share_point = DeleteFileSharePointOperator(task_id = 'delete_files_on_share_point', sharepoint_conn_id = sharepoint_conn_id, parent_xcom_task_id=parent_xcom_task_id)

    ### Get PDF FILE REPORT DATA
    pdf_file_data = PythonOperator(task_id='get_pdf_file_data_report', python_callable=get_file_url_in_folders)
    
    ### Insert data to dwh:
    for table_metadata in table_metadata_list.list:
        csv_file_path = os.path.join(local_saved_folder, f'*{table_metadata.xlsx_sheet_name}.csv')
        
        insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=oracle_conn_id, local_file_path=csv_file_path)
        
        create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
                                                    table_schema=table_metadata.stg_table_schema,
                                                    oracle_conn_id=oracle_conn_id,
                                                    insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

        create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}',
                                                table_schema=table_metadata.dwh_table_schema,
                                                oracle_conn_id=oracle_conn_id,
                                                insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)

        transform_table = STG2DWHOperators(
            task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',
            oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema,
            target_table_schema=table_metadata.dwh_table_schema,
            select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)

        convert_excel_to_csv >> create_and_truncate_stg_table >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table >> concurrent_upload_pdf_files >> delete_files_on_share_point >> delete_all_downloaded_local_file
            
    ### Create flow:
    download_excel_and_pdf >> check_file_before_process_task >> convert_excel_to_csv

    
    delete_files_on_share_point >> pdf_file_data

if __name__ == '__main__':
    pass