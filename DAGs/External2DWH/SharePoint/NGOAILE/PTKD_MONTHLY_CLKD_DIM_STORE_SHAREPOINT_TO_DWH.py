import datetime
import logging
import os
from Provider.OracleProvider.operators.DDLOperators import DD<PERSON><PERSON>perator
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.context import Context
from DAGs.utils import DAGMonitor

def run_proc(oracle_conn_id:str, sql:str, **Context) -> None:
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    logging.info('Start running sql code:')
    logging.info(sql)
    hook.callproc(identifier=sql, autocommit=True)

parent_dir = os.path.dirname(os.path.abspath(__file__))
DAG_ID = 'PTKD_MONTHLY_CLKD_DIM_STORE_SHAREPOINT_TO_DWH'
description = 'Main Dag for daily'
table_metadata = [{"xlsx_sheet_name": "DIM_STORE", "datatype_mapping": {"UPDATE_DATE": "datetime64", "GDM_NM": "str", "GDM_CODE": "str", "MIEN": "str", "PHAN_VUNG": "str", "PHAN_VUNG_CODE": "str", "QLV_NM": "str", "QLV_CODE": "str", "KHU_VUC": "str", "QLKV_NM": "str", "QLKV_CODE": "str", "SHOP_NM": "str", "SHORT_NM": "str", "SHOP_ID": "str", "TRADE_MKT": "str", "OPEN_DATE": "datetime64", "CLOSE_DATE": "datetime64", "SHOP_AGE": "str", "LATITUDE": "str", "LONGITUDE": "str", "DIA_HINH": "str", "PROVINCE": "str", "DISTRICT": "str", "WARD": "str", "ADDRESS": "str", "DON_VI_HC": "str", "XEP_HANG_MAT_BANG": "str", "DIEM_MAT_BANG": "str", "XEP_HANG_PGD": "str", "PHAN_HANG_PGD": "str", "OPENING_TIME": "str", "CLOSING_TIME": "str"}, "list_columns_to_ingest_to_stg": ["UPDATE_DATE", "GDM_NM", "GDM_CODE", "MIEN", "PHAN_VUNG", "PHAN_VUNG_CODE", "QLV_NM", "QLV_CODE", "KHU_VUC", "QLKV_NM", "QLKV_CODE", "SHOP_NM", "SHORT_NM", "SHOP_ID", "TRADE_MKT", "OPEN_DATE", "CLOSE_DATE", "SHOP_AGE", "LATITUDE", "LONGITUDE", "DIA_HINH", "PROVINCE", "DISTRICT", "WARD", "ADDRESS", "DON_VI_HC", "XEP_HANG_MAT_BANG", "DIEM_MAT_BANG", "XEP_HANG_PGD", "PHAN_HANG_PGD", "OPENING_TIME", "CLOSING_TIME"], "stg_table_schema": {"table_name": "STGPROD.AREA_MANAGER_MONTHLY_D", "columns_datatype": {"UPDATE_DATE": "DATE", "GDM_NM": "VARCHAR2(30)", "GDM_CODE": "VARCHAR2(30)", "MIEN": "VARCHAR2(30)", "PHAN_VUNG": "VARCHAR2(30)", "PHAN_VUNG_CODE": "VARCHAR2(30)", "QLV_NM": "VARCHAR2(30)", "QLV_CODE": "VARCHAR2(30)", "KHU_VUC": "VARCHAR2(30)", "QLKV_NM": "VARCHAR2(30)", "QLKV_CODE": "VARCHAR2(30)", "SHOP_NM": "VARCHAR2(200)", "SHORT_NM": "VARCHAR2(30)", "SHOP_ID": "VARCHAR2(50)", "TRADE_MKT": "VARCHAR2(30)", "OPEN_DATE": "DATE", "CLOSE_DATE": "DATE", "SHOP_AGE": "VARCHAR2(50)", "LATITUDE": "VARCHAR2(30)", "LONGITUDE": "VARCHAR2(30)", "DIA_HINH": "VARCHAR2(30)", "PROVINCE": "VARCHAR2(30)", "DISTRICT": "VARCHAR2(30)", "WARD": "VARCHAR2(30)", "ADDRESS": "VARCHAR2(100)", "DON_VI_HC": "VARCHAR2(30)", "XEP_HANG_MAT_BANG": "VARCHAR2(30)", "DIEM_MAT_BANG": "VARCHAR2(50)", "XEP_HANG_PGD": "VARCHAR2(50)", "PHAN_HANG_PGD": "VARCHAR2(30)", "OPENING_TIME": "VARCHAR2(30)", "CLOSING_TIME": "VARCHAR2(30)"}}, "sql_transform_stg_to_dwh": """SELECT
        CAST(:YEAR_NUM AS NUMBER) AS YEAR_NUM,
        CAST(:MONTH_NUM AS NUMBER) AS MONTH_NUM,
        AREA_MANAGER_MONTHLY_D.UPDATE_DATE,
        TO_DATE('19000101', 'YYYYMMDD') AS VALID_FM_DT,
        TO_DATE('24000101', 'YYYYMMDD') AS VALID_TO_DT,
        1 AS CRN_ROW_IND,
        REGEXP_REPLACE(GDM_NM, '^nan$|^$|^,$') AS GDM_NM,
        REGEXP_REPLACE(GDM_CODE, '^nan$|^$|^,$') AS GDM_CODE,
        REGEXP_REPLACE(MIEN, '^nan$|^$|^,$') AS MIEN,
        REGEXP_REPLACE(PHAN_VUNG, '^nan$|^$|^,$') AS PHAN_VUNG,
        REGEXP_REPLACE(PHAN_VUNG_CODE, '^nan$|^$|^,$') AS PHAN_VUNG_CODE,
        REGEXP_REPLACE(AREA_MANAGER_MONTHLY_D.QLV_NM, '^nan$|^$|^,$') AS QLV_NM,
        REGEXP_REPLACE(trim(QLV_CODE), '^nan$|^$|^,$') AS QLV_CODE,
        REGEXP_REPLACE(KHU_VUC, '^nan$|^$|^,$') AS KHU_VUC,
        REGEXP_REPLACE(QLKV_NM, '^nan$|^$|^,$') AS QLKV_NM,
        REGEXP_REPLACE(trim(QLKV_CODE), '^nan$|^$|^,$') AS QLKV_CODE,
        REGEXP_REPLACE(SHOP_NM, '^nan$|^$|^,$') AS SHOP_NM,
        REGEXP_REPLACE(SHORT_NM, '^nan$|^$|^,$') AS SHORT_NM,
        CAST(REPLACE(REGEXP_REPLACE(SHOP_ID, '^nan$|^$|^,$'), ',', '') AS NUMBER) AS SHOP_ID,
        REGEXP_REPLACE(TRADE_MKT, '^nan$|^$|^,$') AS TRADE_MKT,
        CASE WHEN OPEN_DATE = to_date('0001-01-01', 'YYYY-MM-DD') THEN NULL ELSE OPEN_DATE END AS OPEN_DATE,
        CASE WHEN CLOSE_DATE = to_date('0001-01-01', 'YYYY-MM-DD') THEN NULL ELSE CLOSE_DATE END AS CLOSE_DATE,
        CAST(REPLACE(REGEXP_REPLACE(SHOP_AGE, '^nan$|^$|^,$'), ',', '') AS NUMBER) AS SHOP_AGE,
        REGEXP_REPLACE(LATITUDE, '^nan$|^$|^,$') AS LATITUDE,
        REGEXP_REPLACE(LONGITUDE, '^nan$|^$|^,$') AS LONGITUDE,
        REGEXP_REPLACE(DIA_HINH, '^nan$|^$|^,$') AS DIA_HINH,
        REGEXP_REPLACE(PROVINCE, '^nan$|^$|^,$') AS PROVINCE,
        REGEXP_REPLACE(DISTRICT, '^nan$|^$|^,$') AS DISTRICT,
        REGEXP_REPLACE(WARD, '^nan$|^$|^,$') AS WARD,
        REGEXP_REPLACE(ADDRESS, '^nan$|^$|^,$') AS ADDRESS,
        REGEXP_REPLACE(DON_VI_HC, '^nan$|^$|^,$') AS DON_VI_HC,
        REGEXP_REPLACE(XEP_HANG_MAT_BANG, '^nan$|^$|^,$') AS XEP_HANG_MAT_BANG,
        CAST(REPLACE(REGEXP_REPLACE(DIEM_MAT_BANG, '^nan$|^$|^,$'), ',', '') AS NUMBER) AS DIEM_MAT_BANG,
        CAST(REPLACE(REGEXP_REPLACE(XEP_HANG_PGD, '^nan$|^$|^,$'), ',', '') AS NUMBER) AS XEP_HANG_PGD,
        REGEXP_REPLACE(PHAN_HANG_PGD, '^nan$|^$|^,$') AS PHAN_HANG_PGD,
        REGEXP_REPLACE(OPENING_TIME, '^nan$|^$|^,$') AS OPENING_TIME,
        REGEXP_REPLACE(CLOSING_TIME, '^nan$|^$|^,$') AS CLOSING_TIME,
        CASE WHEN REGEXP_REPLACE(trim(QLKV_CODE), '^nan$|^$|^,$') IS NOT NULL THEN CONCAT('KV', SUBSTR(RAWTOHEX(HEXTORAW(standard_hash(trim(QLKV_CODE), 'MD5'))), 0, 8)) END AS QLKV_PASSWORD, 
       	CASE WHEN REGEXP_REPLACE(trim(QLV_CODE), '^nan$|^$|^,$') IS NOT NULL THEN CONCAT('VG', SUBSTR(RAWTOHEX(HEXTORAW(standard_hash(trim(QLV_CODE), 'MD5'))), 0, 8)) END AS QLV_PASSWORD,
        'Sharepoint' AS SOURCE,
        W_REGIONAL_BDM_D.BD_CODE,
        W_REGIONAL_BDM_D.BD_NM,
        W_REGIONAL_BDM_D.TEAM_LEAD,
        W_REGIONAL_BDM_D.BD_PASSWORD,
        W_REGIONAL_BDM_D.LBD_PASSWORD
FROM
        STGPROD.AREA_MANAGER_MONTHLY_D AREA_MANAGER_MONTHLY_D
LEFT JOIN F88DWH.W_REGIONAL_BDM_D W_REGIONAL_BDM_D
ON AREA_MANAGER_MONTHLY_D.PHAN_VUNG = W_REGIONAL_BDM_D.REGION_NM 
AND CAST(:YEAR_NUM AS NUMBER) = W_REGIONAL_BDM_D.YEAR_NUM 
AND CAST(:MONTH_NUM AS NUMBER) = W_REGIONAL_BDM_D.MONTH_NUM""", "dwh_table_schema": {"table_name": "F88DWH.W_AREA_MANAGER_MONTHLY_D", "merge_key": ["YEAR_NUM","MONTH_NUM","SHOP_ID"], "columns_datatype": {"YEAR_NUM": "NUMBER", "MONTH_NUM": "NUMBER", "UPDATE_DATE": "DATE", "VALID_FM_DT": "DATE", "VALID_TO_DT": "DATE", "CRN_ROW_IND": "NUMBER", "GDM_NM": "VARCHAR2(30)", "GDM_CODE": "VARCHAR2(30)", "MIEN": "VARCHAR2(30)", "PHAN_VUNG": "VARCHAR2(30)", "PHAN_VUNG_CODE": "VARCHAR2(30)", "QLV_NM": "VARCHAR2(30)", "QLV_CODE": "VARCHAR2(30)", "KHU_VUC": "VARCHAR2(30)", "QLKV_NM": "VARCHAR2(30)", "QLKV_CODE": "VARCHAR2(30)", "SHOP_NM": "VARCHAR2(200)", "SHORT_NM": "VARCHAR2(30)", "SHOP_ID": "NUMBER", "TRADE_MKT": "VARCHAR2(30)", "OPEN_DATE": "DATE", "CLOSE_DATE": "DATE", "SHOP_AGE": "NUMBER", "LATITUDE": "VARCHAR2(30)", "LONGITUDE": "VARCHAR2(30)", "DIA_HINH": "VARCHAR2(30)", "PROVINCE": "VARCHAR2(30)", "DISTRICT": "VARCHAR2(30)", "WARD": "VARCHAR2(30)", "ADDRESS": "VARCHAR2(100)", "DON_VI_HC": "VARCHAR2(30)", "XEP_HANG_MAT_BANG": "VARCHAR2(30)", "DIEM_MAT_BANG": "NUMBER", "XEP_HANG_PGD": "NUMBER", "PHAN_HANG_PGD": "VARCHAR2(30)", "OPENING_TIME": "VARCHAR2(30)", "CLOSING_TIME": "VARCHAR2(30)", "QLKV_PASSWORD": "VARCHAR2(50)", "QLV_PASSWORD": "VARCHAR2(50)", "SOURCE": "VARCHAR2(50)", "BD_CODE": "VARCHAR2(50)", "BD_NM": "VARCHAR2(200)", "TEAM_LEAD": "VARCHAR2(200)", "BD_PASSWORD": "VARCHAR2(50)", "LBD_PASSWORD": "VARCHAR2(50)"}}}]

proc_sql = 'F88DWH.PROC_MERGE_AM_MONTHLY_TO_AM'

dag_monitor_data = DAGMonitor(main_dag_id='MAIN_SHAREPOINT2DWH_PTKD', dag_id='PTKD_MONTHLY_CLKD_DIM_STORE_SHAREPOINT_TO_DWH', source_conn_id='sharepoint_f88_data', destination_conn_id='oracle_f88_dwh', query=None, save_folder='Downloaded', file_name=None, report_name='DIM_STORE', overwrite=1, freq='monthly', department='Project - PTKD', type='SharePoint to Data Warehouse', description='Đẩy dữ liệu org-chart theo tháng từ sharepoint lên DWH', table_metadata=table_metadata, schedule_interval='0 3 * * *', start_date=datetime.datetime(2023,9,1), tags=["ptkd", "daily", "sharepoint2dwh"])

local_saved_folder = os.path.join(parent_dir, dag_monitor_data.save_folder)
with DAG(dag_id=f'{dag_monitor_data.dag_id}',
    schedule_interval=dag_monitor_data.schedule_interval,
    start_date=dag_monitor_data.start_date,
    description=dag_monitor_data.description,
    dagrun_timeout=datetime.timedelta(minutes=45),
    catchup=False,
    max_active_runs=1,
    default_args={
        'owner': 'F88-DE',
        'retries': 3,
        'retry_delay': datetime.timedelta(minutes=1),
    },
    tags=dag_monitor_data.tags) as dag:
    download_files = DownloadSharePointOperator(task_id='download_files',
                                sharepoint_conn_id=dag_monitor_data.source_conn_id,
                                department=dag_monitor_data.department,
                                report_name=dag_monitor_data.report_name,
                                local_saved_folder=local_saved_folder,
                                freq=dag_monitor_data.freq,
                                _type=dag_monitor_data.type
                                )

    for table_metadata in dag_monitor_data.table_metadata.list:
        insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=dag_monitor_data.destination_conn_id)
        
        create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
                                                    table_schema=table_metadata.stg_table_schema,
                                                    oracle_conn_id=dag_monitor_data.destination_conn_id,
                                                    insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

        create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}',
                                                table_schema=table_metadata.dwh_table_schema,
                                                oracle_conn_id=dag_monitor_data.destination_conn_id,
                                                insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)

        transform_table = STG2DWHOperators(
            task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',
            oracle_conn_id=dag_monitor_data.destination_conn_id, source_table_schema=table_metadata.stg_table_schema,
            target_table_schema=table_metadata.dwh_table_schema,
            select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)
        
        run_procedure = PythonOperator(task_id='run_proc_task', python_callable=run_proc, provide_context=True, op_kwargs={'oracle_conn_id': dag_monitor_data.destination_conn_id, 'sql': proc_sql})

        create_and_truncate_stg_table >> download_files >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table >> run_procedure