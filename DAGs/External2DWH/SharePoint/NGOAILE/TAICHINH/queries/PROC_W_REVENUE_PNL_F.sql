CREATE OR <PERSON><PERSON><PERSON>CE PROCEDURE F88DWH.PROC_W_REVENUE_PNL_F AS
/*
  -- Author  : QUANNV2
  -- Created : 16/07/2024
  -- Purpose : <PERSON>uc vu keo du lieu TAI CHINH CHO PHONG PHAN TICH - ANHDT10 ORDER
*/
BEGIN
	-- DELETE

	DELETE FROM	F88DWH.W_PL_BY_STORE_NEW_F
		WHERE YEAR_MONTH = (
			SELECT
				DISTINCT YEAR_MONTH
			FROM
				STGPROD.REVENUE_SHOP);

	COMMIT;
--- INSERT --

	INSERT INTO F88DWH.W_PL_BY_STORE_NEW_F (
		SHOP_CODE,
		LEVEL0,
		LEVEL1,
		LEVEL2,
		AMOUNT,
		YEAR_MONTH)
	SELECT
		SHOP_CODE,
		LEVEL0,
		LEVEL1,
		LEVEL2,
		AMOUNT,
		YEAR_MONTH
	FROM
		STGPROD.REVENUE_SHOP;

	COMMIT;
END PROC_W_REVENUE_PNL_F;