import glob
import os
import datetime
import shutil
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from DAGs.utils import DAGMonitor
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from SendMail.SendMail import dagmonitor_on_fail_callback
import pandas as pd
import logging
from airflow.providers.oracle.hooks.oracle import OracleHook
import pendulum
from DAGs.utils import truncate_stg_tbl, merge_data_stag_to_dwh

parent_dir = os.path.dirname(os.path.abspath(__file__))
DAG_ID = 'TAICHINH_HDDT'
description = 'Kéo dữ liệu VAT thủ công - Kế toán upload lên sharepoint'
start_date = pendulum.datetime(year=2024, month=1, day=1, tz='Asia/Ho_Chi_Minh')
schedule_interval = '1 0 11 * *'
tags = ['taichinh', 'sharepoint2dwh']
freq = "monthly"
sharepoint_conn_id = "sharepoint_f88_data"
department = 'Project - Tài chính'
report_name = 'Hóa đơn VAT vietel'
local_saved_folder = os.path.join(parent_dir, 'Downloaded')
type = 'SharePoint to Data Warehouse'
oracle_conn_id = 'oracle_f88_dwh'
stg_tbl = 'STGPROD.VIETTEL_MANUAL_VAT'
proc_name = 'F88DWH.PROC_W_VAT_MANUAL_F'


def set_params(**context):
    return DAGMonitor.set_params(context['dag_run'].logical_date)


def delete_downloaded() -> None:
    folder_paths = glob.glob(os.path.join(parent_dir, '*', 'Downloaded'))

    for folder_path in folder_paths:
        if folder_path.__contains__('QLV') or folder_path.__contains__('CIMB'):
            continue
        try:
            shutil.rmtree(folder_path)
            print(f"Deleted folder: {folder_path}")
        except OSError as e:
            print(f"Error deleting folder: {folder_path} - {e}")
    shutil.rmtree(os.path.join(parent_dir, 'Downloaded'), ignore_errors=True)


def excel_to_dwh(table_name, conn_id, **context):
    xcom_key = context['dag_run'].dag_id + '_' + 'downloaded_file_list'
    local_file_path = context['ti'].xcom_pull(key=xcom_key)
    for file_path in local_file_path:
        insert_data_to_stg(table_name=table_name, file_path=file_path, conn_id=conn_id)


def insert_data_to_stg(table_name: str, file_path: str, conn_id: str) -> None:
    """datatype_mapping: 'str', 'int', 'float', 'datetime64'
    """
    logging.info(f'Read And Ingest Sheet {file_path}')
    # if file_path.endswith('.xlsx'):
    # skip 7 first rows of file
    df = pd.read_excel(file_path, skiprows=lambda x: x in range(7), dtype=str)  # or x == 8)

    if not df.empty:
        df = df.iloc[:-1]  # Remove the last row
        df = df.dropna(subset=['STT'])
        selected_columns = ['Ký hiệu mẫu hóa đơn', 'Ký hiệu hóa đơn', 'Số hóa đơn',
                            'Ngày, tháng, năm lập hóa đơn',
                            'Mã khách hàng', 'Tên khách hàng', 'Tên đơn vị', 'Mã số thuế', 'Địa chỉ', 'Mã hàng hóa',
                            'Tên hàng hóa', 'ĐVT', 'Số lượng', 'Đơn giá', 'Doanh số bán hàng chưa thuế VNĐ',
                            'Thuế suất (%)', 'Thuế GTGT đầu ra VNĐ', 'Tổng cộng VNĐ', 'MST đơn vị bán hàng',
                            'Trạng thái hóa đơn', 'Trạng thái gửi CQT', 'Mã CQT cấp', 'Lý do CQT từ chối']

        logging.info(f'Total record in df: {str(len(df))}')
        selected_df = df[selected_columns]
        selected_df = selected_df.fillna(value='')
        selected_df = selected_df.rename(columns={
            'Ký hiệu mẫu hóa đơn': 'ky_hieu_mau_hoa_don',
            'Ký hiệu hóa đơn': 'ky_hieu_hoa_don',
            'Số hóa đơn': 'so_hoa_don',
            'Ngày, tháng, năm lập hóa đơn': 'ngay_thang_nam_lap_hoa_don',
            'Mã khách hàng': 'ma_khach_hang',
            'Tên khách hàng': 'ten_khach_hang',
            'Tên đơn vị': 'ten_don_vi',
            'Mã số thuế': 'ma_so_thue',
            'Địa chỉ': 'dia_chi',
            'Mã hàng hóa': 'ma_hang_hoa',
            'Tên hàng hóa': 'ten_hang_hoa',
            'ĐVT': 'dvt',
            'Số lượng': 'so_luong',
            'Đơn giá': 'don_gia',
            'Doanh số bán hàng chưa thuế VNĐ': 'doanh_so_ban_hang_chua_thue',
            'Thuế suất (%)': 'thue_suat',
            'Thuế GTGT đầu ra VNĐ': 'thue_gtgt_dau_ra',
            'Tổng cộng VNĐ': 'tong_cong',
            'MST đơn vị bán hàng': 'mst_don_vi_ban_hang',
            'Trạng thái hóa đơn': 'trang_thai_hoa_don',
            'Trạng thái gửi CQT': 'trang_thai_gui_cqt',
            'Mã CQT cấp': 'ma_cqt_cap',
            'Lý do CQT từ chối': 'ly_do_cqt_tu_choi'
        })

        # selected_df = selected_df.astype(str)
        # print(selected_df.dtypes)
        logging.info(f'Total record in df after clean: {str(len(selected_df))}')
        oracle_hook = OracleHook(oracle_conn_id=conn_id)
        oracle_hook.bulk_insert_rows(table=table_name, rows=selected_df.itertuples(index=False),
                                     target_fields=selected_df.columns.tolist(),
                                     commit_every=1000)
        logging.info(f'Ingested Sheet {file_path}')


with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'none_skipped'
         },
         start_date=start_date,
         catchup=True,
         schedule_interval=schedule_interval,
         max_active_runs=1,
         tags=tags
         ) as main_dag:
    start = EmptyOperator(task_id="Start")
    end = EmptyOperator(task_id="End")

    # Define the PythonOperator to execute set_params
    set_params_task = PythonOperator(
        task_id='set_params_task',
        python_callable=set_params,
        provide_context=True
    )
    delete_downloaded = PythonOperator(
        task_id='delete_downloaded',
        python_callable=delete_downloaded
    )

    truncate_stg_tbl = PythonOperator(task_id='truncate_stg_tbl',
                                      python_callable=truncate_stg_tbl,
                                      op_kwargs={'tbl_name': stg_tbl, 'conn_id': oracle_conn_id},
                                      provide_context=True)
    download_files = DownloadSharePointOperator(task_id='download_files',
                                                sharepoint_conn_id=sharepoint_conn_id,
                                                department=department,
                                                report_name=report_name,
                                                local_saved_folder=local_saved_folder,
                                                freq=freq,
                                                _type=type
                                                )
    ingestion = PythonOperator(task_id='ingestion',
                               python_callable=excel_to_dwh,
                               op_kwargs={'table_name': stg_tbl, 'conn_id': oracle_conn_id},
                               provide_context=True)

    delete_insert_into_dwh_tbl = PythonOperator(task_id='delete_insert_into_dwh_tbl',
                                                python_callable=merge_data_stag_to_dwh,
                                                op_kwargs={'proc_name': proc_name, 'conn_id': oracle_conn_id},
                                                provide_context=True)

    start >> set_params_task >> truncate_stg_tbl >> download_files >> ingestion >> delete_insert_into_dwh_tbl >> delete_downloaded >> end
