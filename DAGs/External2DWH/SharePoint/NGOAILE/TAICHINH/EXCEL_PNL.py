import glob
import os
import datetime
import shutil
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from DAGs.utils import DAGMonitor
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from SendMail.SendMail import dagmonitor_on_fail_callback
import pandas as pd
import logging
from airflow.providers.oracle.hooks.oracle import OracleHook
import pendulum
from DAGs.utils import truncate_stg_tbl, merge_data_stag_to_dwh

parent_dir = os.path.dirname(os.path.abspath(__file__))
DAG_ID = 'TAICHINH_PNL'
description = 'Kéo dữ liệu PNL - tài chính (phân tích) upload lên sharepoint'
start_date = pendulum.datetime(year=2024, month=7, day=1, tz='Asia/Ho_Chi_Minh')
schedule_interval = '1 0 15 * *'
tags = ['taichinh', 'sharepoint2dwh','pnl']
freq = "monthly"
sharepoint_conn_id = "sharepoint_f88_data"
department = 'Project - Tài chính'
report_name = 'PNL'
local_saved_folder = os.path.join(parent_dir, 'Downloaded')
type = 'SharePoint to Data Warehouse'
oracle_conn_id = 'oracle_f88_dwh'
stg_tbl = 'STGPROD.REVENUE_SHOP'
proc_name = 'F88DWH.PROC_W_REVENUE_PNL_F'


def set_params(**context):
    params = DAGMonitor.set_params(context['dag_run'].logical_date)
    context['ti'].xcom_push(key=DAG_ID, value=params)


def delete_downloaded() -> None:
    folder_paths = glob.glob(os.path.join(parent_dir, '*', 'Downloaded'))

    for folder_path in folder_paths:
        if folder_path.__contains__('QLV') or folder_path.__contains__('CIMB'):
            continue
        try:
            shutil.rmtree(folder_path)
            print(f"Deleted folder: {folder_path}")
        except OSError as e:
            print(f"Error deleting folder: {folder_path} - {e}")
    shutil.rmtree(os.path.join(parent_dir, 'Downloaded'), ignore_errors=True)


def excel_to_dwh(table_name, conn_id, **context):
    xcom_key = context['dag_run'].dag_id + '_' + 'downloaded_file_list'
    local_file_path = context['ti'].xcom_pull(key=xcom_key)
    year_month = context['ti'].xcom_pull(key=DAG_ID)
    print(year_month)
    print(year_month['LAST_YEAR_MONTH'])
    year_month2= year_month['LAST_YEAR_MONTH']
    for file_path in local_file_path:
        insert_data_to_stg(table_name=table_name, file_path=file_path, conn_id=conn_id,year_month=year_month2)


def insert_data_to_stg(table_name: str, file_path: str, conn_id: str, year_month) -> None:
    """datatype_mapping: 'str', 'int', 'float', 'datetime64'
    """
    logging.info(f'Read And Ingest Sheet {file_path}')
    raw_data_pnl = pd.read_excel(file_path, dtype=str, skiprows=0, sheet_name='Sheet1')
    raw_data_pnl['SHOP_CODE'] = raw_data_pnl['PGD_ACTIVE_NAME'].str.split('.').str.get(0)
    raw_data_pnl = raw_data_pnl[['SHOP_CODE', '0-HOATDONG', '01-NHOMCHITIEU', '02-LOAICHITIEU', 'PnL', 'YM']]
    columns = ['SHOP_CODE', 'LEVEL0', 'LEVEL1', 'LEVEL2', 'AMOUNT', 'YEAR_MONTH']
    raw_data_pnl.columns = columns
    raw_data_pnl = raw_data_pnl.dropna(subset=['SHOP_CODE'])
    raw_data_pnl = raw_data_pnl[raw_data_pnl.YEAR_MONTH == year_month]

    logging.info(f'Total record in df after clean: {str(len(raw_data_pnl))}')
    oracle_hook = OracleHook(oracle_conn_id=conn_id)
    oracle_hook.bulk_insert_rows(table=table_name, rows=raw_data_pnl.itertuples(index=False),
                                 target_fields=raw_data_pnl.columns.tolist(),
                                 commit_every=1000)
    logging.info(f'Number of record: {len(raw_data_pnl)}')
    logging.info(f'Ingested Sheet {file_path}')


with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'none_skipped'
         },
         start_date=start_date,
         catchup=True,
         schedule_interval=schedule_interval,
         max_active_runs=1,
         tags=tags
         ) as main_dag:
    start = EmptyOperator(task_id="Start")
    end = EmptyOperator(task_id="End")

    # Define the PythonOperator to execute set_params
    set_params_task = PythonOperator(
        task_id='set_params_task',
        python_callable=set_params,
        provide_context=True
    )
    delete_downloaded = PythonOperator(
        task_id='delete_downloaded',
        python_callable=delete_downloaded
    )
    truncate_stg_tbl = PythonOperator(task_id=f'truncate_stg_tbl_{stg_tbl}',
                                      python_callable=truncate_stg_tbl,
                                      op_kwargs={'tbl_name': stg_tbl, 'conn_id': oracle_conn_id},
                                      provide_context=True)
    download_files = DownloadSharePointOperator(task_id='download_files',
                                                sharepoint_conn_id=sharepoint_conn_id,
                                                department=department,
                                                report_name=report_name,
                                                local_saved_folder=local_saved_folder,
                                                freq=freq,
                                                _type=type
                                                )
    ingestion = PythonOperator(task_id='ingestion',
                               python_callable=excel_to_dwh,
                               op_kwargs={'table_name': stg_tbl, 'conn_id': oracle_conn_id},
                               provide_context=True)

    delete_insert_into_dwh_tbl = PythonOperator(task_id='delete_insert_into_dwh_tbl',
                                                python_callable=merge_data_stag_to_dwh,
                                                op_kwargs={'proc_name': proc_name, 'conn_id': oracle_conn_id},
                                                provide_context=True)

    start >> set_params_task >> truncate_stg_tbl >> download_files >> ingestion  >> delete_downloaded >> delete_insert_into_dwh_tbl >> end
