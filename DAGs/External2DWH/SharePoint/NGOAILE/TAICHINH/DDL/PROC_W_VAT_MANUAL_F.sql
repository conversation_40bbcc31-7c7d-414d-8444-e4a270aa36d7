CREATE OR REPLACE PROCEDURE F88DWH.PROC_W_VAT_MANUAL_F AS
/*
  -- Author  : QUANNV2
  -- Created : 24/05/2024
  -- Purpose : Phuc vu keo du lieu thu cong VAT tu ke toan
*/
BEGIN
	-- DELETE

	EXECUTE IMMEDIATE 'TRUNCATE TABLE STGPROD.VAT_MANUAL_DEL';

	INSERT INTO STGPROD.VAT_MANUAL_DEL (MA_HOA_DON,TEN_HANG_HOA,SOURCE)
	SELECT 	CONCAT(VAT_STG.KY_HIEU_HOA_DON,VAT_STG.SO_HOA_DON) MA_HOA_DON , VAT_STG.TEN_HANG_HOA , VAT_DWH.SOURCE
	FROM
		F88DWH.W_VIETTEL_TAX_VAT VAT_DWH
	JOIN  STGPROD.VIETTEL_MANUAL_VAT VAT_STG ON
		VAT_DWH.MA_HOA_DON  = CONCAT(VAT_STG.KY_HIEU_HOA_DON,VAT_STG.SO_HOA_DON)
		AND VAT_DWH.TEN_HANG_HOA  = VAT_STG.TEN_HANG_HOA
	WHERE
		VAT_DWH.SOURCE = 'MANUAL' ;

	COMMIT;

	DELETE FROM	F88DWH.W_VIETTEL_TAX_VAT VAT_DWH
	WHERE EXISTS (
		SELECT
			1
		FROM
			STGPROD.VAT_MANUAL_DEL VAT_DEL
		WHERE
			VAT_DWH.MA_HOA_DON  = VAT_DEL.MA_HOA_DON
			AND VAT_DWH.TEN_HANG_HOA  = VAT_DEL.TEN_HANG_HOA
			AND VAT_DWH.SOURCE = VAT_DEL.SOURCE)
		    ;

	COMMIT;

--- INSERT --

	INSERT INTO F88DWH.W_VIETTEL_TAX_VAT (
			DATE_WID,
			YEAR_NUM,
			MONTH_NUM,
		    KY_HIEU_MAU_HOA_DON ,
		    NGAY_LAP_HOA_DON,
		    MA_HOA_DON ,
		    MA_KHACH_HANG ,
		    TEN_KHACH_HANG ,
		    TEN_DON_VI,
		    MA_SO_THUE,
		    DIA_CHI,
		    MA_HANG_HOA,
		    TEN_HANG_HOA ,
		    DVT ,
		    SO_LUONG ,
		    DON_GIA ,
		    DOANH_SO_BAN_HANG_CHUA_THUE ,
		    THUE_SUAT ,
		    THUE_GTGT_DAU_RA,
		    TONG_CONG ,
		    MST_DON_VI_BAN_HANG ,
		    TRANG_THAI_HOA_DON ,
		    TRANG_THAI_GUI_CQT,
		   	MA_CQT_CAP,
		    LY_DO_CQT_TU_CHOI,
		    SOURCE
		)
		SELECT
			TO_CHAR(TO_DATE(NGAY_THANG_NAM_LAP_HOA_DON,'DD/MM/YYYY HH24:MI'),'YYYYMMDD')  DATE_WID,
	        TO_CHAR(TO_DATE(NGAY_THANG_NAM_LAP_HOA_DON,'DD/MM/YYYY HH24:MI'),'YYYYMMDD')  YEAR_NUM,
	        TO_CHAR(TO_DATE(NGAY_THANG_NAM_LAP_HOA_DON,'DD/MM/YYYY HH24:MI'),'YYYYMMDD')  MONTH_NUM,
		    KY_HIEU_MAU_HOA_DON ,
		    TO_DATE(NGAY_THANG_NAM_LAP_HOA_DON,'DD/MM/YYYY HH24:MI'),
		    CONCAT(KY_HIEU_HOA_DON,SO_HOA_DON)  ,
		    MA_KHACH_HANG ,
		    TEN_KHACH_HANG ,
		    TEN_DON_VI,
		    MA_SO_THUE,
		    DIA_CHI,
		    MA_HANG_HOA,
		    TEN_HANG_HOA ,
		    DVT ,
		    SO_LUONG ,
		    DON_GIA ,
		    DOANH_SO_BAN_HANG_CHUA_THUE ,
		    CASE WHEN INSTR(THUE_SUAT, '%') > 0 THEN CAST(REPLACE(THUE_SUAT, '%', '') AS NUMBER ) ELSE NULL END THUE_SUAT,
		    THUE_GTGT_DAU_RA,
		    TONG_CONG ,
		    MST_DON_VI_BAN_HANG ,
		    TRANG_THAI_HOA_DON ,
		    TRANG_THAI_GUI_CQT,
		   	MA_CQT_CAP,
		    LY_DO_CQT_TU_CHOI,
		    'MANUAL'
		FROM
			STGPROD.VIETTEL_MANUAL_VAT
		;

	COMMIT;
END PROC_W_VAT_MANUAL_F;