
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON><PERSON>cutor
from datetime import datetime, timedel<PERSON>
import logging
import shutil
from Provider.OracleProvider.operators.DDLOperators import DD<PERSON><PERSON>perator
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.hooks.DeleteHook import DeleteFileSharePointHook
from Provider.SharePointProvider.hooks.DownloadHook import DownloadSharePointHook
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaData
from Provider.utils import set_saved_folder_and_file_name
import pickle
from typing import Any, List, Tuple
import os
from airflow.hooks.base import BaseHook
from airflow.models.baseoperator import BaseOperator
from airflow.utils.context import Context
from airflow import DAG
from airflow.operators.python import <PERSON><PERSON><PERSON>ator
from DAGs.utils import DAGMonitor
import subprocess
import re
from office365.sharepoint.files.file import File
from office365.runtime.client_request_exception import ClientRequestException
from tenacity import retry, wait_fixed, stop_after_attempt
import pendulum
import shutil

def pass_client_request_exception(error: ClientRequestException):
    if '404 Client Error: Not Found for url' not in str(error):
        raise error
    else:
        logging.info(str(error))

class DownloadFilesInChildFolderHook(DownloadSharePointHook):
    def __init__(self, sharepoint_conn_id: str, local_saved_folder: str) -> None:
        super().__init__(sharepoint_conn_id)
        self.local_saved_folder = local_saved_folder
    
    def run(self, relative_url_folder_path: str, local_saved_folder: str, process_date: str) -> Tuple[str]:
        @retry(wait=wait_fixed(10), stop=stop_after_attempt(5))
        def _get_child_files_relative_url_by_parent_folder_and_download(employee_code_and_folder_path):
            """Lấy tất cả URL của file excel và pdf về, đồng thời tải file excel nếu tìm thấy.
            Thứ tự trả về: (Mã nv, [list file excel url], {shop_code: [list file pdf url]}, [list file exel downloaded])

            Args:
                employee_code_and_folder_path (_type_): _description_

            Returns:
                _type_: _description_
            """    
            session = self.get_conn()
            employee_code, folder_path = employee_code_and_folder_path
            self.log.info(f'Process {str(employee_code)}')
            files = session.web.get_folder_by_server_relative_path(folder_path).files
            session.load(files).execute_query()
            share_point_files = [f.properties["ServerRelativeUrl"] for f in files]
            
            @retry(wait=wait_fixed(10), stop=stop_after_attempt(5))
            def _download_share_point_file(file: File | str) -> str:
                """Tải file excel bởi File object

                Args:
                    file (File): _description_
                    local_saved_file (str): _description_

                Returns:
                    str: _description_
                """
                file = file.properties["ServerRelativeUrl"]
                file_name = os.path.basename(file)
                if file_name.endswith('.pdf') or re.findall('.xls\w{0,1}$', file_name):
                ### Only get excel files
                #if re.findall('.xls\w{0,1}$', file_name):
                    download_file_path = os.path.join(local_saved_folder, '{0}_{1}_{2}'.format(employee_code, process_date, file_name))
                    if os.path.exists(download_file_path):
                        if os.path.getsize(download_file_path) > 0:
                            self.log.info('File has already downloaded. Skip and continue another file.')
                            return download_file_path
                    self.log.info("Start download file: {0}".format(os.path.basename(download_file_path))) 
                    ### Download file with new session
                    session = self.get_conn()
                    with open(download_file_path, "wb") as local_file:
                        session.web.get_file_by_server_relative_path(file).download(local_file).execute_query()
                    self.log.info("[Ok] file has been downloaded into: {0}".format(download_file_path))
                    return download_file_path
                else:
                    self.log.info('Pass %s because it is not as expected format.', file)
    
            local_excel_files = []
            
            with ThreadPoolExecutor(max_workers=2) as pool:
                list_downloaded_file = tuple(pool.map(_download_share_point_file, files))

            list_downloaded_file = tuple([i for i in list_downloaded_file if i])
            
            for file_path in list_downloaded_file:
                if re.findall('.xls\w{0,1}$', os.path.basename(file_path)):
                    ### Get excel then
                    local_excel_files.append(file_path)
            return share_point_files, local_excel_files
        
        session = self.get_conn()
            
        relative_url_folder_path = relative_url_folder_path.replace("\\", "/").removeprefix("/").removesuffix("/")
        if 'sites' in relative_url_folder_path:
            relative_url_folder_path = f'/{relative_url_folder_path}'
        else:
            relative_url_folder_path = f'{self.download_path}/{relative_url_folder_path}'
        folders = session.web.get_folder_by_server_relative_path(relative_url_folder_path).folders
        session.load(folders).execute_query()
        
        employee_code_folder_map = {i.properties["Name"]: i.properties["ServerRelativeUrl"] for i in folders}
        with ThreadPoolExecutor(max_workers=5) as pool:
            employee_excel_and_pdf_files_relative_url = tuple(pool.map(_get_child_files_relative_url_by_parent_folder_and_download, employee_code_folder_map.items()))
    
        excel_localfile_path_pickle_file = os.path.join(local_saved_folder,f'{process_date}_excel_localfile_path.pickle')
        with open(excel_localfile_path_pickle_file, 'wb') as file:
            pickle.dump(tuple(j for i in employee_excel_and_pdf_files_relative_url for j in i[-1]), file, protocol=pickle.HIGHEST_PROTOCOL)
        
        sharepoint_file_relative_url = tuple(j for i in employee_excel_and_pdf_files_relative_url for j in i[0])
                
        sharepoint_file_relative_url_pickle_file = os.path.join(local_saved_folder,f'{process_date}_sharepoint_files_relative_url.pickle')
        with open(sharepoint_file_relative_url_pickle_file, 'wb') as file:
            pickle.dump(tuple(sharepoint_file_relative_url), file, protocol=pickle.HIGHEST_PROTOCOL)

        return excel_localfile_path_pickle_file, sharepoint_file_relative_url_pickle_file
    
class DownloadFilesInChildFolderOperator(BaseOperator):
    def __init__(self, relative_url_folder_path: str, sharepoint_conn_id: str, local_saved_folder: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.local_saved_folder = local_saved_folder
        self.relative_url_folder_path = relative_url_folder_path
    
    def execute(self, context: Context) -> Any:
        self.parameters = DAGMonitor.set_params_operator(**context)
        process_date = self.parameters['TODAY']
        
        download_hook = DownloadFilesInChildFolderHook(sharepoint_conn_id=self.sharepoint_conn_id, local_saved_folder=self.local_saved_folder)
        
        excel_localfile_path_pickle_file, sharepoint_file_relative_url_pickle_file = download_hook.run(relative_url_folder_path=self.relative_url_folder_path, local_saved_folder=self.local_saved_folder, process_date=process_date)
        
        return excel_localfile_path_pickle_file, sharepoint_file_relative_url_pickle_file
    
class Excel2CSVHook(BaseHook):
    def __init__(self, file_path: str | List[str] | Tuple[str], sheet_name: str | List[str] | Tuple[str], *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.file_path = [file_path] if isinstance(file_path, str) else file_path
        self.sheet_name = [sheet_name] if isinstance(sheet_name, str) else sheet_name
        
    def _convert_excel_to_csv(self, file_path):
        for sheet in self.sheet_name:
            self.log.info(f'Starting to convert file: {os.path.dirname(file_path)} sheet name: {sheet} to csv')
            new_csv_file_path = os.path.join(os.path.dirname(file_path), re.sub('.xls\w{0,1}$', '', os.path.basename(file_path)) + '_' + sheet + '.csv')
            ### Use in2csv written in Python
            # convert_cmd = f"in2csv --format xls -e 'utf-8' --encoding-xls 'utf-8' --sheet '{sheet}' '{file_path}' > '{new_csv_file_path}'"
            
            # ### Use xlsx-to-csv written in Go
            convert_cmd = f"xlsx-to-csv -s '{sheet}' -o '{new_csv_file_path}' '{file_path}'"
            self.log.info('Command: {0}'.format(convert_cmd))
            res = subprocess.run(convert_cmd, shell=True, capture_output=True, text=True, env=dict(PATH='/opt/python/bin:/usr/bin'))
            self.log.info('Error message: {0}'.format(res.stderr))
        
    def run(self):
        with ThreadPoolExecutor(max_workers=20) as pool:
            res = tuple(pool.map(self._convert_excel_to_csv, self.file_path))

class DeleteFileSharePointOperator(BaseOperator):
    def __init__(self, sharepoint_conn_id: str, parent_xcom_task_id: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.parent_xcom_task_id = parent_xcom_task_id

    def execute(self, context: Context) -> Any:
        
        sharepoint_file_relative_url_pickle_file = context['ti'].xcom_pull(task_ids=self.parent_xcom_task_id)[1]
        
        with open(sharepoint_file_relative_url_pickle_file, 'rb') as f:
            sharepoint_file_relative_url_pickle_file = pickle.load(f)
    
        DeleteFileSharePointHook(sharepoint_conn_id=self.sharepoint_conn_id).run(sharepoint_file_relative_url_pickle_file)

class Excel2CSVOperator(BaseOperator):
    def __init__(self, sheet_name: str | List[str] | Tuple[str], parent_xcom_task_id: str, local_saved_folder: str, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.sheet_name = sheet_name
        self.parent_xcom_task_id = parent_xcom_task_id
        self.local_saved_folder = local_saved_folder

    def execute(self, context: Context) -> Any:
        self.log.info('Remove all csv files before convert...')
        cmd = f"rm '{os.path.join(self.local_saved_folder, '*.csv')}'"
        self.log.info('Command: {0}'.format(cmd))
        subprocess.run(cmd, shell=True)
        
        excel_localfile_path_pickle_file = context['ti'].xcom_pull(task_ids=self.parent_xcom_task_id)[0]
        
        with open(excel_localfile_path_pickle_file, 'rb') as f:
            excel_localfile_path_pickle_file = pickle.load(f)
        
        excel_to_csv = Excel2CSVHook(file_path=excel_localfile_path_pickle_file, sheet_name=self.sheet_name)
        
        excel_to_csv.run()

def delete_downloaded(folder_path: str) -> None:
    shutil.rmtree(folder_path, ignore_errors=True)
    logging.info(f"Deleted folder: {folder_path}")

with DAG(dag_id='PTKD_DAILY_NV_NHAPLIEU_PCTT', description='Xử lý dữ liệu báo cáo PCTT và xử lý đẩy file pdf của các PGD',
         default_args={
             'owner': 'F88-DE',
             'retries': 3,
            'retry_delay': timedelta(minutes=1),
             'trigger_rule': 'all_success'
         },
         start_date=datetime(2023, 12, 15),
         catchup=False,
         schedule_interval="@daily",
         max_active_runs=1,
         tags=['PCTT','daily','sharepoint2dwh','ngoaile']
         ) as dag:
    process_date = pendulum.now(tz='Asia/Ho_Chi_Minh').strftime('%Y%m%d')
    local_saved_folder = set_saved_folder_and_file_name(False, None, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Downloaded'), None, 'Project - PTKD/PCTT_Hồ sơ pháp lý PGD', process_date[:4], process_date[4:6], process_date[6:])[0]
    
    if not os.path.exists(local_saved_folder):
        os.makedirs(local_saved_folder)
    ### Prod
    relative_url_folder_path = '/sites/F88-DATA/Shared Documents/SharePoint to Data Warehouse/Project - PCTT/Hồ sơ pháp lý PGD/2. Nhân viên pháp chế nhập liệu/'
    # ### Test
    # relative_url_folder_path = '/sites/F88-DATA/Shared Documents/Test/SharePoint to Data Warehouse/Project - PTKD/PCTT_Hồ sơ pháp lý PGD/'
    
    sheet_name = ('Database_Capmoi', 'Database_Caplai')#, 'Database_Caplai', 'Database_CN')
    oracle_conn_id = 'oracle_f88_dwh'
    sharepoint_conn_id = 'sharepoint_f88_data'
    parent_xcom_task_id = 'download_excel_files'
    table_metadata_list = ListTableMetaData(list=[{"xlsx_sheet_name":"Database_Capmoi","rename_columns":{"Miền":"REGION","Tỉnh":"PROVINCE","Huyện":"DISTRICT","Địa chỉ chi tiết theo Hợp đồng thuê mặt bằng":"ADDRESS_BY_LEASE_CONTRACT","Địa chỉ chi tiết theo sổ đỏ/sổ hồng":"ADDRESS_BY_LAND_USE_RIGHTS","Ngày\nđề xuất":"PROPOSED_DATE","Ngày\nđặt cọc":"DEPOSIT_DATE", "Ngày nhận mặt bằng":"RECEIVE_PERMISE_DATE", "Cấp":"CAP", "Trạng thái":"STATUS", "Dự kiến":"EXPECTED_DATE", "Hành động":"HANH_DONG", "ĐKKD": "DKKD", "Ghi chú": "NOTE", "Mã nhân viên nhập liệu": "EMPLOYEE_CODE", "Tên nhân viên nhập liệu":"EMPLOYEE_NAME"},"list_columns_to_ingest_to_stg":['REGION', 'PROVINCE', 'DISTRICT', 'ADDRESS_BY_LEASE_CONTRACT', 'ADDRESS_BY_LAND_USE_RIGHTS', 'PROPOSED_DATE', 'DEPOSIT_DATE', 'RECEIVE_PERMISE_DATE', 'CAP', 'STATUS', 'EXPECTED_DATE', 'HANH_DONG', 'LLTP', 'DKKD', 'PCCC', 'ANTT', 'NOTE', 'EMPLOYEE_CODE', 'EMPLOYEE_NAME'],"stg_table_schema":{"table_name":"STGPROD.PCTT_NHAPLIEU_CAPMOI","columns_datatype":{'REGION': 'VARCHAR2(1000)', 'PROVINCE': 'VARCHAR2(1000)', 'DISTRICT': 'VARCHAR2(1000)', 'ADDRESS_BY_LEASE_CONTRACT': 'VARCHAR2(1000)', 'ADDRESS_BY_LAND_USE_RIGHTS': 'VARCHAR2(1000)', 'PROPOSED_DATE': 'VARCHAR2(1000)', 'DEPOSIT_DATE': 'VARCHAR2(1000)', 'RECEIVE_PERMISE_DATE': 'VARCHAR2(1000)', 'CAP': 'VARCHAR2(1000)', 'STATUS': 'VARCHAR2(1000)', 'EXPECTED_DATE': 'VARCHAR2(1000)', 'HANH_DONG': 'VARCHAR2(1000)', 'LLTP': 'VARCHAR2(1000)', 'DKKD': 'VARCHAR2(1000)', 'PCCC': 'VARCHAR2(1000)', 'ANTT': 'VARCHAR2(1000)', 'NOTE': 'VARCHAR2(1000)', 'EMPLOYEE_CODE': 'VARCHAR2(1000)', 'EMPLOYEE_NAME': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT REGION, PROVINCE, DISTRICT, ADDRESS_BY_LEASE_CONTRACT, ADDRESS_BY_LAND_USE_RIGHTS, TO_DATE(REGEXP_REPLACE(PROPOSED_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') PROPOSED_DATE, TO_DATE(REGEXP_REPLACE(DEPOSIT_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') DEPOSIT_DATE, TO_DATE(REGEXP_REPLACE(RECEIVE_PERMISE_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') RECEIVE_PERMISE_DATE, CAP, STATUS, TO_DATE(REGEXP_REPLACE(EXPECTED_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') EXPECTED_DATE, HANH_DONG, TO_DATE(REGEXP_REPLACE(LLTP, '^nan$|^$|^,$'), 'DD-MON-YYYY') LLTP, TO_DATE(REGEXP_REPLACE(DKKD, '^nan$|^$|^,$'), 'DD-MON-YYYY') DKKD, TO_DATE(REGEXP_REPLACE(PCCC, '^nan$|^$|^,$'), 'DD-MON-YYYY') PCCC, TO_DATE(REGEXP_REPLACE(ANTT, '^nan$|^$|^,$'), 'DD-MON-YYYY') ANTT, NOTE, EMPLOYEE_CODE, EMPLOYEE_NAME, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE FROM STGPROD.PCTT_NHAPLIEU_CAPMOI","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_NHAPLIEU_CAPMOI","columns_datatype":{'REGION': 'VARCHAR2(1000)', 'PROVINCE': 'VARCHAR2(1000)', 'DISTRICT': 'VARCHAR2(1000)', 'ADDRESS_BY_LEASE_CONTRACT': 'VARCHAR2(1000)', 'ADDRESS_BY_LAND_USE_RIGHTS': 'VARCHAR2(1000)', 'PROPOSED_DATE': 'DATE', 'DEPOSIT_DATE': 'DATE', 'RECEIVE_PERMISE_DATE': 'DATE', 'CAP': 'VARCHAR2(1000)', 'STATUS': 'VARCHAR2(1000)', 'EXPECTED_DATE': 'DATE', 'HANH_DONG': 'VARCHAR2(1000)', 'LLTP': 'DATE', 'DKKD': 'DATE', 'PCCC': 'DATE', 'ANTT': 'DATE', 'NOTE': 'VARCHAR2(1000)', 'EMPLOYEE_CODE': 'VARCHAR2(1000)', 'EMPLOYEE_NAME': 'VARCHAR2(1000)', 'PROCESS_DATE':'DATE'},"merge_key":['ADDRESS_BY_LEASE_CONTRACT']}},{"xlsx_sheet_name":"Database_Caplai","rename_columns":{"Tên phòng giao dịch":"SHOP_NAME","Mã":"SHOP_CODE","Ngày\nđề xuất":"PROPOSED_DATE","Ngày duyệt":"APPROVAL_DATE", "Cấp":"CAP", "Trạng thái":"STATUS", "Dự kiến":"EXPECTED_DATE", "Hành động":"HANH_DONG", "ĐKKD": "DKKD", "Ghi chú": "NOTE","Miền":"REGION","Tỉnh":"PROVINCE","Huyện":"DISTRICT", "Địa chỉ chi tết theo ĐKKD": "DETAIL_ADDRESS", "Mã nhân viên nhập liệu": "EMPLOYEE_CODE", "Tên nhân viên nhập liệu":"EMPLOYEE_NAME"},"list_columns_to_ingest_to_stg":["SHOP_NAME", "SHOP_CODE", "PROPOSED_DATE", "APPROVAL_DATE", "CAP", "STATUS", "EXPECTED_DATE", "HANH_DONG", 'LLTP', 'DKKD', 'PCCC', 'ANTT', "NOTE", "REGION", "PROVINCE", "DISTRICT", "DETAIL_ADDRESS", "EMPLOYEE_CODE", "EMPLOYEE_NAME"],"stg_table_schema":{"table_name":"STGPROD.PCTT_NHAPLIEU_CAPLAI","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'PROPOSED_DATE': 'VARCHAR2(1000)', 'APPROVAL_DATE': 'VARCHAR2(1000)', 'CAP': 'VARCHAR2(1000)', 'STATUS': 'VARCHAR2(1000)', 'EXPECTED_DATE': 'VARCHAR2(1000)', 'HANH_DONG': 'VARCHAR2(1000)', 'LLTP': 'VARCHAR2(1000)', 'DKKD': 'VARCHAR2(1000)', 'PCCC': 'VARCHAR2(1000)', 'ANTT': 'VARCHAR2(1000)', 'NOTE': 'VARCHAR2(1000)', 'REGION': 'VARCHAR2(1000)', 'PROVINCE': 'VARCHAR2(1000)', 'DISTRICT': 'VARCHAR2(1000)', 'DETAIL_ADDRESS': 'VARCHAR2(1000)', 'EMPLOYEE_CODE': 'VARCHAR2(1000)', 'EMPLOYEE_NAME': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT SHOP_NAME, SHOP_CODE, TO_DATE(REGEXP_REPLACE(PROPOSED_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') PROPOSED_DATE, TO_DATE(REGEXP_REPLACE(APPROVAL_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') APPROVAL_DATE, CAP, STATUS, TO_DATE(REGEXP_REPLACE(EXPECTED_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') EXPECTED_DATE, HANH_DONG, TO_DATE(REGEXP_REPLACE(LLTP, '^nan$|^$|^,$'), 'DD-MON-YYYY') LLTP, TO_DATE(REGEXP_REPLACE(DKKD, '^nan$|^$|^,$'), 'DD-MON-YYYY') DKKD, TO_DATE(REGEXP_REPLACE(PCCC, '^nan$|^$|^,$'), 'DD-MON-YYYY') PCCC, TO_DATE(REGEXP_REPLACE(ANTT, '^nan$|^$|^,$'), 'DD-MON-YYYY') ANTT, NOTE, REGION, PROVINCE, DISTRICT, DETAIL_ADDRESS, EMPLOYEE_CODE, EMPLOYEE_NAME, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE FROM STGPROD.PCTT_NHAPLIEU_CAPLAI","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_NHAPLIEU_CAPLAI","columns_datatype":{'SHOP_NAME': 'VARCHAR2(1000)', 'SHOP_CODE': 'VARCHAR2(1000)', 'PROPOSED_DATE': 'DATE', 'APPROVAL_DATE': 'DATE', 'CAP': 'VARCHAR2(1000)', 'STATUS': 'VARCHAR2(1000)', 'EXPECTED_DATE': 'DATE', 'HANH_DONG': 'VARCHAR2(1000)', 'LLTP': 'DATE', 'DKKD': 'DATE', 'PCCC': 'DATE', 'ANTT': 'DATE', 'NOTE': 'VARCHAR2(1000)', 'REGION': 'VARCHAR2(1000)', 'PROVINCE': 'VARCHAR2(1000)', 'DISTRICT': 'VARCHAR2(1000)', 'DETAIL_ADDRESS': 'VARCHAR2(1000)', 'EMPLOYEE_CODE': 'VARCHAR2(1000)', 'EMPLOYEE_NAME': 'VARCHAR2(1000)', 'PROCESS_DATE':'DATE'},"merge_key":['SHOP_CODE']}}, {"xlsx_sheet_name":"Database_CN","rename_columns":{"Mã số Chi nhánh":"BRANCH_CODE","Tên Chi nhánh":"BRANCH_NAME","Lần cấp":"ISSUE_NUMBER","Ngày cấp":"ISSUE_DATE", "Địa chỉ":"ADDRESS", "Người đứng tên":"NGUOI_DUNG_TEN", "Ngày sinh":"DOB", "Số CCCD/CMT":"CITIZEN_ID_NUM", "Ngày cấp CMT": "CITIZEN_ID_ISSUE_DATE", "Nơi cấp": "ISSUE_PLACE", "Mã nhân viên nhập liệu": "EMPLOYEE_CODE", "Tên nhân viên nhập liệu":"EMPLOYEE_NAME"},"list_columns_to_ingest_to_stg":["BRANCH_CODE", "BRANCH_NAME", "ISSUE_NUMBER", "ISSUE_DATE", "ADDRESS", "NGUOI_DUNG_TEN", "DOB", "CITIZEN_ID_NUM", "CITIZEN_ID_ISSUE_DATE", "ISSUE_PLACE", "EMPLOYEE_CODE", "EMPLOYEE_NAME"],"stg_table_schema":{"table_name":"STGPROD.PCTT_NHAPLIEU_CN","columns_datatype":{'BRANCH_CODE': 'VARCHAR2(1000)', 'BRANCH_NAME': 'VARCHAR2(1000)', 'ISSUE_NUMBER': 'VARCHAR2(1000)', 'ISSUE_DATE': 'VARCHAR2(1000)', 'ADDRESS': 'VARCHAR2(1000)', 'NGUOI_DUNG_TEN': 'VARCHAR2(1000)', 'DOB': 'VARCHAR2(1000)', 'CITIZEN_ID_NUM': 'VARCHAR2(1000)', 'CITIZEN_ID_ISSUE_DATE': 'VARCHAR2(1000)', 'ISSUE_PLACE': 'VARCHAR2(1000)', 'EMPLOYEE_CODE': 'VARCHAR2(1000)', 'EMPLOYEE_NAME': 'VARCHAR2(1000)'}},"sql_transform_stg_to_dwh":"SELECT DISTINCT BRANCH_CODE, BRANCH_NAME, ISSUE_NUMBER, TO_DATE(REGEXP_REPLACE(ISSUE_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') ISSUE_DATE, ADDRESS, NGUOI_DUNG_TEN, TO_DATE(REGEXP_REPLACE(DOB, '^nan$|^$|^,$'), 'DD-MON-YYYY') DOB, CITIZEN_ID_NUM, TO_DATE(REGEXP_REPLACE(CITIZEN_ID_ISSUE_DATE, '^nan$|^$|^,$'), 'DD-MON-YYYY') CITIZEN_ID_ISSUE_DATE, ISSUE_PLACE, EMPLOYEE_CODE, EMPLOYEE_NAME, TO_DATE(:TODAY, 'YYYYMMDD') AS PROCESS_DATE FROM STGPROD.PCTT_NHAPLIEU_CN","dwh_table_schema":{"table_name":"F88DWH.W_PCTT_NHAPLIEU_CN","columns_datatype":{'BRANCH_CODE': 'VARCHAR2(1000)', 'BRANCH_NAME': 'VARCHAR2(1000)', 'ISSUE_NUMBER': 'VARCHAR2(1000)', 'ISSUE_DATE': 'DATE', 'ADDRESS': 'VARCHAR2(1000)', 'NGUOI_DUNG_TEN': 'VARCHAR2(1000)', 'DOB': 'DATE', 'CITIZEN_ID_NUM': 'VARCHAR2(1000)', 'CITIZEN_ID_ISSUE_DATE': 'DATE', 'ISSUE_PLACE': 'VARCHAR2(1000)', 'EMPLOYEE_CODE': 'VARCHAR2(1000)', 'EMPLOYEE_NAME': 'VARCHAR2(1000)', 'PROCESS_DATE':'DATE'},"merge_key":['BRANCH_CODE']}}])
    
    ### Task get all excel, pdf url and download excel file to local:
    download_excel_and_pdf = DownloadFilesInChildFolderOperator(task_id = parent_xcom_task_id, sharepoint_conn_id = sharepoint_conn_id, local_saved_folder = local_saved_folder, relative_url_folder_path=relative_url_folder_path)
    
    ### Task convert excel files to csv
    convert_excel_to_csv = Excel2CSVOperator(task_id = 'convert_excel_to_csv', sheet_name=sheet_name, parent_xcom_task_id=parent_xcom_task_id, local_saved_folder=local_saved_folder)
    
    ### Delete downloaded
    delete_all_downloaded_local_file = PythonOperator(task_id='delete_all_downloaded_local_file', python_callable=delete_downloaded, op_kwargs={'folder_path': local_saved_folder})

    ### Delete files on share point
    delete_files_on_share_point = DeleteFileSharePointOperator(task_id = 'delete_files_on_share_point', sharepoint_conn_id = sharepoint_conn_id, parent_xcom_task_id=parent_xcom_task_id)
    
    ### Insert data to dwh:
    for table_metadata in table_metadata_list.list:
        csv_file_path = os.path.join(local_saved_folder, f'*{table_metadata.xlsx_sheet_name}.csv')
        
        insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=oracle_conn_id, local_file_path=csv_file_path)
        
        create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
                                                    table_schema=table_metadata.stg_table_schema,
                                                    oracle_conn_id=oracle_conn_id,
                                                    insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

        create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}',
                                                table_schema=table_metadata.dwh_table_schema,
                                                oracle_conn_id=oracle_conn_id,
                                                insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)

        transform_table = STG2DWHOperators(
            task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',
            oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema,
            target_table_schema=table_metadata.dwh_table_schema,
            select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)

        convert_excel_to_csv >> create_and_truncate_stg_table >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table >> delete_files_on_share_point >> delete_all_downloaded_local_file
            
    ### Create flow:
    download_excel_and_pdf >> convert_excel_to_csv

if __name__ == '__main__':
    pass