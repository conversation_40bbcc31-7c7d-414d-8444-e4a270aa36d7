import glob
import logging
import os
import datetime
import shutil
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.operators.python import PythonOperator
from DAGs.utils import DAGMonitor
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from Provider.OracleProvider.operators.DDLOperators import DDLOperator
from SendMail.SendMail import dagmonitor_on_fail_callback

parent_dir = os.path.dirname(os.path.abspath(__file__))
DAG_ID = 'MAIN_SHAREPOINT2DWH'
description = 'Main Dag for SHAREPOINT2DWH to DWH'
start_date = datetime.datetime(2025, 1, 1)
schedule_interval = None
tags = ['main', 'sharepoint2dwh']
freq = None
pattern_type='SHAREPOINT2DWH'

def set_params(**context):
    return DAGMonitor.set_params(context['dag_run'].logical_date)


def delete_downloaded() -> None:
    folder_paths = glob.glob(os.path.join(parent_dir, '*', 'Downloaded'))

    for folder_path in folder_paths:
        if folder_path.__contains__('QLV') or folder_path.__contains__('CIMB'):
            continue
        try:
            shutil.rmtree(folder_path)
            print(f"Deleted folder: {folder_path}")
        except OSError as e:
            print(f"Error deleting folder: {folder_path} - {e}")
    shutil.rmtree(os.path.join(parent_dir, 'Downloaded'), ignore_errors=True)


with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'none_skipped'
         },
         start_date=start_date,
         catchup=False,
         schedule_interval=schedule_interval,
         max_active_runs=1,
         tags=tags
         ) as main_dag:
    start_main = EmptyOperator(task_id="Start")
    end_main = EmptyOperator(task_id="End")

    # Define the PythonOperator to execute set_params
    set_params_task = PythonOperator(
        task_id='set_params_task',
        python_callable=set_params,
        provide_context=True
    )
    delete_downloaded = PythonOperator(
        task_id='delete_downloaded',
        python_callable=delete_downloaded
    )

    list_sub_main_name = DAGMonitor.get_distinct_main_dagid(freq=freq, pattern_type=pattern_type)
    #print("all main_dag_id: " + list_sub_main_name)
    #hieumx2 fix loi TypeError: can only concatenate str (not "list") to str
    print("all main_dag_id: " + ", ".join(list_sub_main_name))
    list_sub_main_dag = []
    #1, Get all distinct main_dag_id of daily ->main_dag_id_daily_lst
    #2. Loop main_dag_id_daily_lst -> x {get by main dag id}
    #3. delete_downloaded
    for sub_main_dag_id in list_sub_main_name:
        try:
            sub_main_description = f'Main Dag for {sub_main_dag_id}'
            tags = [
                sub_main_dag_id.split('.')[-1] if '.' in sub_main_dag_id else sub_main_dag_id.split('_')[-1],
                'daily', 'main'
            ]
            dag_monitor_data = DAGMonitor.get_dag_monitor_data(main_dag_id=sub_main_dag_id)
            list_sub_dag = []

            for sub_dag in dag_monitor_data:
                try:
                    local_saved_folder = os.path.join(parent_dir, sub_dag.save_folder)
                    logging.info(f'local_saved_folder: {local_saved_folder}')

                    with DAG(
                            dag_id=f'{sub_dag.dag_id}',
                            schedule_interval=sub_dag.schedule_interval,
                            start_date=sub_dag.start_date,
                            description=sub_dag.description,
                            dagrun_timeout=datetime.timedelta(minutes=45),
                            catchup=False,
                            max_active_runs=1,
                            default_args={
                                'owner': 'F88-DE',
                                'retries': 3,
                                'retry_delay': datetime.timedelta(minutes=1),
                            },
                            on_failure_callback=dagmonitor_on_fail_callback,
                            tags=sub_dag.tags
                    ) as dag:
                        download_files = DownloadSharePointOperator(
                            task_id='download_files',
                            sharepoint_conn_id=sub_dag.source_conn_id,
                            department=sub_dag.department,
                            report_name=sub_dag.report_name,
                            local_saved_folder=local_saved_folder,
                            freq=sub_dag.freq,
                            _type=sub_dag.type
                        )

                        for table_metadata in sub_dag.table_metadata.list:
                            insert_data_to_stg = XLSX2DWHOperator(
                                task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}',
                                table_metadata=table_metadata,
                                oracle_conn_id=sub_dag.destination_conn_id
                            )
                            create_and_truncate_stg_table = DDLOperator(
                                task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}',
                                table_schema=table_metadata.stg_table_schema,
                                oracle_conn_id=sub_dag.destination_conn_id,
                                insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE
                            )
                            create_and_merge_dwh_table = DDLOperator(
                                task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}',
                                table_schema=table_metadata.dwh_table_schema,
                                oracle_conn_id=sub_dag.destination_conn_id,
                                insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE
                            )
                            transform_table = STG2DWHOperators(
                                task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',
                                oracle_conn_id=sub_dag.destination_conn_id,
                                source_table_schema=table_metadata.stg_table_schema,
                                target_table_schema=table_metadata.dwh_table_schema,
                                select_sql_command=table_metadata.sql_transform_stg_to_dwh,
                                insert_strategy=INSERT_STRATEGY.MERGE
                            )
                            create_and_truncate_stg_table >> download_files >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table

                        list_sub_dag.append(f'{sub_dag.dag_id}')

                except Exception as e:
                    print(f"Lỗi khi xử lý sub_dag {sub_dag.dag_id}: {e}")

            if list_sub_dag:
                try:
                    with DAG(
                            dag_id=sub_main_dag_id,
                            description=sub_main_description,
                            default_args={'owner': 'F88-DE'},
                            start_date=start_date,
                            catchup=False,
                            schedule_interval=None,
                            max_active_runs=1,
                            tags=tags
                    ) as sub_main_dag:
                        start_sub_main = EmptyOperator(task_id='Start')
                        end_sub_main = EmptyOperator(task_id='End')

                        trigger_tasks = [
                            TriggerDagRunOperator(
                                task_id=f"{item}_task",
                                trigger_dag_id=item,
                                wait_for_completion=True,
                                poke_interval=5,
                                deferrable=True,
                                conf={
                                    "set_params_task": "{{ dag_run.conf['set_params_task'] if 'set_params_task' in dag_run.conf else None }}"}
                            ) for item in list_sub_dag
                        ]

                        start_sub_main >> trigger_tasks >> end_sub_main

                    trigger_dag = TriggerDagRunOperator(
                        task_id=sub_main_dag_id,
                        trigger_dag_id=sub_main_dag_id,
                        wait_for_completion=True,
                        poke_interval=5,
                        deferrable=True,
                        conf={"set_params_task": "{{ task_instance.xcom_pull(task_ids='set_params_task') }}"}
                    )
                    list_sub_main_dag.append(trigger_dag)

                except Exception as e:
                    print(f"Lỗi khi xử lý main DAG {sub_main_dag_id}: {e}")

        except Exception as e:
            print(f"Lỗi khi xử lý sub_main_dag_id {sub_main_dag_id}: {e}")

    prev_task = set_params_task
    start_main >> prev_task

    for task in list_sub_main_dag:
        prev_task >> task
        prev_task = task

    prev_task >> delete_downloaded >> end_main
