from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.DDLOperators import DDL<PERSON>perator
from airflow import DAG
import datetime
import os
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaData

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'KPI_SHAREPOINT_QUARTER_KPI_TO_DWH'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
description = 'Lấy dữ liệu từ Excel rồi đưa lên DB'
tags = ['KPI', 'sharepoint2dwh']
overwrite = True
freq = 'quarterly'
oracle_conn_id = 'oracle_f88_dwh'
_type = 'SharePoint to Data Warehouse'
department = 'Project - Incentive'
saved_folder = os.path.join(parent_dir, 'Downloaded')
report_name_list=['HR', 'TAI CHINH', 'TNKH']                   
create_and_merge_dwh_table = None

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         catchup=False,
         tags=tags) as dag:

    for report_name in report_name_list:
        report_name = report_name.replace(' ', '')
        tables_metadata = ListTableMetaData(list=[{
            'xlsx_read_options': {'header': 6, 'skiprows': [1,2,3,4,5,6,7,8,9,10]},
            'rename_columns': {'QUARTER_ID': 'QUARTER_ID', 'KPI_CODE': 'KPI_CODE', 'KHOI': 'KHOI', 'DON_VI': 'DON_VI', 'PHONG': 'PHONG', 'CHI_TIEU': 'CHI_TIEU', 'KY_DO_LUONG': 'KY_DO_LUONG', 'DV_DO_LUONG': 'DV_DO_LUONG', 'THUC_TE': 'THUC_TE'},
            'datatype_mapping': {'QUARTER_ID': 'str', 'KPI_CODE': 'str', 'KHOI': 'str', 'DON_VI': 'str', 'PHONG': 'str', 'CHI_TIEU': 'str', 'KY_DO_LUONG': 'str', 'DV_DO_LUONG': 'str', 'THUC_TE': 'str'},
            'list_columns_to_ingest_to_stg': ['QUARTER_ID', 'KPI_CODE', 'KHOI', 'DON_VI', 'PHONG', 'CHI_TIEU', 'KY_DO_LUONG', 'DV_DO_LUONG', 'THUC_TE'],
            'stg_table_schema': {
                'table_name': f'STGPROD.QUARTER_KPI_{report_name}',
                'columns_datatype': {'QUARTER_ID': 'VARCHAR2(30)', 'KPI_CODE': 'VARCHAR2(30)', 'KHOI': 'VARCHAR2(100)', 'DON_VI': 'VARCHAR2(100)', 'PHONG': 'VARCHAR2(100)', 'CHI_TIEU': 'VARCHAR2(500)', 'KY_DO_LUONG': 'VARCHAR2(100)', 'DV_DO_LUONG': 'VARCHAR2(100)', 'THUC_TE': 'VARCHAR2(50)'}
            },
            'sql_transform_stg_to_dwh' : f"""select REGEXP_REPLACE(QUARTER_ID, '^nan$|^$|^,$') AS QUARTER_ID, REGEXP_REPLACE(KPI_CODE, '^nan$|^$|^,$') AS KPI_CODE, REGEXP_REPLACE(KHOI, '^nan$|^$|^,$') AS KHOI, REGEXP_REPLACE(DON_VI, '^nan$|^$|^,$') AS DON_VI, REGEXP_REPLACE(PHONG, '^nan$|^$|^,$') AS PHONG, REGEXP_REPLACE(CHI_TIEU, '^nan$|^$|^,$') AS CHI_TIEU, REGEXP_REPLACE(KY_DO_LUONG, '^nan$|^$|^,$') AS KY_DO_LUONG, REGEXP_REPLACE(DV_DO_LUONG, '^nan$|^$|^,$') AS DV_DO_LUONG, CAST(REPLACE(REGEXP_REPLACE(THUC_TE, '^nan$|^$|^,$'), ',', '') as NUMBER) AS THUC_TE from STGPROD.QUARTER_KPI_{report_name}""",
            'dwh_table_schema': {
                'table_name': 'F88DWH.W_QUARTER_KPI',
                'merge_key': ['QUARTER_ID', 'KPI_CODE', 'KHOI', 'DON_VI', 'PHONG', 'CHI_TIEU'],
                'columns_datatype': {'QUARTER_ID': 'VARCHAR2(30)', 'KPI_CODE': 'VARCHAR2(30)', 'KHOI': 'VARCHAR2(100)', 'DON_VI': 'VARCHAR2(100)', 'PHONG': 'VARCHAR2(100)', 'CHI_TIEU': 'VARCHAR2(500)', 'KY_DO_LUONG': 'VARCHAR2(100)', 'DV_DO_LUONG': 'VARCHAR2(100)', 'THUC_TE': 'NUMBER'}
            }
            }
])
        download_files = DownloadSharePointOperator(task_id=f'download_files_{report_name}',
                                            sharepoint_conn_id='sharepoint_f88_data',
                                            department=department,
                                            report_name=report_name,
                                            local_saved_folder=saved_folder,
                                            freq=freq,
                                            _type=_type
                                            )
        for table_metadata in tables_metadata.list:

            insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=oracle_conn_id)
            
            create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}', table_schema=table_metadata.stg_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

            if not create_and_merge_dwh_table:
                create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}', table_schema=table_metadata.dwh_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)
            
            transform_table = STG2DWHOperators(task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema, target_table_schema=table_metadata.dwh_table_schema, select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)

            create_and_truncate_stg_table >> download_files >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table