from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.DDLOperators import DDL<PERSON>perator
from airflow import DAG
import datetime
import os
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaData

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'KPI_SHAREPOINT_MONTHLY_KPI_TO_DWH'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 9 11 * *'
description = 'Lấy dữ liệu từ Excel rồi đưa lên DB'
tags = ['KPI', 'sharepoint2dwh']
overwrite = True
freq = 'monthly'
oracle_conn_id = 'oracle_f88_dwh'
_type = 'SharePoint to Data Warehouse'
department = 'Project - Trade MKT'
saved_folder = os.path.join(parent_dir, 'Downloaded')
report_name = 'KPI_MONTHLY'                 
create_and_merge_dwh_table = None

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         catchup=False,
         tags=tags) as dag:

        tables_metadata = ListTableMetaData(list=[{
            'rename_columns': {'YEAR_NUM': 'YEAR_NUM', 'MONTH_NUM': 'MONTH_NUM', 'SHORT_NM': 'SHORT_NM', 'SHOP_NM': 'SHOP_NM', 'KPI_FORM': 'KPI_FORM', 'KPI_TRAFFIC': 'KPI_TRAFFIC', 'KPI_TASK': 'KPI_TASK', 'KPI_KHM_DKXM': 'KPI_KHM_DKXM', 'KPI_KHM_DKOT': 'KPI_KHM_DKOT', 'KPI_KHM': 'KPI_KHM', 'KPI_SALE_DKXM': 'KPI_SALE_DKXM', 'KPI_SALE_DKOT': 'KPI_SALE_DKOT', 'KPI_SALE': 'KPI_SALE'},
            'datatype_mapping': {'YEAR_NUM': 'str', 'MONTH_NUM': 'str', 'SHORT_NM': 'str', 'SHOP_NM': 'str', 'KPI_FORM': 'str', 'KPI_TRAFFIC': 'str', 'KPI_TASK': 'str', 'KPI_KHM_DKXM': 'str', 'KPI_KHM_DKOT': 'str', 'KPI_KHM': 'str', 'KPI_SALE_DKXM': 'str', 'KPI_SALE_DKOT': 'str', 'KPI_SALE': 'str'},
            'list_columns_to_ingest_to_stg': ['YEAR_NUM', 'MONTH_NUM', 'SHORT_NM', 'SHOP_NM', 'KPI_FORM', 'KPI_TRAFFIC', 'KPI_TASK', 'KPI_KHM_DKXM', 'KPI_KHM_DKOT', 'KPI_KHM', 'KPI_SALE_DKXM', 'KPI_SALE_DKOT', 'KPI_SALE'],
            'stg_table_schema': {
                'table_name': f'STGPROD.QUARTER_KPI_TRADE_MKT',
                'columns_datatype': {'YEAR_NUM': 'VARCHAR2(30)', 'MONTH_NUM': 'VARCHAR2(30)', 'SHORT_NM': 'VARCHAR2(100)', 'SHOP_NM': 'VARCHAR2(200)', 'KPI_FORM': 'VARCHAR2(30)', 'KPI_TRAFFIC': 'VARCHAR2(30)', 'KPI_TASK': 'VARCHAR2(30)', 'KPI_KHM_DKXM': 'VARCHAR2(30)', 'KPI_KHM_DKOT': 'VARCHAR2(30)', 'KPI_KHM': 'VARCHAR2(30)', 'KPI_SALE_DKXM': 'VARCHAR2(30)', 'KPI_SALE_DKOT': 'VARCHAR2(30)', 'KPI_SALE': 'VARCHAR2(30)'}
            },
            'sql_transform_stg_to_dwh' : f"""select CAST(REGEXP_REPLACE(YEAR_NUM, '^nan$|^$|^,$') as NUMBER) AS YEAR_NUM, CAST(REGEXP_REPLACE(MONTH_NUM, '^nan$|^$|^,$') as NUMBER) AS MONTH_NUM, REGEXP_REPLACE(SHORT_NM, '^nan$|^$|^,$') AS SHORT_NM, REGEXP_REPLACE(SHOP_NM, '^nan$|^$|^,$') AS SHOP_NM, CAST(REGEXP_REPLACE(KPI_FORM, '^nan$|^$|^,$') as NUMBER) AS KPI_FORM, CAST(REGEXP_REPLACE(KPI_TRAFFIC, '^nan$|^$|^,$') as NUMBER) AS KPI_TRAFFIC, CAST(REGEXP_REPLACE(KPI_TASK, '^nan$|^$|^,$') as NUMBER) AS KPI_TASK, CAST(REGEXP_REPLACE(KPI_KHM_DKXM, '^nan$|^$|^,$') as NUMBER) AS KPI_KHM_DKXM, CAST(REGEXP_REPLACE(KPI_KHM_DKOT, '^nan$|^$|^,$') as NUMBER) AS KPI_KHM_DKOT, CAST(REGEXP_REPLACE(KPI_KHM, '^nan$|^$|^,$') as NUMBER) AS KPI_KHM, CAST(REGEXP_REPLACE(KPI_SALE_DKXM, '^nan$|^$|^,$') as NUMBER) AS KPI_SALE_DKXM, CAST(REGEXP_REPLACE(KPI_SALE_DKOT, '^nan$|^$|^,$') as NUMBER) AS KPI_SALE_DKOT, CAST(REGEXP_REPLACE(KPI_SALE, '^nan$|^$|^,$') as NUMBER) AS KPI_SALE from STGPROD.QUARTER_KPI_TRADE_MKT""",
            'dwh_table_schema': {
                'table_name': 'F88DWH.W_TRADE_KPI_F',
                'merge_key': ['YEAR_NUM', 'MONTH_NUM', 'SHORT_NM', 'SHOP_NM'],
                'columns_datatype': {'YEAR_NUM': 'NUMBER', 'MONTH_NUM': 'NUMBER', 'SHORT_NM': 'VARCHAR2(100)', 'SHOP_NM': 'VARCHAR2(200)', 'KPI_FORM': 'NUMBER', 'KPI_TRAFFIC': 'NUMBER', 'KPI_TASK': 'NUMBER', 'KPI_KHM_DKXM': 'NUMBER', 'KPI_KHM_DKOT': 'NUMBER', 'KPI_KHM': 'NUMBER', 'KPI_SALE_DKXM': 'NUMBER', 'KPI_SALE_DKOT': 'NUMBER', 'KPI_SALE': 'NUMBER'}
            }
            }
])
        download_files = DownloadSharePointOperator(task_id=f'download_files_{report_name}',
                                            sharepoint_conn_id='sharepoint_f88_data',
                                            department=department,
                                            report_name=report_name,
                                            local_saved_folder=saved_folder,
                                            freq=freq,
                                            _type=_type
                                            )
        for table_metadata in tables_metadata.list:

            insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=oracle_conn_id)
            
            create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}', table_schema=table_metadata.stg_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

            if not create_and_merge_dwh_table:
                create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}', table_schema=table_metadata.dwh_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)
            
            transform_table = STG2DWHOperators(task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema, target_table_schema=table_metadata.dwh_table_schema, select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)

            create_and_truncate_stg_table >> download_files >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table