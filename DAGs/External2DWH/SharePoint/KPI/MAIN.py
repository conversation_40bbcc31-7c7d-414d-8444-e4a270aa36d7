import os
import datetime
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

DAG_ID = 'MAIN_DAILY_SHAREPOINT2DWH_KPI'
description = 'Main Dag for SHAREPOINT2DWH to DWH KPI'
start_date = datetime.datetime(2023, 11, 13)
schedule_interval = None
tags = ['KPI', 'daily', 'main', 'sharepoint2dwh']

with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'all_success'
         },
         start_date=start_date,
         catchup=False,
         max_active_runs=1,
         schedule_interval=schedule_interval,
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id="Start", dag=dag)
    end = EmptyOperator(task_id="End", dag=dag)
    parent_dir = os.path.dirname(os.path.abspath(__file__))
    list_dags = next(os.walk(parent_dir))[2]

    trigger_KPI_SHAREPOINT_QUARTER_KPI_RESULT_TO_DWH = TriggerDagRunOperator(task_id='trigger_KPI_SHAREPOINT_QUARTER_KPI_RESULT_TO_DWH', trigger_dag_id='KPI_SHAREPOINT_QUARTER_KPI_RESULT_TO_DWH', poke_interval=5, wait_for_completion=True)

    trigger_KPI_SHAREPOINT_QUARTER_KPI_TO_DWH = TriggerDagRunOperator(task_id='trigger_KPI_SHAREPOINT_QUARTER_KPI_TO_DWH', trigger_dag_id='KPI_SHAREPOINT_QUARTER_KPI_TO_DWH', poke_interval=5, wait_for_completion=True)

    trigger_KPI_SHAREPOINT_RUN_PROC_KPI_TO_DWH = TriggerDagRunOperator(task_id='trigger_KPI_SHAREPOINT_RUN_PROC_KPI_TO_DWH', trigger_dag_id='KPI_SHAREPOINT_RUN_PROC_KPI_TO_DWH', poke_interval=5, wait_for_completion=True)

    start >> trigger_KPI_SHAREPOINT_QUARTER_KPI_RESULT_TO_DWH >> trigger_KPI_SHAREPOINT_QUARTER_KPI_TO_DWH >> trigger_KPI_SHAREPOINT_RUN_PROC_KPI_TO_DWH >> end