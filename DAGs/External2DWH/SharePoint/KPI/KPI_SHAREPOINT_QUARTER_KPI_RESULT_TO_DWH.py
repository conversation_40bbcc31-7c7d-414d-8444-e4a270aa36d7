from Provider.OracleProvider.utils import INSERT_STRATEGY
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from Provider.OracleProvider.operators.STG2DWHOperators import STG2DWHOperators
from Provider.OracleProvider.operators.FILE2DWHOperators import XLSX2DWHOperator
from Provider.OracleProvider.operators.DDLOperators import DD<PERSON>Operator
from airflow import DAG
import datetime
import os
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaData

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'KPI_SHAREPOINT_QUARTER_KPI_RESULT_TO_DWH'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
description = 'Lấy dữ liệu từ Excel rồi đưa lên DB'
tags = ['KPI', 'sharepoint2dwh']
overwrite = True
freq = None
oracle_conn_id = 'oracle_f88_dwh'
_type = 'SharePoint to Data Warehouse'
department='Project - KPI'
saved_folder = os.path.join(parent_dir, 'Downloaded')
report_name='HR'

### Trong một cột nào đấy ở Dag Monitor
tables_metadata = ListTableMetaData(list=[{
            'xlsx_sheet_name': 'QUARTER_KPI_RESULT',
            'rename_columns': {'QUARTER_ID': 'QUARTER_ID', 'KPI_CODE': 'KPI_CODE', 'KHOI': 'KHOI', 'DON_VI': 'DON_VI', 'PHONG': 'PHONG', 'DOI_TUONG': 'DOI_TUONG', 'KY_DO_LUONG': 'KY_DO_LUONG', 'CHI_TIEU': 'CHI_TIEU', 'NHOM_CHI_TIEU': 'NHOM_CHI_TIEU', 'DV_DO_LUONG': 'DV_DO_LUONG', 'LOAI_CHI_TIEU': 'LOAI_CHI_TIEU', 'DINH_NGHIA': 'DINH_NGHIA', 'TY_TRONG': 'TY_TRONG', 'MUC_TIEU_NGUONG': 'MUC_TIEU_NGUONG', 'MUC_TIEU_CO_BAN': 'MUC_TIEU_CO_BAN', 'MUC_TIEU_DAY_MANH': 'MUC_TIEU_DAY_MANH', 'THUC_TE': 'THUC_TE', 'KQ_HOAN_THANH': 'KQ_HOAN_THANH', 'KQ_CHI_TIEU': 'KQ_CHI_TIEU', 'NOTE': 'NOTE'},
            'datatype_mapping': {'QUARTER_ID': 'str', 'KPI_CODE': 'str', 'KHOI': 'str', 'DON_VI': 'str', 'PHONG': 'str', 'DOI_TUONG': 'str', 'KY_DO_LUONG': 'str', 'CHI_TIEU': 'str', 'NHOM_CHI_TIEU': 'str', 'DV_DO_LUONG': 'str', 'LOAI_CHI_TIEU': 'str', 'DINH_NGHIA': 'str', 'TY_TRONG': 'str', 'MUC_TIEU_NGUONG': 'str', 'MUC_TIEU_CO_BAN': 'str', 'MUC_TIEU_DAY_MANH': 'str', 'THUC_TE': 'str', 'KQ_HOAN_THANH': 'str', 'KQ_CHI_TIEU': 'str', 'NOTE': 'str'},
            'list_columns_to_ingest_to_stg': ['QUARTER_ID', 'KPI_CODE', 'KHOI', 'DON_VI', 'PHONG', 'DOI_TUONG', 'KY_DO_LUONG', 'CHI_TIEU', 'NHOM_CHI_TIEU', 'DV_DO_LUONG', 'LOAI_CHI_TIEU', 'DINH_NGHIA', 'TY_TRONG', 'MUC_TIEU_NGUONG', 'MUC_TIEU_CO_BAN', 'MUC_TIEU_DAY_MANH', 'THUC_TE', 'KQ_HOAN_THANH', 'KQ_CHI_TIEU', 'NOTE'],
            'stg_table_schema': {
                'table_name': 'STGPROD.QUARTER_KPI_RESULT',
                'columns_datatype': {'QUARTER_ID': 'VARCHAR2(30)', 'KPI_CODE': 'VARCHAR2(30)', 'KHOI': 'VARCHAR2(100)', 'DON_VI': 'VARCHAR2(100)', 'PHONG': 'VARCHAR2(100)', 'DOI_TUONG': 'VARCHAR2(100)', 'KY_DO_LUONG': 'VARCHAR2(100)', 'CHI_TIEU': 'VARCHAR2(500)', 'NHOM_CHI_TIEU': 'VARCHAR2(100)', 'DV_DO_LUONG': 'VARCHAR2(100)', 'LOAI_CHI_TIEU': 'VARCHAR2(100)', 'DINH_NGHIA': 'VARCHAR2(1000)', 'TY_TRONG': 'VARCHAR2(50)', 'MUC_TIEU_NGUONG': 'VARCHAR2(50)', 'MUC_TIEU_CO_BAN': 'VARCHAR2(50)', 'MUC_TIEU_DAY_MANH': 'VARCHAR2(50)', 'THUC_TE': 'VARCHAR2(50)', 'KQ_HOAN_THANH': 'VARCHAR2(50)', 'KQ_CHI_TIEU': 'VARCHAR2(50)', 'NOTE': 'VARCHAR2(500)'}
            },
            'sql_transform_stg_to_dwh' : """select REGEXP_REPLACE(QUARTER_ID, '^nan$|^$|^,$') AS QUARTER_ID, REGEXP_REPLACE(KPI_CODE, '^nan$|^$|^,$') AS KPI_CODE, REGEXP_REPLACE(KHOI, '^nan$|^$|^,$') AS KHOI, REGEXP_REPLACE(DON_VI, '^nan$|^$|^,$') AS DON_VI, REGEXP_REPLACE(PHONG, '^nan$|^$|^,$') AS PHONG, REGEXP_REPLACE(DOI_TUONG, '^nan$|^$|^,$') AS DOI_TUONG, REGEXP_REPLACE(KY_DO_LUONG, '^nan$|^$|^,$') AS KY_DO_LUONG, REGEXP_REPLACE(CHI_TIEU, '^nan$|^$|^,$') AS CHI_TIEU, REGEXP_REPLACE(NHOM_CHI_TIEU, '^nan$|^$|^,$') AS NHOM_CHI_TIEU, REGEXP_REPLACE(DV_DO_LUONG, '^nan$|^$|^,$') AS DV_DO_LUONG, REGEXP_REPLACE(LOAI_CHI_TIEU, '^nan$|^$|^,$') AS LOAI_CHI_TIEU, REGEXP_REPLACE(DINH_NGHIA, '^nan$|^$|^,$') AS DINH_NGHIA, CAST(REPLACE(REGEXP_REPLACE(TY_TRONG, '^nan$|^$|^,$'), ',', '') as NUMBER) AS TY_TRONG, CAST(REPLACE(REGEXP_REPLACE(MUC_TIEU_NGUONG, '^nan$|^$|^,$'), ',', '') as NUMBER) AS MUC_TIEU_NGUONG, CAST(REPLACE(REGEXP_REPLACE(MUC_TIEU_CO_BAN, '^nan$|^$|^,$'), ',', '') as NUMBER) AS MUC_TIEU_CO_BAN, CAST(REPLACE(REGEXP_REPLACE(MUC_TIEU_DAY_MANH, '^nan$|^$|^,$'), ',', '') as NUMBER) AS MUC_TIEU_DAY_MANH, CAST(REPLACE(REGEXP_REPLACE(THUC_TE, '^nan$|^$|^,$'), ',', '') as NUMBER) AS THUC_TE, CAST(REPLACE(REGEXP_REPLACE(KQ_HOAN_THANH, '^nan$|^$|^,$'), ',', '') as NUMBER) AS KQ_HOAN_THANH, CAST(REPLACE(REGEXP_REPLACE(KQ_CHI_TIEU, '^nan$|^$|^,$'), ',', '') as NUMBER) AS KQ_CHI_TIEU, REGEXP_REPLACE(NOTE, '^nan$|^$|^,$') AS NOTE from STGPROD.QUARTER_KPI_RESULT""",
            'dwh_table_schema': {
                'table_name': 'F88DWH.W_QUARTER_KPI_RESULT',
                'merge_key': ['QUARTER_ID', 'KPI_CODE', 'KHOI', 'DON_VI', 'PHONG', 'DOI_TUONG', 'CHI_TIEU'],
                'columns_datatype': {'QUARTER_ID': 'VARCHAR2(30)', 'KPI_CODE': 'VARCHAR2(30)', 'KHOI': 'VARCHAR2(100)', 'DON_VI': 'VARCHAR2(100)', 'PHONG': 'VARCHAR2(100)', 'DOI_TUONG': 'VARCHAR2(100)', 'KY_DO_LUONG': 'VARCHAR2(100)', 'CHI_TIEU': 'VARCHAR2(500)', 'NHOM_CHI_TIEU': 'VARCHAR2(100)', 'DV_DO_LUONG': 'VARCHAR2(100)', 'LOAI_CHI_TIEU': 'VARCHAR2(100)', 'DINH_NGHIA': 'VARCHAR2(1000)', 'TY_TRONG': 'NUMBER', 'MUC_TIEU_NGUONG': 'NUMBER', 'MUC_TIEU_CO_BAN': 'NUMBER', 'MUC_TIEU_DAY_MANH': 'NUMBER', 'THUC_TE': 'NUMBER', 'KQ_HOAN_THANH': 'NUMBER', 'KQ_CHI_TIEU': 'NUMBER', 'NOTE': 'VARCHAR2(500)'}
            }
            }
])
                                

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         catchup=False,
         tags=tags) as dag:

    download_files = DownloadSharePointOperator(task_id='download_files',
                                            sharepoint_conn_id='sharepoint_f88_data',
                                            department=department,
                                            report_name=report_name,
                                            local_saved_folder=saved_folder,
                                            freq=freq,
                                            _type=_type
                                            )

    for table_metadata in tables_metadata.list:

        insert_data_to_stg = XLSX2DWHOperator(task_id=f'insert_data_to_{table_metadata.stg_table_schema.table_name}', table_metadata=table_metadata, oracle_conn_id=oracle_conn_id)
        
        create_and_truncate_stg_table = DDLOperator(task_id=f'ddl_table_{table_metadata.stg_table_schema.table_name}', table_schema=table_metadata.stg_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_TRUNCATE)

        create_and_merge_dwh_table = DDLOperator(task_id=f'ddl_table_{table_metadata.dwh_table_schema.table_name}', table_schema=table_metadata.dwh_table_schema, oracle_conn_id=oracle_conn_id, insert_strategy=INSERT_STRATEGY.CREATE_AND_MERGE)

        transform_table = STG2DWHOperators(task_id=f'Transform_{table_metadata.stg_table_schema.table_name}_and_load_to_{table_metadata.dwh_table_schema.table_name}',oracle_conn_id=oracle_conn_id, source_table_schema=table_metadata.stg_table_schema, target_table_schema=table_metadata.dwh_table_schema, select_sql_command=table_metadata.sql_transform_stg_to_dwh, insert_strategy=INSERT_STRATEGY.MERGE)

        create_and_truncate_stg_table >> download_files >> insert_data_to_stg >> create_and_merge_dwh_table >> transform_table