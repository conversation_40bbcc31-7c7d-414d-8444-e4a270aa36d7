import os
import datetime
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
import datetime
from airflow.hooks.oracle_hook import OracleHook

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'KPI_SHAREPOINT_RUN_PROC_KPI_TO_DWH'
description = 'Run procedure for KPI'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
tags = ['KPI', 'sharepoint2dwh']
overwrite = True
freq = 'quarterly'
_type = 'SharePoint to Data Warehouse'
department = 'Project - Incentive'


def run_proceduce(conn_id='oracle_f88_dwh', **kwargs):
    hook = OracleHook(oracle_conn_id=conn_id)
    dag_run = kwargs['dag_run']

    # Get last day of last quarter:
    # Lấy ngày hiện tại
    today = dag_run.logical_date

    # Xác định quý của ngày hiện tại
    current_quarter = (today.month - 1) // 3 + 1

    # Tính toán ngày đầu của quý trước
    if current_quarter == 1:
        # Nếu đang ở quý 1, l<PERSON>y ngày cuối của năm trước
        p_date = datetime.date(today.year - 1, 12, 31)
    else:
        # Nếu không phải quý 1, lấy ngày cuối của quý trước
        last_quarter_start = datetime.date(today.year, (current_quarter - 1) * 3, 1)
        p_date = last_quarter_start - datetime.timedelta(days=1)
    p_date = p_date.strftime("%Y%m%d")
    # query = """BEGIN 
	# PKG_QUY_TEST.MAIN('"""+str(p_date)+"""');
    # END;
    # """
    print(str(p_date))
    query = """BEGIN 
    F88DWH.PKG_QUY.MAIN('"""+str(p_date)+"""') ;-- lấy biến ngày cuối quý trước
    END;
    """
    hook.run(query, autocommit=True)

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')
    run_procedure = PythonOperator(task_id='run_procedure',
                                   python_callable=run_proceduce)
    end = EmptyOperator(task_id='End')
    start >> run_procedure >> end
