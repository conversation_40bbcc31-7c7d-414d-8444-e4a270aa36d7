from DAGs.utils import DAGMonitor
from airflow.utils.context import Context
from airflow.operators.python import PythonOperator
from airflow import settings
from airflow.models import Variable
from sqlalchemy import create_engine
from sqlalchemy import text
from airflow import DAG
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime, timedelta
import logging

def send_mail(p_mail_to, p_subject, p_content):
    
    email_var = Variable.get('BI_EMAIL', deserialize_json=True)
    sender_address = email_var.get('email')
    sender_pass = email_var.get('password')

    #Setup the MIME
    message = MIMEMultipart()
    message['From'] = sender_address
    message['To'] = p_mail_to
    message['Subject'] = p_subject
    #message.attach(MIMEText(p_content, 'plain'))
    message.attach(MIMEText(p_content, 'html'))

    session = smtplib.SMTP('smtp.f88.co', 587)
    session.starttls()
    session.login(sender_address, sender_pass)
    
    #text = message.as_string() recipients.split(',')
    session.sendmail(sender_address, p_mail_to.split(','), message.as_string())
    session.quit()
    logging.info('Mail Sent')

################################ CONTENT ###################################
def get_data_report(**context: Context):
    today = DAGMonitor.set_params_operator(**context)['TODAY']
    
    engine = create_engine(settings.SQL_ALCHEMY_CONN)

    v_mail_content = """<html><head>
    <style>
        html {font-family: arial;}
        table {border-collapse: collapse; font-size: 10pt;}
        th, td {border: 1px solid #999; text-align: left; padding: 3px 5px;}
    </style>
    </head><body>"""

    v_mail_content += "<h1>AIRFLOW DAGs REPORT DAILY " + datetime.strptime(today, '%Y%m%d').strftime('%d/%m/%Y') + chr(10) + "</h1>"

    ################# JOB ERROR #################

    logging.info('Get data job error')

    v_mail_content += "<h3>DAGs JOB ERROR</h3>"

    query = f"""with dag_state as (select id, max(id) over(partition by dag_id) as max_id, dag_id, state, start_date, end_date from dag_run tf where date(start_date) = to_date('{today}', 'YYYYMMDD') and state <> 'success'and dag_id not like '%MAIN%')
    select dag_id, state, start_date, end_date from dag_state where max_id = id"""
    
    with engine.connect() as conn:
        rows = conn.execute(text(query)).fetchall()

    v_mail_content += """<table style="width: 100%">
        <tr>
        <th>DAG Name</th>
        <th>Status</th>
        <th>Start_time</th>
        <th>End_time</th>
        </tr>
    """

    for row in rows:
        v_mail_content += "<tr>"
        v_mail_content += "<td>" + row[0] + "</td>"
        v_mail_content += "<td>" + row[1] + "</td>"
        v_mail_content += "<td style='text-align: right;'>" + str(row[2]) + "</td>"
        v_mail_content += "<td style='text-align: right;'>" + str(row[3]) + "</td>"  
        v_mail_content += "</tr>"
    del rows

    v_mail_content += "</table>"

    v_mail_content += "</body></html>"

    v_mail_subject = "Airflow DAGs Daily Report " + datetime.strptime(today, '%Y%m%d').strftime('%d/%m/%Y')
    v_mail_to = '<EMAIL>,<EMAIL>,<EMAIL>'
    

    send_mail(v_mail_to, v_mail_subject, v_mail_content)
    
with DAG(dag_id='DAILY_REPORT_AIRFLOW_JOBS', description='Báo cáo tình trạng job Airflow hàng ngày vào 8h30 sáng',
        dagrun_timeout=timedelta(minutes=10),
        catchup=False,
        max_active_runs=1,
        default_args={
            'owner': 'F88-DE',
            'retries': 3,
            'retry_delay': timedelta(minutes=1),
             'trigger_rule': 'all_success'
         },
         start_date=datetime(2023, 12, 21),
         schedule='30 8 * * *',
         tags=['report','airflow','daily']
         ) as dag:
    get_data_report_dag = PythonOperator(task_id='get_data_report_and_send_mail', python_callable=get_data_report)

if __name__ == '__main__':
    get_data_report()