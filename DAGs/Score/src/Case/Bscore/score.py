from joblib import load
import pandas as pd
import re
import csv
import os, sys
from datetime import datetime

# datetime object containing current date and time
now = datetime.now()
 
# dd/mm/YY H:M:S
dt_string = now.strftime("%Y/%m/%d %H:%M:%S")



ETL_DIR = os.path.abspath(os.path.dirname(__file__))
MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ''))
sys.path.append(ETL_DIR)
sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

def score(date_wid):
    date_wid=int(date_wid)
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)
    data=pd.read_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + 'dd_0.csv'))
    data_n15 = data.loc[data['ASSET_TYPE_15max'] == 0]
    data_n15 = data_n15.assign(ASSET_TYPE_15 = data_n15['ASSET_TYPE_15max'])

    def scaler_n15(data_s):
        scale_para = load(os.path.join(MODEL_DIR,'model_scale_n15.joblib'))
        for na in ['LOAN_AMTmean', 'LOAN_AMTmax', 'LOAN_AMTsum', 'COLLATERAL_AMT_ACTIVEmean', 'COLLATERAL_AMT_ACTIVEmax',
                    'COLLATERAL_AMT_ACTIVEmin', 'DISBURSE_AMT_CLOSEDmax', 'DISBURSE_AMTmean', 'TRA_CPV',
                    'COLLATERAL_AMTmean', 'COLLATERAL_AMTmax', 'COLLATERAL_AMTmin']:
            std = (data_s[na] - scale_para[na]['min']) / (scale_para[na]['max'] - scale_para[na]['min'])
            data_s[na] = std
        return data_s

    def fna(data_f):
        data_f.fillna(-1, inplace=True)
        return data_f
        
    dd_n15 = data_n15[['DATE_WID', 'CUSTOMER_CODE', 'DPD', 'ASSET_TYPE_15max']]
    data_n15 = data_n15.drop(['CUSTOMER_CODE', 'DATE_WID', 'DPD', 'ASSET_TYPE_15max'], axis=1)
    data_n15 = scaler_n15(data_n15)
    data_n15 = fna(data_n15)

    clf_n15 = load(os.path.join(MODEL_DIR,'model_clf_n15.joblib'))
    dd_n15['SCORE'] = clf_n15.predict_proba(data_n15)[:,1]
    dd_n15.loc[(dd_n15['DPD'] <= 0) & (dd_n15['SCORE'] < 1) & (dd_n15['SCORE'] > .65), 'SCORE'] = .65
    dd_n15.loc[(dd_n15['DPD'] > 0) & (dd_n15['DPD'] <= 2) & (dd_n15['SCORE'] < 1), 'SCORE'] = .65
    dd_n15.loc[dd_n15['DPD'] > 2, 'SCORE'] = .84
    dd_n15.loc[dd_n15['DPD'] >= 10, 'SCORE'] = 1.1
    dd_n15.loc[dd_n15['DPD'] >= 30, 'SCORE'] = 2
        
    dd_n15['RANK'] = pd.cut(dd_n15['SCORE'], 
                            bins=[0, 0.1,  0.21,  0.65, 1, 2], 
                            labels=['A', 'B', 'C', 'D', 'E']
                            )
                            
    dd_n15['BSCORE'] = pd.cut(dd_n15['SCORE'], 
                            bins=[0, 0.1,  0.21,  0.65, 2], 
                            labels=[650, 550, 450, 350]
                            )

    dd_n15['date_created'] = str(now.strftime("%Y/%m/%d%H:%M:%S"))                        


    data_15 = data.loc[data['ASSET_TYPE_15max'] == 1]
    data_15 = data_15.assign(ASSET_TYPE_15 = data_15['ASSET_TYPE_15max'])

    def scaler_15(data_s):
        scale_para = load(os.path.join(MODEL_DIR,'model_scale_15.joblib'))
        for na in ['LOAN_AMTmean', 'LOAN_AMTmax', 'LOAN_AMTsum', 'COLLATERAL_AMT_ACTIVEmean', 'COLLATERAL_AMT_ACTIVEmax',
                    'COLLATERAL_AMT_ACTIVEmin', 'DISBURSE_AMT_CLOSEDmax', 'DISBURSE_AMTmean', 'TRA_CPV',
                    'COLLATERAL_AMTmean', 'COLLATERAL_AMTmax', 'COLLATERAL_AMTmin']:
            std = (data_s[na] - scale_para[na]['min']) / (scale_para[na]['max'] - scale_para[na]['min'])
            data_s[na] = std
        return data_s
        
    dd_15 = data_15[['DATE_WID', 'CUSTOMER_CODE', 'DPD', 'ASSET_TYPE_15max']]
    data_15 = data_15.drop(['CUSTOMER_CODE', 'DATE_WID', 'DPD', 'ASSET_TYPE_15max'], axis=1)
    data_15 = scaler_15(data_15)
    data_15 = fna(data_15)

    clf_15 = load(os.path.join(MODEL_DIR,'model_clf_15.joblib'))
    dd_15['SCORE'] = clf_15.predict_proba(data_15)[:,1]
    dd_15.loc[(dd_15['DPD'] <= 0) & (dd_15['SCORE'] < 1) & (dd_15['SCORE'] > .65), 'SCORE'] = .65
    dd_15.loc[(dd_15['DPD'] > 0) & (dd_15['DPD'] <= 2) & (dd_15['SCORE'] < 1), 'SCORE'] = .65
    dd_15.loc[dd_15['DPD'] > 2, 'SCORE'] = .99
    dd_15.loc[dd_15['DPD'] >= 10, 'SCORE'] = 1.1
    dd_15.loc[dd_15['DPD'] >= 30, 'SCORE'] = 2
        
    dd_15['RANK'] = pd.cut(dd_15['SCORE'], 
                            bins=[0, 0.001, 0.07, 0.65, 1, 2], 
                            labels=['A', 'B', 'C', 'D', 'E']
                            )
                            
    dd_15['BSCORE'] = pd.cut(dd_15['SCORE'], 
                            bins=[0, 0.001, 0.07, 0.65,  2], 
                            labels=[650, 550, 450, 350]
                            )
                            
    dd_15['date_created'] = str(now.strftime("%Y/%m/%d%H:%M:%S"))
    
    pd.concat((dd_n15, dd_15)).to_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'), index=False)
    try:
        print("Start delete file score")
        file_path = os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + 'dd_0.csv')

        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Đã xóa: {file_path}")
        else:
            print(f"Không tìm thấy file: {file_path}")
    except:
        print("Error: Can not delete file")
    return pd.concat((dd_n15, dd_15))


#def main():
#    df = score(20240719)
#if __name__ == "__main__":
#    main()