import pandas as pd
import numpy as np

from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
from airflow.providers.oracle.hooks.oracle import OracleHook
import cx_Oracle

def log():
    hook = OracleHook(conn_id= 'oracle_f88_dwh')
    conn = hook.get_connection(conn_id= 'oracle_f88_dwh')
    username = conn.login
    password = conn.password
    host = conn.host
    port = conn.port
    schema = conn.schema
    dsn = cx_Oracle.makedsn(host, port, service_name=schema)
    connection = cx_Oracle.connect(username, password, dsn)
    cursor=connection.cursor()
    sql_str_1 = "truncate table F88DWH.W_B_SCORE_CHANGE_F"
    sql_str_2 = """insert into F88DWH.W_B_SCORE_CHANGE_F 
                SELECT
                (SELECT DATE_WID FROM F88DWH.W_B_SCORE_F WHERE ROWNUM=1) AS DATE_WID,
                CUSTOMER_CODE,
                	SCORE,
                	<PERSON>CORE,
                	"RANK" ,
                (SELECT CREATED_DATE  FROM F88DWH.W_B_SCORE_F WHERE ROWNUM=1) AS DATE_CREATED
                FROM
                (SELECT CUSTOMER_CODE , 
                	SCORE,
                	BSCORE,
                	"RANK" 
                FROM
                F88DWH.W_B_SCORE_F
                MINUS
                SELECT CUSTOMER_CODE,
                	SCORE,
                	BSCORE,
                	"RANK" 
                FROM
                F88DWH.W_B_SCORE_BK_F
                ) B_SCORE_TBL"""    
    sql_str_3 = """UPDATE F88DWH.W_B_SCORE_LOG_F
                SET CRN_ROW_IND = 0
                WHERE CUSTOMER_CODE IN (SELECT customer_Code FROM F88DWH.W_B_SCORE_CHANGE_F)"""
    sql_str_4 = """INSERT INTO F88DWH.W_B_SCORE_LOG_F
                SELECT a.*, a.DATE_WID  AS  VALID_FROM, 30000101 AS VALID_TO, 1 AS CRN_ROW_IND FROM F88DWH.W_B_SCORE_F a
                WHERE a.CUSTOMER_CODE IN (SELECT CUSTOMER_CODE FROM F88DWH.W_B_SCORE_CHANGE_F)"""
    # read sql - E
    print("Truncate table log change")
    cursor.execute(sql_str_1)
    connection.commit()
    print("Insert data log change")
    cursor.execute(sql_str_2)
    connection.commit()
    
    print("Update CRN_ROW_IND customer_code change")
    cursor.execute(sql_str_3)
    connection.commit()
    
    print("Insert customer_code change")
    cursor.execute(sql_str_4)
    connection.commit()
    
def main():
    back_up()

if __name__ == "__main__":
    main()