import pandas as pd
import numpy as np



from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
import re
import datetime
import os, sys


#ETL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../ETL'))
#MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__)))
#sys.path.append(ETL_DIR)
#sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

def ETL(date_wid, conn_id='oracle_f88_dwh'):

    #date_wid=pd.to_datetime(int(date_wid) , format='%Y%m%d')
    date_wid=int(date_wid)
    if pd.to_datetime(date_wid, format='%Y%m%d').day == 2:
        hook = BaseHook.get_hook(conn_id)
        conn_uri = hook.get_uri()
        connection = create_engine(conn_uri, echo=True)
        
        # sql string
        sql_str_1 = """select
        act.date_wid, activity_entity_wid, loan_wid, activity_wid, colection_type, start_time, end_time, promise_status
        from f88dwh.w_activity_entity_f act
        left join f88dwh.w_calendar_d cal
        on cal.date_wid = act.date_wid
        where cal.month_num = extract(month from ADD_MONTHS((to_date({date_wid},'yyyymmdd')),-1)) and cal.year_num = extract(year from ADD_MONTHS((to_date({date_wid},'yyyymmdd')),-1))
        and act.DEBT_ID != 'f88-system-debt'""".format(date_wid=date_wid)
        
        sql_str_2 = """ select
            dtl.loan_wid,
            cusd.customer_code,  
            dtl.disburse_date_wid,
            cal.year_num - cusd.birth_day cust_age,
            lcus.loai_hinh_cu_tru
          from f88dwh.w_loan_dtl_f dtl
            left join f88dwh.vw_W_loan_customer_f lcus
              on dtl.loan_wid = lcus.loan_wid
            left join f88dwh.vw_w_customer_d cusd
              on cusd.customer_wid = dtl.customer_wid
            left join f88dwh.w_shop_d shop
              on shop.shop_wid = dtl.shop_wid
            left join f88dwh.w_calendar_d cal
              on cal.date_wid = dtl.disburse_date_wid
          where
            cal.date_tm <= last_day(to_date({date_wid},'yyyymmdd') - interval '2' month) and
            dtl.status in ('14','300')
            """.format(date_wid=date_wid)
    
        sql_str_3 = """with s1 as (
        select
        loan_wid
        from f88dwh.w_loan_dtl_f dtl
        left join f88dwh.w_calendar_d cal
        on dtl.disburse_date_wid = cal.date_wid
        where
        cal.date_tm <= last_day(to_date({date_wid},'yyyymmdd') - interval '2' month) and
        dtl.status in ('14','300')
        )
        select
          s1.loan_wid,
          dl.date_wid,
          dl.overdue_days crn_dpd
        from s1
          left join f88dwh.w_loan_daily_f dl
            on dl.loan_wid = s1.loan_wid
          left join f88dwh.w_calendar_d cal
            on dl.date_wid = cal.date_wid
        where
          dl.year_num = to_number(extract(year from ADD_MONTHS(to_date({date_wid},'yyyymmdd'),-1))) and
          dl.month_num = to_number(extract(month from ADD_MONTHS(to_date({date_wid},'yyyymmdd'),-1))) and
          cal.day_num_in_month in ('10','20')""".format(date_wid=date_wid)
    
        sql_str_4 = """with s1 as (
        select
          loan_wid
        from f88dwh.w_loan_dtl_f dtl
          left join f88dwh.w_calendar_d cal
            on dtl.disburse_date_wid = cal.date_wid
        where
          cal.date_tm <= last_day(to_date({date_wid},'yyyymmdd') - interval '2' month) and
          dtl.status in ('14','300')
          )
        select
          s1.loan_wid,
          dl.date_wid BOM_DATE_TM,
          dl.overdue_days BOM_DPD
        from s1
          left join f88dwh.w_loan_daily_f dl
            on dl.loan_wid = s1.loan_wid
          left join f88dwh.w_calendar_d cal
            on dl.date_wid = cal.date_wid
        where
          dl.year_num = to_number(extract(year from to_date({date_wid},'yyyymmdd'))) and
          dl.month_num = to_number(extract(month from to_date({date_wid},'yyyymmdd'))) and
          cal.day_num_in_month = '1'""".format(date_wid=date_wid)
    
        sql_str_5 = """select
          dtl.loan_wid, substr(disburse_date_wid,1,4) disburse_year, ast.loan_asset_nm
        from f88dwh.w_loan_asset_f ast
          left join f88dwh.w_loan_dtl_f dtl
            on to_number(ast.loan_wid) = dtl.loan_wid
          left join f88dwh.w_calendar_d cal
            on cal.date_wid = dtl.disburse_date_wid
        where
          cal.date_tm <= last_day(to_date({date_wid},'yyyymmdd') - interval '2' month) and
          dtl.status in ('14','300')
          """.format(date_wid=date_wid)
        
        sql_str_6 = """select
          distinct(cusd.customer_code)
        from f88dwh.w_loan_dtl_f dtl
          left join f88dwh.vw_w_customer_d cusd
            on cusd.customer_wid = dtl.customer_wid
          left join f88dwh.w_calendar_d cal
            on cal.date_wid = dtl.disburse_date_wid
        where
          cal.month_num = extract(month from ADD_MONTHS((to_date({date_wid},'yyyymmdd')),-1)) and cal.year_num = extract(year from ADD_MONTHS((to_date({date_wid},'yyyymmdd')),-1)) and
          dtl.status in ('14', '300') 
          """.format(date_wid=date_wid)        
        
        
    
    
        # read sql - E    
        
        dt_call1 = pd.read_sql(sql_str_1, connection)
        dt_req1  = pd.read_sql(sql_str_2, connection)
        crn_dpd1 = pd.read_sql(sql_str_3, connection) 
        BOM_dpd1 = pd.read_sql(sql_str_4, connection)
        ast1     = pd.read_sql(sql_str_5, connection)
        dt_req3  = pd.read_sql(sql_str_6, connection)
        
        print(dt_call1.shape)
        print(dt_req1.shape)
        print(crn_dpd1.shape)
        print(BOM_dpd1.shape)
        print(ast1.shape)
        #upper
        #dd_1.columns = dd_1.columns.str.upper()
        #dd_2.columns = dd_2.columns.str.upper()
        #dd_3.columns = dd_3.columns.str.upper()
        #dd_4.columns = dd_4.columns.str.upper()
        #dd_5.columns = dd_5.columns.str.upper()
        #dd_6.columns = dd_6.columns.str.upper()
        
        # transform - T
        dt_call1['end_time'] = pd.to_datetime(dt_call1['end_time'])
        dt_call1['start_time'] = pd.to_datetime(dt_call1['start_time'])
        
        dt_call2 = dt_call1.copy()
        dt_call2.columns = map(str.lower, dt_call2.columns)
        dt_call2['dif'] = (dt_call2['end_time'] - dt_call2['start_time']).dt.total_seconds()
        
        #print(dt_call2.shape)
        
        dt_req2 = dt_req1.copy()
        dt_req2.columns = map(str.lower, dt_req2.columns)
        dt_req2 = dt_req2.drop_duplicates(subset='loan_wid', keep='first')
        
        dt_req4 = dt_req3.rename(columns=str.lower)
        dt_req4['date_wid'] = ''
        dt_req4['risk_group'] = 2
        dt_req4['score'] = 0.599999
        dt_req4['curent_dpd'] = 0.0
        dt_req4['date_wid'] = pd.to_numeric(dt_req4['date_wid'])
        
        dt_req4.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_dd_req4.csv'), index=False)
        
        #print(dt_req2.shape)
        
        # Nhac no bang dien thoai
        call1 = dt_call2[(dt_call2['colection_type'] == 1) & (dt_call2['dif'].notna()) & (dt_call2['dif'] != 0)]
        call1 = call1[['loan_wid']].drop_duplicates()
        call1['flag_col_call'] = 1
        
        #print(call1.shape)
        
        flag_col_call_prev = dt_req2.merge(call1, on='loan_wid', how='left')
        flag_col_call_prev['flag_col_call'] = flag_col_call_prev['flag_col_call'].fillna(0).astype(int)
        flag_col_call_prev = flag_col_call_prev.rename(columns={'flag_col_call': 'flag_col_call_prev_M'})
        flag_col_call_prev = flag_col_call_prev[flag_col_call_prev['flag_col_call_prev_M'] == 1]
        flag_col_call_prev = flag_col_call_prev[['loan_wid', 'flag_col_call_prev_M']]
        
        #print(flag_col_call_prev.shape)
        
        # Flag loan_wid khong hua tra
        call2 = dt_call2[(dt_call2['colection_type'] == 1) & (dt_call2['promise_status'] == 3)]
        call2 = call2[['loan_wid']]
        call2['flag_no_promise'] = 1
        
        
        #print(call2.shape)
        
        no_promise_sum = dt_req2.merge(call2, on='loan_wid', how = 'left')
        no_promise_sum['flag_no_promise'] = no_promise_sum['flag_no_promise'].fillna(0).astype(int)
        no_promise_sum = no_promise_sum[no_promise_sum['flag_no_promise'] == 1]
        no_promise_sum[['loan_wid','flag_no_promise']]
        no_promise_sum = no_promise_sum.groupby('loan_wid').agg({'flag_no_promise': 'sum'})
        no_promise_sum = no_promise_sum.rename(columns = {'flag_no_promise':'no_promise_sum'})
        no_promise_sum['flag_no_promise_ever'] = 1
        
        
        #print(no_promise_sum.shape)
        
        # du lieu dpd 
        crn_dpd2 = crn_dpd1.copy()
        crn_dpd2 = crn_dpd2.sort_values(by=['loan_wid', 'date_wid'])
        crn_dpd2['crn_dpd'] = crn_dpd2['crn_dpd'].apply(lambda x: 0 if pd.isna(x) or x < 0 else x)
        crn_dpd2 = crn_dpd2.rename(columns={'crn_dpd': 'crn_dpd_10'})
        crn_dpd2['crn_dpd_20'] = crn_dpd2.groupby('loan_wid')['crn_dpd_10'].shift(-1)
        crn_dpd2['crn_dpd_20'] = crn_dpd2['crn_dpd_20'].fillna(0)
        crn_dpd2 = crn_dpd2.drop_duplicates('loan_wid', keep='first')
        crn_dpd2.columns = map(str.lower, crn_dpd2.columns)
        crn_dpd2 = crn_dpd2.drop(columns=['date_wid'])
        
        
        #print(crn_dpd2.shape)
        
        crn_dpd2['B1_20_flag'] = crn_dpd2['crn_dpd_20'].apply(lambda x: 1 if x > 0 else 0)
        b1_flag = crn_dpd2[['loan_wid', 'crn_dpd_10', 'B1_20_flag']]
        
        #print(b1_flag.shape)
        # du lieu dpd dau thang
        BOM_dpd2 = BOM_dpd1.copy()
        BOM_dpd2.columns = map(str.lower, BOM_dpd2.columns)
        BOM_dpd2['bom_dpd'] = BOM_dpd2['bom_dpd'].apply(lambda x: 0 if pd.isnull(x) or x < 0 else x)
        flag_go_ba = dt_req2.merge(BOM_dpd2, on='loan_wid', how='left')
        flag_go_ba = flag_go_ba[flag_go_ba['bom_dpd'].notna()]
        
        flag_go_ba['max_bom_dpd'] = flag_go_ba.groupby('customer_code')['bom_dpd'].transform(np.max)
        #flag_go_ba['max_bom_dpd'] = flag_go_ba.groupby('customer_code')['bom_dpd'].transform(np.max)\
        #.rename(columns={'bom_date_tm': 'date_wid'})
        flag_go_ba = flag_go_ba.rename(columns = {'bom_date_tm':'date_wid'})
        
        flag_go_ba['date_wid'] = flag_go_ba['date_wid'].astype(int)
        
        #print(BOM_dpd2.shape)
        
        # Flag B0
        flag_go = flag_go_ba[flag_go_ba['max_bom_dpd'] == 0]
        # Flag B0+
        flag_ba = flag_go_ba[flag_go_ba['max_bom_dpd'] > 0]
        flag_ba = flag_ba[['customer_code', 'date_wid']]
        flag_ba['risk_group'] = 4
        flag_ba['score'] = 1
        flag_ba['curent_dpd'] = 1.0
        
        flag_ba.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + 'flag_ba.csv'), index=False)
        #print(flag_go_ba.shape)
        
        #du lieu tai san
        ast2 = ast1.copy()
        ast2.columns = map(str.lower, ast2.columns)
        ast2['disburse_year'] = ast2['disburse_year'].astype(int)
        
        
        ast2['asset_year'] = pd.Series(dtype='Int64')
        x = np.arange(1014, 2101)
        for var in x:
            ast2['asset_year'] = np.where(ast2['loan_asset_nm'].str.contains(str(var)), var, ast2['asset_year'])
            
        ast2['asset_year'] = ast2['asset_year'].fillna(ast2['asset_year'].mean()).astype(int)
        ast2['asset_year'] = np.where(ast2['loan_asset_nm'].str.contains('HONDA MSX 125cc Fi 1014'), 2014, ast2['asset_year'])
        ast2['year_in_use'] = ast2['disburse_year'] - ast2['asset_year']
        ast2['year_in_use'] = ast2['year_in_use'].fillna(ast2['year_in_use'].mean())
        ast2['year_in_use_grp'] = pd.cut(ast2['year_in_use'], bins=[-np.inf, 2, 4, 7, np.inf], labels=[1, 2, 3, 0])
        ast2 = ast2[['loan_wid','year_in_use_grp']]
        
        
        print(ast2.shape)
        
        #merge data
        dt_req_fn1 = flag_go.loc[:, ~flag_go.columns.str.contains('dpd')]
        dt_req_fn1 = pd.merge(dt_req_fn1, ast2, on='loan_wid', how='left')
        dt_req_fn1 = pd.merge(dt_req_fn1, flag_col_call_prev, on='loan_wid', how='left')
        dt_req_fn1 = pd.merge(dt_req_fn1, no_promise_sum, on='loan_wid', how='left')
        dt_req_fn1 = pd.merge(dt_req_fn1, b1_flag, on='loan_wid', how='left')
        
        print(ast2.shape)
        
        dt_req_fn1.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_dd_req_raw.csv'), index=False)
        
        
        dt_req_fn1['no_promise_sum'] = dt_req_fn1['no_promise_sum'].apply(lambda x: 0 if pd.isna(x) else x)
        dt_req_fn1['flag_col_call_prev_M'] = dt_req_fn1['flag_col_call_prev_M'].apply(lambda x: 0 if pd.isna(x) else x).astype(int)
        dt_req_fn1['flag_no_promise_ever'] = dt_req_fn1['flag_no_promise_ever'].apply(lambda x: 0 if pd.isna(x) else x).astype(int)
        dt_req_fn1['year_in_use_grp'] = dt_req_fn1['year_in_use_grp'].astype(int)
        dt_req_fn1['cust_age'] = dt_req_fn1['cust_age'].fillna(round(dt_req_fn1['cust_age'].mean())).astype(int)
        
        dt_req_fn1.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_dd_req.csv'), index=False)
        return dt_req_fn1
    else:
      print('Not run')
      return None

    


def main():
    df = ETL(20240502)
    #df = up2db(20230523)
if __name__ == "__main__":
    main()
