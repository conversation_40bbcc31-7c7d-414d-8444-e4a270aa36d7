import pandas as pd
import numpy as np
import re
import datetime
import os, sys



import statsmodels.api as sm
import statsmodels.formula.api as smf
from datetime import datetime
from sklearn import linear_model
from joblib import dump, load
from sklearn.metrics import roc_curve, auc

ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__)))
#sys.path.append(ETL_DIR)
#sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

def score(date_wid):

    # Du lieu Cscore
    date_wid=int(date_wid)
    if pd.to_datetime(date_wid, format='%Y%m%d').day == 2:

        
        def kt(loai_hinh_cu_tru):
            if loai_hinh_cu_tru == 'KT1':
                return 1
            elif loai_hinh_cu_tru == 'KT3':
                return 3
            else:
                return 0
        
        def normalize(x):
            return (x - np.min(x)) / (np.max(x) - np.min(x))
            
            
        
        path = os.path.abspath(__file__)
        path = os.path.dirname(path)
        
        dt_req_fn1=pd.read_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_dd_req.csv'))
        flag_ba =pd.read_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_flag_ba.csv'))
        
        dt_req_fn1['no_promise_sum'] = normalize(dt_req_fn1['no_promise_sum'])
        dt_req_fn1['crn_dpd_10'] = normalize(dt_req_fn1['crn_dpd_10'])
        dt_req_fn1['cust_age'] = normalize(dt_req_fn1['cust_age'])
        dt_req_fn1['loai_hinh_cu_tru'] = dt_req_fn1['loai_hinh_cu_tru'].apply(kt)
        
        
        mod = load(ORIGIN_DIR + '/models/mod.joblib')
        Y_pred = pd.DataFrame(mod.predict(dt_req_fn1), columns=['score'])
        
        
        dt_C = pd.concat([dt_req_fn1,Y_pred], axis=1)
        
        Y_pred.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_predict.csv'), index=False)
        
        dt_C.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_score.csv'), index=False)
        
        def classify(row):
            if (row['flag_col_call_prev_M'] == 0 and row['crn_dpd_10'] == 0 and 
                row['no_promise_sum'] == 0 and row['B1_20_flag'] == 0 and 
                row['score'] <= 0.38):
                return 1
            elif row['score'] <= 0.6:
                return 2
            elif row['score'] <= 0.9:
                return 3
            else:
                return 4
        
        dt_C['risk_group'] = dt_C.apply(classify, axis=1)
        #dt_C.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_C.csv'), index=False)
        dt_C = dt_C[['customer_code','date_wid','risk_group','score','crn_dpd_10']].rename(columns={'crn_dpd_10': 'curent_dpd'})
        
        dt_fn = pd.concat([dt_C, flag_ba])
        
        
        
        risk_group_max = dt_fn.groupby('customer_code')['risk_group'].max().reset_index()
               
        dt_fn = dt_fn.merge(risk_group_max, on='customer_code', suffixes=('', '_max'))
        
          
        dt_fn = dt_fn[dt_fn['risk_group'] == dt_fn['risk_group_max']]\
        .sort_values(['customer_code', 'risk_group', 'score', 'curent_dpd'], ascending=[True, False, False, False])\
        .drop_duplicates('customer_code', keep='first')\
        .drop(columns='risk_group_max')
        
        def result(risk_group_):
            if risk_group_ == 4:
                return 'Rat cao'
            elif risk_group_ == 3:
                return 'Cao'
            elif risk_group_ == 2:
                return 'Trung binh'
            else:
                return 'Thap'
        
        dt_fn['risk_group'] = dt_fn['risk_group'].apply(result)
                
        now = datetime.now()
        dt_fn['DATE_CREATED'] = str(now.strftime("%m/%d/%Y %H:%M"))
        
        
        dt_fn.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_result.csv'), index=False)
    
        return dt_fn
    else:
        print('Not run')
        return None
    
def main():
    df = score(20240502)
#    df = up2db(20230523)
if __name__ == "__main__":
   main()