import pandas as pd
import numpy as np

from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
from airflow.providers.oracle.hooks.oracle import OracleHook
import cx_Oracle

def back_up():
    hook = OracleHook(conn_id= 'oracle_f88_dwh')
    conn = hook.get_connection(conn_id= 'oracle_f88_dwh')
    username = conn.login
    password = conn.password
    host = conn.host
    port = conn.port
    schema = conn.schema
    dsn = cx_Oracle.makedsn(host, port, service_name=schema)
    connection = cx_Oracle.connect(username, password, dsn)
    cursor=connection.cursor()
    sql_str_1 = "truncate table F88DWH.W_EWS_SCORE_BK_F"
    sql_str_2 = "insert into F88DWH.W_EWS_SCORE_BK_F select * from F88DWH.W_EWS_SCORE_F"    
    # read sql - E
    print("Truncate table log")
    cursor.execute(sql_str_1)
    connection.commit()
    print("Insert data")
    cursor.execute(sql_str_2)
    connection.commit()
    
def main():
    back_up()

if __name__ == "__main__":
    main()