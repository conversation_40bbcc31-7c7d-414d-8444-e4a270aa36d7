import pandas as pd
import numpy as np
import re
import datetime
import os, sys


from joblib import load
from sklearn import preprocessing
from xgboost import XGBClassifier
from datetime import datetime

ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__)))
#sys.path.append(ETL_DIR)
#sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

def score(date_wid):
    date_wid=int(date_wid)
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)
    data=pd.read_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + '_dd.csv'))
    
    
    nvar = ['DATE_WID', 'CUSTOMER_CODE', 'LOAN_WID', 'CURRENT_DPD']
    var = ['ASSET_TYPE_WID', 'LTV', 'MOB',
           'LOAN_LOANMAX_RATIOmean', 'LOAN_LOANMAX_RATIOmax', 'LTVmean', 'LTVmin',
           'MADE_YEARmean', 'MOBmin',
           'LOAN_WIDnunique', 'TERM_FREQUENCYmean', 'TERM_FREQUENCY_DAYSsum',
           'ASSET_TYPE_17sum', 'CIMB_FUNDsum', 'INTEREST_RATEmax',
           'ASSET_TYPE_15max', 'DPD', 'MAX_DPD_EVR',
           'TRA_CPV_LOAN_AMT_sum_RATIO']
    svar = ['LOAN_AMTmean', 'LOAN_AMTmax', 'LOAN_AMTsum', 'COLLATERAL_AMTmean', 'COLLATERAL_AMTmax',
            'COLLATERAL_AMTmin', 'COLLATERAL_AMT_ACTIVEmean', 'COLLATERAL_AMT_ACTIVEmax', 'COLLATERAL_AMT_ACTIVEmin', 
            'DISBURSE_AMT_CLOSEDmax', 'DISBURSE_AMTmean', 'TRA_CPV', ]

    # split 15/17
    dd_15 = data.loc[data.ASSET_TYPE_WID == 15]
    dd_17 = data.loc[data.ASSET_TYPE_WID == 17]

    # scaler
    scaler_15 = load(ORIGIN_DIR + '/models/scaler_15.joblib')
    scaler_17 = load(ORIGIN_DIR + '/models/scaler_17.joblib')

    X = pd.concat((
        pd.DataFrame(scaler_15.transform(dd_15[svar]), columns=svar, index=dd_15.index)\
            .merge(dd_15[nvar + var], left_index=True, right_index=True),
            pd.DataFrame(scaler_17.transform(dd_17[svar]), columns=svar, index=dd_17.index)\
                .merge(dd_17[nvar + var], left_index=True, right_index=True)
                ))

    X.fillna(-9, inplace=True)
    X.loc[X.LTV == np.inf, 'LTV'] = X.loc[X.LTV != np.inf, 'LTV'].mean()

    xgb_model = load(ORIGIN_DIR + '/models/model_clf_15.joblib')
    proba = xgb_model.predict_proba(X.loc[:, lambda x: ~x.columns.isin(nvar)])[:,1]

    X = X[nvar].assign(SCORE = proba)
    X.loc[X.CURRENT_DPD > 0, 'SCORE'] = 1.
    
    X = X.groupby(['DATE_WID', 'CUSTOMER_CODE'])[['CURRENT_DPD', 'SCORE']].agg('max').reset_index()

    X['RISK_GROUP'] = pd.cut(X.SCORE, bins=[0, 0.00114, 0.00225, 0.00372, 1], labels=['Thap', 'Trung binh', 'Cao', 'Rat cao'])
    
    now = datetime.now()
    X['DATE_CREATED'] = str(now.strftime("%m/%d/%Y %H:%M"))
        
    X.to_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'), index=False)

    return X
    
#def main():
#    df = score(20231220)
#    df = up2db(20230523)
#if __name__ == "__main__":
#   main()