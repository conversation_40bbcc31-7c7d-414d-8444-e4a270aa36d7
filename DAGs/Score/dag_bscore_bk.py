import airflow
from datetime import timedelta,datetime

from airflow import DAG
from airflow.utils.dates import days_ago
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
import pandas
import numpy as np
from sqlalchemy import create_engine
from configparser import ConfigParser
import sys
import os
from airflow.operators.email import EmailOperator
from airflow.utils.trigger_rule import TriggerRule

ETL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'src/Case/Bscore'))
MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'src/Case/Bscore/models'))
sys.path.append(ETL_DIR)
sys.path.append(MODEL_DIR)

from back_up import back_up

default_args = {
    'owner': 'airflow',    
    'start_date': datetime(2023, 4, 1, 16, 50),
    "retries": 5,
    "retry_delay": timedelta(minutes=2),
}

dag_spark = DAG(
        dag_id = "BSCORE_BACKUP_EDIT",
        default_args=default_args,
        schedule='40 7 * * *',
        dagrun_timeout=timedelta(minutes=60),
        description='use case of sparkoperator in airflow',
        start_date=datetime(2025, 1, 6),
        #tz="Asia/Ho_Chi_Minh",
        catchup=False
)
start = EmptyOperator(task_id='Start')


t1 = PythonOperator(
    task_id='run_etl',
    python_callable=back_up,
    dag=dag_spark,
    op_kwargs={'date_wid':'{{ ds_nodash  }}'},
    trigger_rule=TriggerRule.ALL_SUCCESS
    ,do_xcom_push=False
    #provide_context=True
)




#email = EmailOperator(
#    task_id='send_email_success',
#    to='<EMAIL>',
#    subject='Alert Bscore wworkflow !',
#    html_content=""" <h3>Success!!!</h3> """,
#    trigger_rule=TriggerRule.ALL_SUCCESS,
#    dag=dag_spark
#    ) 
end = EmptyOperator(task_id='End')

start >>  t1 >> end