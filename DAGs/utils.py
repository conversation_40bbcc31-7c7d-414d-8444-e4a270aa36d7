import ast
import json
import logging
import os
import re
from typing import Dict
import requests
from airflow.hooks.base import BaseHook
import cx_Oracle
import datetime
from dateutil.relativedelta import relativedelta
import calendar
import pendulum
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import ListTableMetaData
from airflow.providers.oracle.hooks.oracle import OracleHook
import pandas as pd

DAG_MONITOR = 'F88DWH.W_DAG_MONITOR'
DAG_MONITOR_MAPPING = 'F88DWH.W_DAG_MONITOR_MAPPING'


class DAGMonitor:
    def __init__(self, main_dag_id, dag_id, source_conn_id, destination_conn_id, query,
                 save_folder, file_name, report_name, overwrite, freq, department, type, description, table_metadata,
                 schedule_interval=None, start_date=datetime.datetime(2023, 1, 1), tags=['NO TAGS']):
        self.main_dag_id = main_dag_id
        self.dag_id = dag_id
        self.source_conn_id = source_conn_id
        self.destination_conn_id = destination_conn_id
        self.query = query
        self.save_folder = save_folder
        self.file_name = file_name
        self.report_name = report_name
        self.overwrite = overwrite
        self.freq = freq
        self.department = department
        self.type = type
        self.description = description
        self.table_metadata = ListTableMetaData(list=table_metadata) if table_metadata else None
        self.schedule_interval = schedule_interval
        self.start_date = start_date
        self.tags = tags

    @staticmethod
    def get_db_connection():
        connection_name = 'oracle_f88_dwh'
        oracle_conn = BaseHook.get_connection(connection_name)
        # Extract the connection details
        username = oracle_conn.login
        password = oracle_conn.password
        host = oracle_conn.host
        port = oracle_conn.port
        service_name = oracle_conn.extra_dejson.get('service_name')
        # Construct the connection string
        connection_string = f"{username}/{password}@{host}:{port}/{service_name}"
        # Establish a connection to the Oracle database
        connection = cx_Oracle.connect(connection_string)
        return connection

    @classmethod
    def get_dag_monitor_data(cls, main_dag_id):
        dag_monitor_list = []
        # Establish a connection to the Oracle database
        connection = DAGMonitor.get_db_connection()
        # Create a cursor object to execute SQL statements
        cursor = connection.cursor()

        # Execute a SELECT statement to retrieve data from the table with a CLOB column
        query = f"SELECT * FROM {DAG_MONITOR} WHERE MAIN_DAG_ID = '{main_dag_id}' and STATUS = 1"
        cursor.execute(query)

        # Fetch all rows from the result set
        rows = cursor.fetchall()

        # Iterate over the rows and process the CLOB data
        for row in rows:
            # print(row[1])
            dag_monitor = cls(
                main_dag_id=row[0],
                dag_id=row[1],
                source_conn_id=row[2],
                destination_conn_id=row[3],
                query=row[4].read() if row[4] else None,
                save_folder=row[5],
                file_name=row[6],
                report_name=row[7],
                overwrite=row[8],
                freq=row[9],
                department=row[10],
                type=row[11],
                description=row[13],
                table_metadata=json.loads(row[15].read()) if row[15] else None,
                schedule_interval=row[18],
                start_date=row[19],
                tags=ast.literal_eval(row[20].read()) if row[20] else None
            )
            dag_monitor_list.append(dag_monitor)

        # Close the cursor and the database connection
        cursor.close()
        connection.close()
        return dag_monitor_list

    @staticmethod
    def get_distinct_main_dagid(freq: str, pattern_type: str):
        connection = DAGMonitor.get_db_connection()
        if freq:
            query = f"""SELECT MAIN_DAG_ID
                            FROM (SELECT DISTINCT dm.MAIN_DAG_ID,
                                                  dmm.PRIORITY
                                  FROM {DAG_MONITOR} dm
                                           LEFT JOIN {DAG_MONITOR_MAPPING} dmm ON
                                      dm.MAIN_DAG_ID = dmm.MAIN_DAG_ID
                                  WHERE dm.PATTERN_TYPE = '{pattern_type}'
                                    AND dm.STATUS = 1
                                    AND dm.MAIN_DAG_ID LIKE '%{freq}%'
                                    AND dmm.STATUS = 1
                                  ORDER BY dmm.PRIORITY ASC)"""
            # query = f"SELECT DISTINCT MAIN_DAG_ID FROM {DAG_MONITOR} WHERE PATTERN_TYPE = '{pattern_type}' and STATUS = 1 and MAIN_DAG_ID like '%{freq}%'"
        else:
            query = f"SELECT DISTINCT MAIN_DAG_ID FROM {DAG_MONITOR} WHERE PATTERN_TYPE = '{pattern_type}' and STATUS = 1"
        cursor = connection.cursor()
        cursor.execute(query)

        # Fetch all rows from the result set
        rows = cursor.fetchall()
        cursor.close()
        connection.close()
        return [row[0] for row in rows]

    @staticmethod
    def get_table_metadata(dag_id):
        def convert_query_result_to_metadata(query_result):
            # Giải mã dữ liệu trả về từ query nếu là dạng byte
            json_str = query_result.decode('utf-8')
            metadata_list = json.loads(json_str)
            return metadata_list
        connection = DAGMonitor.get_db_connection()

        query = f"SELECT  TABLE_METADATA,DEPARTMENT,REPORT_NAME,SCHEDULE_INTERVAL FROM {DAG_MONITOR} WHERE DAG_ID = '{dag_id}'"
        cursor = connection.cursor()
        cursor.execute(query)

        # Fetch all rows from the result set
        rows = cursor.fetchall()
        if rows:
            result = []
            department = None
            report_name = None
            schedule_interval = None
            # Duyệt qua từng dòng dữ liệu
            for row in rows:
                if row[0] is not None:  # Nếu có dữ liệu trong cột đầu tiên
                    if isinstance(row[0], cx_Oracle.LOB):  # Nếu là CLOB
                        # Đọc dữ liệu từ CLOB và giải mã thành utf-8
                        result.append(row[0].read().encode('utf-8'))
                    else:
                        # Nếu không phải CLOB, giữ nguyên giá trị
                        result.append(row[0])
                department = row[1]
                report_name = row[2]
                schedule_interval = row[3]
            # Giả sử dữ liệu trả về là một chuỗi JSON, ta chuyển nó thành đối tượng Python
            metadata = result[0]  # Nếu bạn chỉ lấy dòng đầu tiên (có thể thay đổi tùy nhu cầu)
            formatted_metadata = convert_query_result_to_metadata(metadata)  # Chuyển đổi dữ liệu

            return formatted_metadata,department,report_name,schedule_interval

        else:
            # Nếu không có dữ liệu, trả về một danh sách rỗng hoặc danh sách từ cột đầu tiên của các dòng
            return None, None, None, None

    def set_params(logical_date: datetime.datetime):
        tz=pendulum.timezone('Asia/Ho_Chi_Minh')
        logical_date = logical_date.astimezone(tz)
        if logical_date.date() == pendulum.now(tz=tz).date():
            yesterday = logical_date - datetime.timedelta(1)
        else:
            yesterday = logical_date
        today = yesterday + datetime.timedelta(1)
        day_before_yesterday = yesterday - datetime.timedelta(1)
        first_day_of_month = yesterday.replace(day=1)
        first_day_last_month = (first_day_of_month - relativedelta(months=1))
        yesterday_formatted = yesterday.strftime("%Y%m%d")
        year_num, month_num = yesterday_formatted[:4], yesterday_formatted[4:6]
        last_day_of_month = yesterday.replace(day=calendar.monthrange(yesterday.year, yesterday.month)[1])
        last_day_of_last_month = (first_day_of_month - datetime.timedelta(days=1))
        year_month_last_month = (yesterday - relativedelta(months=1)).strftime("%Y%m%d")[:6]
        # If in January (tháng 1) then get first day of last year
        if yesterday.month == 1:
            first_day_of_year = datetime.date(yesterday.year - 1, 1, 1).strftime('%Y%m%d')
        else:
            first_day_of_year = datetime.date(yesterday.year, 1, 1).strftime('%Y%m%d')

        params = {}
        params.update({'TODAY': today.strftime("%Y%m%d")})
        params.update({'FIRST_DAY_OF_YEAR': first_day_of_year})
        params.update({"YESTERDAY": yesterday_formatted})
        params.update({"DAY_BEFORE_YESTERDAY": day_before_yesterday.strftime("%Y%m%d")})
        params.update({"FIRST_DAY_OF_MONTH": first_day_of_month.strftime("%Y%m%d")})
        params.update({"FIRST_DAY_LAST_MONTH": first_day_last_month.strftime("%Y%m%d")})
        params.update({"LAST_DAY_OF_MONTH": last_day_of_month.strftime("%Y%m%d")})
        params.update({"LAST_DAY_OF_LAST_MONTH": last_day_of_last_month.strftime("%Y%m%d")})
        params.update({"YEAR_NUM": year_num})
        params.update({"MONTH_NUM": month_num})
        params.update({"LAST_YEAR_MONTH": year_month_last_month})
        return params

    @staticmethod
    def set_params_operator(**context):
        if bool(context['dag_run'].is_backfill):
            logging.info("Back fill mode: %s", str(context['dag_run'].is_backfill))
            return DAGMonitor.set_params(context['dag_run'].logical_date)
        else:
            if not context['dag_run'].conf:
                logging.info("=======Without Config =========")
                return DAGMonitor.set_params(context['dag_run'].logical_date)
            elif context['dag_run'].conf:
                if 'set_params_task' not in context['dag_run'].conf:
                    logging.info("======= Config =========")
                    return context['dag_run'].conf
                else:
                    set_params_task = ast.literal_eval(
                        context['dag_run'].conf.get('set_params_task'))  # Convert to true format datatype
                    if set_params_task:
                        logging.info("=======set_params_task =========")
                        return set_params_task
                    elif not set_params_task:
                        logging.info("=======Without Config =========")
                        return DAGMonitor.set_params(context['dag_run'].logical_date)

    @staticmethod
    def sql_get_filtered_params(parameters: Dict[str, str | int], sql: str) -> Dict[str, str | int]:
        parameter_names = re.findall(r":(\w+)", sql)
        parameter_names = [param.upper() for param in parameter_names]
        filtered_parameters = {param: parameters[param] for param in parameter_names if
                               param in parameters}
        return filtered_parameters


def set_kwargs(parent_dir: str, file_name: str):
    query_file = os.path.join(parent_dir, 'queries', f'{file_name}.sql')
    saved_folder = os.path.join(parent_dir, 'Downloaded')
    sql = open(query_file, "r", encoding='utf-8').read()
    return sql, saved_folder


def send_msg(message, config, receiver, receiver_type: str):
    RECEIVER_TYPE_LIST = ['thread_id', 'partner_id']
    if receiver_type not in RECEIVER_TYPE_LIST:
        raise ValueError(f'receiver_type is not in receiver_type list: {RECEIVER_TYPE_LIST}')
    else:
        try:
            headers = {
                "content-type": "application/json",
                "x-gapo-openapi-key": config['gapo_token']
            }
            query = {
                "bot_token": config['bot_token']
            }
            payload = {
                "bot_id": config['bot_id'],
                "body": {
                    "type": "text",
                    "text": message,
                    "is_markdown_text": True
                },
                "receiver_id": str(receiver)
            }
            response = requests.post(config['url'], params=query, json=payload, headers=headers)
            data = response.json()
        except Exception as e:
            print(e)


def send_message_bot_bi(message, config, receiver, receiver_type: str):
    url = "https://api.gapowork.vn/3rd-bot/v1.0/actions/messages"
    # Create message content:
    RECEIVER_TYPE_LIST = ['thread_id', 'partner_id']
    if receiver_type not in RECEIVER_TYPE_LIST:
        raise ValueError(f'receiver_type is not in receiver_type list: {RECEIVER_TYPE_LIST}')
    else:
        payload = {
            f"{receiver_type}": receiver,
            "message": {
                "type": "text",
                "text": message
            }
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": config['bot_token']
        }
        response = requests.post(url, json=payload, headers=headers)
        data = response.json()
        return data


def df_to_oracle(table_name: str, oracle_conn_id: str, df: pd.DataFrame):
    if not df.empty:
        oracle_hook = OracleHook(oracle_conn_id)
        rows = df.itertuples(index=False)
        oracle_hook.bulk_insert_rows(table=table_name, target_fields=df.columns.tolist(), commit_every=5000, rows=rows)

#Kienpv --> nhung column khong co trong database thi drop columns dataframe
def write_df_to_oracle(table_name: str, oracle_conn_id: str, df: pd.DataFrame):
    if not df.empty:
        oracle_hook = OracleHook(oracle_conn_id)
        conn = oracle_hook.get_conn()
        cursor = conn.cursor()
        # Lấy danh sách cột trong bảng database
        # query = f"SELECT * FROM {table_name} WHERE 1=0"
        if '.' in table_name:
            schema, table = table_name.split('.')
        table = table.strip().upper()
        schema = schema.strip().upper()
        query = f"SELECT column_name, data_type FROM all_tab_columns WHERE table_name = '{table}' and owner = '{schema}'"
        cursor.execute(query)
        columns_info = cursor.fetchall()
        # Tạo danh sách các cột không phải CLOB và CLOB
        non_clob_columns = [col[0] for col in columns_info if col[1] != 'CLOB']
        clob_columns = [col[0] for col in columns_info if col[1] == 'CLOB']

        # Lấy danh sách tên cột
        # table_columns = [desc[0].upper() for desc in cursor.description]
        # common_columns = [col for col in df.columns if col.upper() in table_columns]
        table_columns = []
        # Thêm các cột không phải CLOB vào bảng
        for col in non_clob_columns:
            if col not in table_columns:
                table_columns.append(col)

        # Thêm các cột CLOB vào bảng
        for col in clob_columns:
            if col not in table_columns:
                table_columns.append(col)

            # Tạo danh sách cột chung giữa DataFrame và bảng
        df.columns = df.columns.str.upper()
        

        # Xử lý các cột thiếu
        missing_columns = [col for col in table_columns if col.upper() not in df.columns]

        # Thêm các cột thiếu vào DataFrame với giá trị mặc định là ''
        for col in missing_columns:
            df[col] = ''
        # Lọc các cột của DataFrame chỉ giữ những cột có trong database
        common_columns = [col for col in table_columns if col.upper() in df.columns]
        df = df.loc[:, ~df.columns.duplicated()]
        df_filtered = df[common_columns]

        if not common_columns:
            print(f"Không có cột nào trong DataFrame trùng với bảng {table_name}. Dừng chèn.")
            return
        print(df_filtered.head())
        # Thay thế NaN/NaT cho tất cả các cột tự động
        if isinstance(df_filtered, pd.DataFrame):
            for column in df_filtered.columns:
                series = df_filtered[column]

                if pd.api.types.is_object_dtype(series):
                    df_filtered[column] = series.fillna('')
                elif pd.api.types.is_numeric_dtype(series):
                    df_filtered[column] = series.fillna(0)
                elif pd.api.types.is_datetime64_any_dtype(series):
                    df_filtered[column] = series.fillna(pd.Timestamp('1900-01-01'))
        else:
            print("df_filtered không phải là DataFrame")
        # Tạo DataFrame mới chỉ với các cột hợp lệ
        # chuyen toan bo thanh chuoi
        df_filtered = df_filtered.fillna('')  # Thay NaN bằng chuỗi rỗng
        df_filtered = df_filtered.astype(str)  # Chuyển toàn bộ DataFrame sang chuỗi

        print(df_filtered.dtypes)
        logging.info(f"df_filtered:{df_filtered}")
        column_list = ', '.join(common_columns)
        print(f"noi chuoi cot:{column_list}")
        value_placeholders = ', '.join([f':{i + 1}' for i in range(len(common_columns))])
        insert_query = f"""
                      INSERT INTO {table_name} ({column_list}) VALUES ({value_placeholders}) """
        # Chuyển DataFrame thành danh sách tuple
        data_to_insert = [tuple(row) for row in df_filtered.to_numpy()]
        print(insert_query)
        chunk_size = 5000  # Chèn từng 1000 dòng một
        try:
            for i in range(0, len(data_to_insert), chunk_size):
                chunk = data_to_insert[i:i + chunk_size]
                cursor.executemany(insert_query, chunk)  # Chèn dữ liệu
                conn.commit()  # Lưu thay đổi
                print(f"Đã chèn {len(chunk)} dòng vào bảng {table_name} thành công!")
        except cx_Oracle.Error as e:
            print(f"Lỗi khi chèn dữ liệu: {e}")
            conn.rollback()  # Rollback nếu có lỗi

def truncate_stg_tbl(tbl_name, conn_id):
    logging.info(f'Truncate table {tbl_name} begin!')
    hook = OracleHook(oracle_conn_id=conn_id)
    sql = f"truncate table {tbl_name}"
    hook.run(sql, autocommit=True)
    logging.info(f'delete {tbl_name} Done!')
    logging.info(f'Truncate table {tbl_name} Done!')


def merge_data_stag_to_dwh(conn_id, proc_name):
    logging.info(f'MERGE STG -> DWH begin!')
    logging.info(f'EXECUTE {proc_name}')
    sql_merge = f"BEGIN {proc_name}; END;"
    logging.info(f'sql_merge {sql_merge}')
    hook = OracleHook(oracle_conn_id=conn_id)
    hook.run(sql_merge, autocommit=True)
    logging.info(f'MERGE STG -> DWH Done!')
