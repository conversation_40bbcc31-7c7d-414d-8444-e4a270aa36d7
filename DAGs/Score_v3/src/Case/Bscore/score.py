import logging

from joblib import load
import pandas as pd
import re
import csv
import os, sys
from datetime import datetime,timedelta
import numpy as np

# datetime object containing current date and time
now = datetime.now()
 
# dd/mm/YY H:M:S
dt_string = now.strftime("%Y/%m/%d %H:%M:%S")



ETL_DIR = os.path.abspath(os.path.dirname(__file__))
MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ''))
sys.path.append(ETL_DIR)
sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)



"""
Function này tính ra điểm tín dụng cho KH
data: bảng dữ liệu là kết quả từ function etl đã tính
return: bảng dữ liệu bao gồm DATE_WID, CUSTOMER_CODE, và SCORE
"""
# parameters
fill_na = {
    'CLOSED_CNT_CONTRACT': 0,
    'CLOSED_ASSET_TYPE_17': -99,
    'CLOSED_ASSET_TYPE_15': -99,
    'CLOSED_F88_FUND': -99,
    'CLOSED_CIMB_FUND': -99,
    'CLOSED_MAX_LTV': -99,
    'CLOSED_LTV': -99,
    'CLOSED_RATIO_DIS_MAX_AMT': -99,
    'CLOSED_REAL_MONTH_PCT': -99,
    'TRA_CPV': 0,
    'MIEN_GIAM_CPV_AMT': 0,
    'MIEN_GIAM_PHAT_AMT': 0,
    'MIEN_GIAM_PHI_AMT': 0,
    'MIEN_GIAM_CPV_CNT': 0,
    'MIEN_GIAM_PHAT_CNT': 0,
    'MIEN_GIAM_PHI_CNT': 0,
    }
# variables
xm_active_final_list = [
    'CNT_CONTRACT', 'MOB_1', 'F88_FUND', 'MAX_LTV', 'LTV', 'RATIO_MOB_TERM', 'CLOSED_CNT_CONTRACT',
    'CLOSED_LTV', 'CLOSED_REAL_MONTH_PCT', 'MIEN_GIAM_PHAT_CNT', 'MAX_CUR_DPD', 'RATIO_TRA_CPV', 'RATIO_TRA_CPV_2',
]
ot_active_final_list = [
    'CNT_CONTRACT', 'MOB_1', 'MOB_2', 'MOB_3', 'F88_FUND', 'MAX_LTV', 'LTV', 'RATIO_MOB_TERM', 'CLOSED_CNT_CONTRACT',
    'CLOSED_LTV', 'MIEN_GIAM_CPV_CNT', 'MIEN_GIAM_PHAT_CNT', 'MIEN_GIAM_PHI_CNT', 'MAX_CUR_DPD', 'MAX_DPD_EVR',
    'RATIO_TRA_CPV', 'RATIO_TRA_CPV_2', 'RATIO_INTR_PRIN', 'CLOSED_REAL_MONTH_PCT',
]
xm_inactive_final_list = [
    'CLOSED_CNT_CONTRACT', 'CLOSED_F88_FUND', 'CLOSED_CIMB_FUND', 'CLOSED_MAX_LTV', 'CLOSED_LTV', 'CLOSED_REAL_MONTH_PCT',
    'MAX_DPD_EVR',
]
ot_inactive_final_list = [
    'CLOSED_CNT_CONTRACT', 'CLOSED_ASSET_TYPE_17', 'CLOSED_F88_FUND', 'CLOSED_CIMB_FUND', 'CLOSED_MAX_LTV', 'CLOSED_LTV', 'CLOSED_REAL_MONTH_PCT',
    'MAX_DPD_EVR',
]

# load models

xgb_model_active_XM = load(ORIGIN_DIR + '/models/model_clf_XM.joblib')
xgb_model_active_OT = load(ORIGIN_DIR + '/models/model_clf_OTO.joblib')
xgb_model_inactive_XM = load(ORIGIN_DIR + '/models/model_clf_inactive_XM.joblib')
xgb_model_inactive_OT = load(ORIGIN_DIR + '/models/model_clf_inactive_OTO.joblib')

# bin
b_bin_xm_active = [-np.inf, .06, 0.14, 0.52, 0.86,  np.inf]
b_bin_xm_inactive = [-np.inf, .01, 0.2, 0.55, 0.88,  np.inf]
b_bin_ot_active = [-np.inf, 0.01, 0.15, 0.55, 0.92,  np.inf]
b_bin_ot_inactive = [-np.inf, 0.05, 0.2, 0.55, 0.92,  np.inf]
def score():
    date_wid =  (datetime.today()-timedelta(days = 1)).strftime("%Y%m%d")
    print(date_wid)
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)

    logging.info(f"Start check load ram-cpu ")
    data=pd.read_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + 'dd_0.csv'))
    print(data.shape)
    print(data.head())
    print(data.dtypes)
    # Hợp đồng đang active
    dd_active = data.loc[lambda x: ~pd.isna(x.CNT_CONTRACT)]
    dd_active.fillna(fill_na, inplace=True)
    dd_active = dd_active.loc[lambda x: ~pd.isna(x.MAX_CUR_DPD) & ~pd.isna(x.MAX_DPD_EVR)]
    assert pd.isna(dd_active).sum().sum() == 0
    print(f'run score: dd_active')

    print(f'count dd_active: {dd_active.sum()}')
    # XM active
    dd_active_XM = dd_active.loc[lambda x: x.ASSET_TYPE_15 != 1]
    logging.info(f"Start check ram-cpu read  dd_active_XM")
    dd_active_XM = dd_active_XM \
        .assign(
        RATIO_TRA_CPV=lambda x: x.TRA_CPV / x.TOTAL_OBLIGATION_AMT,
        RATIO_TRA_CPV_2=lambda x: x.TRA_CPV / x.TOTAL_INTEREST_AMT,
    ) \
        [['DATE_WID', 'CUSTOMER_CODE'] + xm_active_final_list + ['PACKAGE_GROUP']]
    print(f'start dd_active_XM')
    dd_active_XM = dd_active_XM[['DATE_WID', 'CUSTOMER_CODE', 'MAX_CUR_DPD', 'PACKAGE_GROUP']] \
        .assign(PROD=xgb_model_active_XM.predict_proba(
        dd_active_XM.drop(['DATE_WID', 'CUSTOMER_CODE'], axis=1)[xm_active_final_list])[:, 1])
    print(f'End model for dd_active_XM')

    dd_active_XM.loc[lambda x: x.PACKAGE_GROUP == 2, 'PROD'] = dd_active_XM.loc[
                                                                   lambda x: x.PACKAGE_GROUP == 2, 'PROD'] * 2
    dd_active_XM.loc[lambda x: x.PACKAGE_GROUP == 3, 'PROD'] = dd_active_XM.loc[
                                                                   lambda x: x.PACKAGE_GROUP == 3, 'PROD'] * 4
    dd_active_XM.loc[lambda x: (x.MAX_CUR_DPD >= 10) & (x.PROD < .9), 'PROD'] = np.float32(.9)
    dd_active_XM.loc[lambda x: (x.MAX_CUR_DPD >= 5) & (x.PROD < .8), 'PROD'] = np.float32(.8)
    dd_active_XM.loc[lambda x: (x.PROD > 1), 'PROD'] = np.float32(1)
    dd_active_XM.drop('PACKAGE_GROUP', axis=1, inplace=True)
    print(f'run score: dd_active_XM')

    # OT active
    dd_active_OT = dd_active.loc[lambda x: x.ASSET_TYPE_15 == 1]
    dd_active_OT = dd_active_OT \
        .assign(
        RATIO_TRA_CPV=lambda x: x.TRA_CPV / x.TOTAL_OBLIGATION_AMT,
        RATIO_TRA_CPV_2=lambda x: x.TRA_CPV / x.TOTAL_INTEREST_AMT,
        RATIO_INTR_PRIN=lambda x: x.TOTAL_INTEREST_AMT / x.TOTAL_PRINCIPAL_AMT
    ) \
        [['DATE_WID', 'CUSTOMER_CODE'] + ot_active_final_list]

    dd_active_OT = dd_active_OT[['DATE_WID', 'CUSTOMER_CODE', 'MAX_CUR_DPD']] \
        .assign(PROD=xgb_model_active_OT.predict_proba(
        dd_active_OT.drop(['DATE_WID', 'CUSTOMER_CODE'], axis=1)[ot_active_final_list])[:, 1])
    dd_active_OT.loc[lambda x: (x.MAX_CUR_DPD >= 10) & (x.PROD < .9), 'PROD'] = np.float32(.9)
    dd_active_OT.loc[lambda x: (x.MAX_CUR_DPD >= 5) & (x.PROD < .8), 'PROD'] = np.float32(.8)
    dd_active_OT.loc[lambda x: (x.PROD > 1), 'PROD'] = np.float32(1)
    print(f'run score: dd_active_OT')

    # Hợp đồng đang inactive
    dd_inactive = data.loc[lambda x: pd.isna(x.CNT_CONTRACT)]
    dd_inactive = dd_inactive.loc[lambda x: ~pd.isna(x.CLOSED_CNT_CONTRACT)]
    dd_inactive.fillna(
        {
            'MAX_DPD_EVR': -99,
        }, inplace=True)
    print(f'run score: dd_inactive')

    # XM inactive
    dd_inactive_XM = dd_inactive \
        .loc[lambda x: x.CLOSED_ASSET_TYPE_15 != 1]

    dd_inactive_XM = dd_inactive_XM[['DATE_WID', 'CUSTOMER_CODE']] \
        .assign(PROD=xgb_model_inactive_XM.predict_proba(
        dd_inactive_XM.drop(['DATE_WID', 'CUSTOMER_CODE'], axis=1)[xm_inactive_final_list])[:, 1])

    # OT inactive
    dd_inactive_OT = dd_inactive \
        .loc[lambda x: x.CLOSED_ASSET_TYPE_15 == 1]
    dd_inactive_OT = dd_inactive_OT[['DATE_WID', 'CUSTOMER_CODE']] \
        .assign(PROD=xgb_model_inactive_OT.predict_proba(
        dd_inactive_OT.drop(['DATE_WID', 'CUSTOMER_CODE'], axis=1)[ot_inactive_final_list])[:, 1])
    print(f'run score: inactive')

    # xm active
    dd_active_XM = dd_active_XM \
        .assign(RANK=lambda x: pd.cut(x.PROD, bins=b_bin_xm_active, labels=['A', 'B', 'C', 'D', 'E']).astype('object'),
                BSCORE=lambda x: (x.RANK == 'A') * 650 + (x.RANK == 'B') * 550 + (x.RANK.isin(['C', 'D'])) * 450 + (
                    x.RANK.isin(['E'])) * 350,
                ASSET_TYPE_15MAX=0) \
        .rename(columns={"MAX_CUR_DPD": "DPD", "PROD": "SCORE"})
    # xm inactive
    dd_inactive_XM = dd_inactive_XM \
        .assign(
        RANK=lambda x: pd.cut(x.PROD, bins=b_bin_xm_inactive, labels=['A', 'B', 'C', 'D', 'E']).astype('object'),
        BSCORE=lambda x: (x.RANK == 'A') * 650 + (x.RANK == 'B') * 550 + (x.RANK.isin(['C', 'D'])) * 450 + (
            x.RANK.isin(['E'])) * 350,
        ASSET_TYPE_15MAX=0) \
        .rename(columns={"MAX_CUR_DPD": "DPD", "PROD": "SCORE"})
    # ot active
    dd_active_OT = dd_active_OT \
        .assign(RANK=lambda x: pd.cut(x.PROD, bins=b_bin_ot_active, labels=['A', 'B', 'C', 'D', 'E']).astype('object'),
                BSCORE=lambda x: (x.RANK == 'A') * 650 + (x.RANK == 'B') * 550 + (x.RANK.isin(['C', 'D'])) * 450 + (
                    x.RANK.isin(['E'])) * 350,
                ASSET_TYPE_15MAX=1) \
        .rename(columns={"MAX_CUR_DPD": "DPD", "PROD": "SCORE"})
    # ot inactive
    dd_inactive_OT = dd_inactive_OT \
        .assign(
        RANK=lambda x: pd.cut(x.PROD, bins=b_bin_ot_inactive, labels=['A', 'B', 'C', 'D', 'E']).astype('object'),
        BSCORE=lambda x: (x.RANK == 'A') * 650 + (x.RANK == 'B') * 550 + (x.RANK.isin(['C', 'D'])) * 450 + (
            x.RANK.isin(['E'])) * 350,
        ASSET_TYPE_15MAX=1) \
        .rename(columns={"MAX_CUR_DPD": "DPD", "PROD": "SCORE"})
    print(f'run score: active')

    
    pd.concat((dd_active_XM, dd_inactive_XM, dd_active_OT, dd_inactive_OT)).to_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'), index=False)
    try:
        print("Start delete file score")
        file_path = os.path.join(ORIGIN_DIR, 'Data/' + str(date_wid) + 'dd_0.csv')

        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Đã xóa: {file_path}")
        else:
            print(f"Không tìm thấy file: {file_path}")
    except:
        print("Error: Can not delete file")
    return pd.concat((dd_active_XM, dd_inactive_XM, dd_active_OT, dd_inactive_OT))



if __name__ == "__main__":
    score()