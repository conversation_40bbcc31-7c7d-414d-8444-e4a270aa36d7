import pandas as pd
import numpy as np

from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
from airflow.providers.oracle.hooks.oracle import OracleHook
import re
import datetime
import os, sys


ETL_DIR = os.path.abspath(os.path.dirname(__file__))
MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'Bscore/models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ''))
sys.path.append(ETL_DIR)
sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

from score import score
"""
Function này sẽ kết nối vào F88DWH và truy xuất (extract) dữ liệu cần để tính toán B-Score v3, tiếp theo nó sẽ biến đổi (transform) các biến thành các biến theo kỳ vọng, cuối cùng nó sẽ tải (load) dữ liệu ra bảng để phục vụ mục đích tính toán điểm B-Score v3
date_wid: là ngày chạy dữ liệu, định dạng yyyymmdd
connection: là kết nối đến F88DWH
return: là bảng dữ liệu làm đầu vào cho function score
"""
xm_package_group = pd.DataFrame(
    {
        'PACKAGE_NM': ['CIMB_ DKXM_Vienchuc_VP', 'CIMB_DKXM_Vienchuc_VP', 'F88_DKXM_Vienchuc_VP', 'CIMB_DKXM_Kinhdoanhonline', 'F88_DKXM_Kinhdoanhonline', 'DKXM_đảo nợ', 'DKXM_Tiêu dùng_Hải Phòng', 'DKXM_Tieudung', 'DKXM_Tieudung_3M', 'DKXM_Tieudung_5M', 'DKXM_Tieudung_Hải Phòng', 'F88_DKXM_GOCCUOIKY', 'F88_ĐKXM_GOCCUOIKY', 'F88_DKXM_VAYTHEM', 'F88_SPXM_24T_2024', 'F88_XM_PHUNUKD', 'Online lending - App vay', 'CIMB_DKXM_Congnhan', 'F88_DKXM_Congnhan', 'CIMB_DKXM_Laodongtudo', 'CIMB_DKXM_Laodongtudo_N1/2', 'CIMB_DKXM_Laodongtudo_N3/4/5', 'F88_DKXM_Laodongtudo', 'F88_DKXM_Laodongtudo_N1/2', 'F88_DKXM_Laodongtudo_N3/4/5', 'CIMB_DKXM_miencavet', 'CIMB_DKXM_Taixecongnghe', 'F88_DKXM_Taixecongnghe', 'CIMB_DKXM_Tieuthuong', 'F88_DKXM_Tieuthuong', 'DKXM_Vay tiền mặt mua hàng_MWG', 'App vay_MB', 'DKXM_Direct Sale', 'DKXM_FMCG', 'F88_DKXM_E2E', 'F88_ĐKXM_HOTRO'],
        'PACKAGE_GROUP': [1]*17+[2]*14+[3]*5
    }
)

def ETL(date_wid,conn_id='oracle_f88_dwh'):
    date_wid=int(date_wid)
    print(date_wid)
    oracle_hook = OracleHook(oracle_conn_id=conn_id)
    connection = oracle_hook.get_conn()
    print("1")
    sql_str_1 = "SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, B.LOAN_WID, DISBURSE_DATE_WID, \
                    CASE WHEN D.ASSET_TP_CODE IN ('00000007', '00000015') THEN 15 \
                    WHEN D.ASSET_TP_CODE IN ('00000013', '00000017') THEN 17 \
                    ELSE NULL END ASSET_TYPE_WID, NVL(B.FUND_NAME, 'F88 Fund') FUND_NAME, B.COLLATERAL_AMT, \
                    B.DISBURSE_AMT, B.MAX_LENDABLE_AMT, CASE WHEN TERM_FREQUENCY_UNIT = 'Days' THEN ROUND(TERM_FREQUENCY / 30, 1) ELSE TERM_FREQUENCY END TERM_FREQUENCY, \
                    CASE WHEN TERM_FREQUENCY_UNIT = 'Days' THEN 1 ELSE 0 END TERM_FREQUENCY_DAYS, NVL(LOAN_PKG_NM, E.PACKAGE_NM) PACKAGE_NM \
                    FROM F88DWH.W_LOAN_DTL_F B \
                    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID \
                    LEFT JOIN F88DWH.W_ASSET_TP_D D ON D.ASSET_TP_WID = B.ASSET_TYPE_WID \
                    LEFT JOIN f88dwh.W_PACKAGE_D E ON B.PACKAGE_WID = E.PACKAGE_WID \
                    WHERE B.DISBURSE_DATE_WID <= {date_wid} AND \
                    (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) AND \
                    NVL(B.MAX_LENDABLE_AMT, 0) != 0 AND NVL(B.COLLATERAL_AMT, 0) != 0 \
                   ".format(date_wid=date_wid)
    sql_str_2 = "SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, B.LOAN_WID, DISBURSE_DATE_WID, \
                    B.CLOSED_DATE_WID, CASE WHEN D.ASSET_TP_CODE IN ('00000007', '00000015') THEN 15 \
                   WHEN D.ASSET_TP_CODE IN ('00000013', '00000017') THEN 17 ELSE NULL END ASSET_TYPE_WID, NVL(B.FUND_NAME, 'F88 Fund') FUND_NAME, \
                   B.COLLATERAL_AMT, B.DISBURSE_AMT, B.MAX_LENDABLE_AMT, TERM_FREQUENCY \
                   FROM F88DWH.W_LOAN_DTL_F B \
                   LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID \
                   LEFT JOIN F88DWH.W_ASSET_TP_D D ON D.ASSET_TP_WID = B.ASSET_TYPE_WID \
                   WHERE DISBURSE_DATE_WID <= {date_wid} \
                   AND (B.CLOSED_DATE_WID IS NOT NULL AND B.CLOSED_DATE_WID <= {date_wid}) \
                   AND NVL(B.MAX_LENDABLE_AMT, 0) != 0 AND NVL(B.COLLATERAL_AMT, 0) != 0 \
                   ".format(date_wid=date_wid)
    sql_str_3a = "SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, TRANSACTION_WID, TRANS_AMT TRA_CPV \
                    FROM F88DWH.W_LOAN_TRANS_DTL_F A \
                    LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID \
                    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID \
                    WHERE DISBURSE_DATE_WID <= {date_wid} AND TRANS_DATE_WID <= {date_wid} AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) AND \
                    ACTION_CODE IN ('TRA_CPV_CIMB', 'TRA_CPV')AND DEACTIVE_FLAG = 0 \
                   ".format(date_wid=date_wid)



    sql_str_3b = "SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, TRANS_DATE_WID, TRANSACTION_WID, ACTION_CODE, TRANS_AMT \
                    FROM F88DWH.W_LOAN_TRANS_DTL_F A \
                    LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID \
                    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID \
                    WHERE DISBURSE_DATE_WID <= {date_wid} AND TRANS_DATE_WID <= {date_wid} AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) \
                    AND ACTION_CODE LIKE '%MIEN_GIAM%'AND DEACTIVE_FLAG = 0 \
                   ".format(date_wid=date_wid)

    sql_str_4 = "SELECT DISTINCT DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, NVL(B.OVERDUE_DAYS, B.OVERDUE_DAYS_ADJUST) DPD \
                    FROM F88DWH.W_LOAN_DTL_F A \
                    LEFT JOIN F88DWH.W_LOAN_DAILY_F B ON A.LOAN_WID = B.LOAN_WID \
                    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON A.CUSTOMER_WID = C.CUSTOMER_WID \
                    WHERE B.YEAR_NUM = ROUND({date_wid} / 10000, 0) AND B.MONTH_NUM = ROUND({date_wid} / 100, 0) - ROUND({date_wid} / 10000, 0) * 100 \
                    AND B.DATE_WID = {date_wid} \
                    AND A.DISBURSE_DATE_WID <= {date_wid} AND (A.CLOSED_DATE_WID IS NULL OR A.CLOSED_DATE_WID > {date_wid}) \
                   ".format(date_wid=date_wid)

    sql_str_5 = "SELECT DISTINCT {date_wid} DATE_WID, A.CUSTOMER_CODE, MAX(A.MAX_DPD_EVR) MAX_DPD_EVR \
                    FROM F88DWH.W_CUSTOMER_MAX_DPD_12MONTHS A \
                    GROUP BY A.CUSTOMER_CODE".format(date_wid=date_wid)
    # WHERE A.THANG <= {date_wid} \
    sql_str_6 = "SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, SUM(NVL(A.PRINCIPAL_AMT, 0)) TOTAL_PRINCIPAL_AMT, \
                    SUM(NVL(A.INTEREST_AMT, 0)) + SUM(NVL(A.INTEREST_VAT, 0)) TOTAL_INTEREST_AMT, \
                    SUM(NVL(A.PRINCIPAL_AMT, 0)) + SUM(NVL(A.INTEREST_AMT, 0)) + SUM(NVL(A.INTEREST_VAT, 0)) TOTAL_OBLIGATION_AMT \
                    FROM F88DWH.W_LOAN_SCH_DTL_F A \
                    LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID \
                    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID \
                    WHERE B.DISBURSE_DATE_WID <= {date_wid} \
                    AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) \
                    GROUP BY C.CUSTOMER_CODE, A.LOAN_WID \
                    ".format(date_wid=date_wid)
    
    # read sql - E
    dd_1 = pd.read_sql(sql_str_1, connection)
    dd_2 = pd.read_sql(sql_str_2, connection)
    dd_3a = pd.read_sql(sql_str_3a, connection)
    dd_3b = pd.read_sql(sql_str_3b, connection)
    dd_4 = pd.read_sql(sql_str_4, connection)
    dd_5 = pd.read_sql(sql_str_5, connection)
    dd_6 = pd.read_sql(sql_str_6, connection)
    # transform - T
    # all customer
    dd_0 = dd_1[['DATE_WID', 'CUSTOMER_CODE']]\
        .merge(dd_2[['DATE_WID', 'CUSTOMER_CODE']], how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .drop_duplicates()

    # handle dd_1 active_info
    dd_1 = dd_1\
        .loc[lambda x: x.ASSET_TYPE_WID.isin([13, 17, 7, 15])]\
        .merge(xm_package_group, how='left', on='PACKAGE_NM')\
        .fillna({'PACKAGE_GROUP': -1})\
        .assign(DATE_WID_DT = lambda x: pd.to_datetime(x.DATE_WID, format='%Y%m%d'),
                DISBURSE_DATE_WID_DT = lambda x: pd.to_datetime(x.DISBURSE_DATE_WID, format='%Y%m%d'),
                MOB = lambda x: (x.DATE_WID_DT.dt.year - x.DISBURSE_DATE_WID_DT.dt.year)*12 +
                        (x.DATE_WID_DT.dt.month - x.DISBURSE_DATE_WID_DT.dt.month),
                ASSET_TYPE_17 = lambda x: (x.ASSET_TYPE_WID.isin([13, 17])).astype(int),
                ASSET_TYPE_15 = lambda x: (x.ASSET_TYPE_WID.isin([7, 15])).astype(int),
                F88_FUND = lambda x: (x.FUND_NAME == 'F88 Fund').astype(int),
                CIMB_FUND = lambda x: (x.FUND_NAME == 'CIMB Fund').astype(int),
            )\
        .groupby(['DATE_WID', 'CUSTOMER_CODE'])[['LOAN_WID', 'COLLATERAL_AMT', 'DISBURSE_AMT', 'MAX_LENDABLE_AMT', 'TERM_FREQUENCY', 'MOB', 'ASSET_TYPE_17',
                                         'ASSET_TYPE_15', 'F88_FUND', 'CIMB_FUND', 'PACKAGE_GROUP']]\
        .agg({
            'LOAN_WID': 'count',
            'COLLATERAL_AMT': 'mean',
            'DISBURSE_AMT': 'mean',
            'MAX_LENDABLE_AMT': 'mean',
            'TERM_FREQUENCY': 'mean',
            'MOB': 'mean',
            'ASSET_TYPE_17': 'max',
            'ASSET_TYPE_15': 'max',
            'F88_FUND': 'max',
            'CIMB_FUND': 'max',
            'PACKAGE_GROUP': 'max',
        })\
        .reset_index()\
        .assign(MAX_LTV = lambda x: x.MAX_LENDABLE_AMT/x.COLLATERAL_AMT,
            LTV = lambda x: x.DISBURSE_AMT/x.COLLATERAL_AMT,
            RATIO_DIS_MAX_AMT = lambda x: x.DISBURSE_AMT/x.MAX_LENDABLE_AMT,
            RATIO_MOB_TERM = lambda x: x.MOB/x.TERM_FREQUENCY,
            MOB_1 = lambda x: (x.MOB <= 1).astype(int),
            MOB_2 = lambda x: (x.MOB <= 2).astype(int),
            MOB_3 = lambda x: (x.MOB <= 3).astype(int))\
        .rename(columns={'LOAN_WID': 'CNT_CONTRACT'})\
        [['DATE_WID', 'CUSTOMER_CODE', 'CNT_CONTRACT', 'MOB_1', 'MOB_2', 'MOB_3', 'ASSET_TYPE_17', 'ASSET_TYPE_15', 'F88_FUND', 'CIMB_FUND', 'MAX_LTV', 'LTV', 'RATIO_DIS_MAX_AMT',
        'RATIO_MOB_TERM', 'PACKAGE_GROUP']]
    assert (dd_1.groupby(['DATE_WID', 'CUSTOMER_CODE']).CUSTOMER_CODE.agg('count') > 1).sum() == 0

    # handle dd_2 closed_info
    dd_2 = dd_2\
        .loc[lambda x: x.ASSET_TYPE_WID.isin([13, 17, 7, 15])]\
        .assign(ASSET_TYPE_17 = lambda x: (x.ASSET_TYPE_WID.isin([13, 17])).astype(int),
            ASSET_TYPE_15 = lambda x: (x.ASSET_TYPE_WID.isin([7, 15])).astype(int),
            F88_FUND = lambda x: (x.FUND_NAME == 'F88 Fund').astype(int),
            CIMB_FUND = lambda x: (x.FUND_NAME == 'CIMB Fund').astype(int),
            MONTH_DURATION = lambda x: (pd.to_datetime(x.CLOSED_DATE_WID, format='%Y%m%d') - pd.to_datetime(x.DISBURSE_DATE_WID, format='%Y%m%d')).dt.days/30,
            MONTH_TERM_DUR = lambda x: round(x.MONTH_DURATION / x.TERM_FREQUENCY, 1)
        )\
        .groupby(['DATE_WID', 'CUSTOMER_CODE'])[['LOAN_WID', 'COLLATERAL_AMT', 'DISBURSE_AMT', 'MAX_LENDABLE_AMT', 'ASSET_TYPE_17',
                                         'ASSET_TYPE_15', 'F88_FUND', 'CIMB_FUND', 'MONTH_TERM_DUR']]\
        .agg({
            'LOAN_WID': 'count',
            'COLLATERAL_AMT': 'mean',
            'DISBURSE_AMT': 'mean',
            'MAX_LENDABLE_AMT': 'mean',
            'ASSET_TYPE_17': 'max',
            'ASSET_TYPE_15': 'max',
            'F88_FUND': 'max',
            'CIMB_FUND': 'max',
            'MONTH_TERM_DUR': 'mean'
        })\
        .reset_index()\
        .assign(CLOSED_MAX_LTV = lambda x: x.MAX_LENDABLE_AMT/x.COLLATERAL_AMT,
            CLOSED_LTV = lambda x: x.DISBURSE_AMT/x.COLLATERAL_AMT,
            CLOSED_RATIO_DIS_MAX_AMT = lambda x: x.DISBURSE_AMT/x.MAX_LENDABLE_AMT,
            )\
        .rename(columns={'LOAN_WID': 'CLOSED_CNT_CONTRACT', 'ASSET_TYPE_17': 'CLOSED_ASSET_TYPE_17', 'ASSET_TYPE_15': 'CLOSED_ASSET_TYPE_15',
                 'F88_FUND': 'CLOSED_F88_FUND', 'CIMB_FUND': 'CLOSED_CIMB_FUND', 'MONTH_TERM_DUR': 'CLOSED_REAL_MONTH_PCT'})\
        [['DATE_WID', 'CUSTOMER_CODE', 'CLOSED_CNT_CONTRACT', 'CLOSED_ASSET_TYPE_17', 'CLOSED_ASSET_TYPE_15', 'CLOSED_F88_FUND', 'CLOSED_CIMB_FUND',
            'CLOSED_MAX_LTV', 'CLOSED_LTV', 'CLOSED_RATIO_DIS_MAX_AMT', 'CLOSED_REAL_MONTH_PCT']]
    assert (dd_2.groupby(['DATE_WID', 'CUSTOMER_CODE']).CUSTOMER_CODE.agg('count') > 1).sum() == 0

    # handle dd_3a active_payment
    dd_3a = dd_3a\
    .groupby(['DATE_WID', 'CUSTOMER_CODE']).TRA_CPV.agg('sum').reset_index()
    assert (dd_3a.groupby(['DATE_WID', 'CUSTOMER_CODE']).CUSTOMER_CODE.agg('count') > 1).sum() == 0

    # handle dd_3b active_miengiam
    dd_3b = dd_3b\
    .loc[lambda x: x.ACTION_CODE.isin(['MIEN_GIAM_CPV', 'MIEN_GIAM_PHAT', 'MIEN_GIAM_PHI'])]\
    .pivot_table(index=['DATE_WID', 'CUSTOMER_CODE'], columns='ACTION_CODE', values='TRANS_AMT', aggfunc=['sum', 'count']).reset_index().fillna(0)
    dd_3b.columns = ['DATE_WID', 'CUSTOMER_CODE', 'MIEN_GIAM_CPV_AMT', 'MIEN_GIAM_PHAT_AMT', 'MIEN_GIAM_PHI_AMT',
                                    'MIEN_GIAM_CPV_CNT', 'MIEN_GIAM_PHAT_CNT', 'MIEN_GIAM_PHI_CNT']
    assert (dd_3b.groupby(['DATE_WID', 'CUSTOMER_CODE']).CUSTOMER_CODE.agg('count') > 1).sum() == 0

    # handle dd_4 active_cur_dpd
    dd_4 = dd_4\
    .groupby(['DATE_WID', 'CUSTOMER_CODE']).DPD.agg(['max', 'mean']).reset_index()\
    .rename(columns={'max': 'MAX_CUR_DPD', 'mean': 'AVG_CUR_DPD'})
    assert (dd_4.groupby(['DATE_WID', 'CUSTOMER_CODE']).CUSTOMER_CODE.agg('count') > 1).sum() == 0

    # handle dd_6 active_obligation
    dd_6 = dd_6\
    .groupby(['DATE_WID', 'CUSTOMER_CODE']).agg({'LOAN_WID': 'count', 'TOTAL_PRINCIPAL_AMT': 'sum',
                                             'TOTAL_INTEREST_AMT': 'sum', 'TOTAL_OBLIGATION_AMT': 'sum'})\
    .reset_index()
    assert (dd_6.groupby(['DATE_WID', 'CUSTOMER_CODE']).CUSTOMER_CODE.agg('count') > 1).sum() == 0


    # merge
    dd = dd_0\
        .merge(dd_1, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .merge(dd_2, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .merge(dd_3a, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .merge(dd_3b, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .merge(dd_4, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .merge(dd_5, how='left', on=['DATE_WID', 'CUSTOMER_CODE'])\
        .merge(dd_6, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])
    dd.to_csv(os.path.join(ORIGIN_DIR,'Data/' + str(date_wid) + 'dd_0.csv'), index=False)
    return dd
    

#def main():
#    df = ETL(20240719)
#if __name__ == "__main__":
#    main()