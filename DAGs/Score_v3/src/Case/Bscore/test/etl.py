import pandas as pd
from matplotlib import pyplot as plt
import numpy as np
import seaborn as sns

import cx_Oracle
import os
import sys



dsn_tns = cx_Oracle.makedsn('*********', '1521', 'orcldwh1') # if needed, place an 'r' before any parameter in order to address special characters such as '\'.
conn = cx_Oracle.connect(user=r'toantd', password='F88_123456', dsn=dsn_tns) # if needed, place an 'r' before any parameter in order to address special characters such as '\'. For example, if your user name contains '\', you'll need to place 'r' before the user name: user=r'User Name'

cursor = conn.cursor()
#print(conn.version)


def connectdata(query):
    cursor.execute(query)
    df = cursor.fetchall()
    columns = [c[0] for c in cursor.description]
    df = pd.DataFrame(df, columns=columns)
    return df


def ETL(date_wid):
    date_wid=int(date_wid)
    sql_str_1 = "SELECT DISTINCT {date_wid} DATE_WID, B<PERSON>CUSTOMER_CODE, A<PERSON>LOAN_WID, A.ASSET_TP_WID, A.ASSET_WID, \
               A.LOAN_ASSET_NM, A.APPRAISAL_AMT, A.LOAN_AMT, A.LOAN_MAX_AMT, DISBURSE_DATE_WID, \
               NVL(CAST(A.MADE_YEAR AS VARCHAR(20)), SUBSTR(LOAN_ASSET_NM, -4)) MADE_YEAR, \
               CASE WHEN TERM_FREQUENCY_UNIT = 'Days' THEN ROUND(TERM_FREQUENCY / 30, 1) ELSE TERM_FREQUENCY END TERM_FREQUENCY, \
               CASE WHEN TERM_FREQUENCY_UNIT = 'Days' THEN 1 ELSE 0 END TERM_FREQUENCY_DAYS, \
               CASE WHEN ASSET_TYPE_WID = 17 THEN 1 ELSE 0 END ASSET_TYPE_17 \
               FROM F88DWH.W_LOAN_ASSET_F A \
               LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID \
               WHERE B.DISBURSE_DATE_WID <= {date_wid} \
               AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) \
               AND NVL(A.LOAN_MAX_AMT, 0) != 0 \
               AND NVL(A.APPRAISAL_AMT, 0) != 0 \
               ".format(date_wid=date_wid)
    sql_str_2 = "SELECT DISTINCT {date_wid} DATE_WID, CUSTOMER_CODE, LOAN_WID, COLLATERAL_AMT, \
               CASE WHEN FUND_NAME = 'CIMB Fund' THEN 1 ELSE 0 END CIMB_FUND, \
               CASE WHEN CLOSED_DATE_WID IS NOT NULL AND CLOSED_DATE_WID <= {date_wid} \
               THEN DISBURSE_AMT ELSE NULL END DISBURSE_AMT_CLOSED, \
               CASE WHEN CLOSED_DATE_WID IS NOT NULL AND CLOSED_DATE_WID <= {date_wid} \
               THEN NULL ELSE COLLATERAL_AMT END COLLATERAL_AMT_ACTIVE, \
               DISBURSE_AMT, ROUND(INTEREST_RATE, 1) INTEREST_RATE, \
               CASE WHEN ASSET_TYPE_WID = 15 THEN 1 ELSE 0 END ASSET_TYPE_15 \
               FROM F88DWH.W_LOAN_DTL_F \
               WHERE DISBURSE_DATE_WID <= {date_wid} \
               ".format(date_wid=date_wid)
    sql_str_3 = "SELECT DISTINCT {date_wid} DATE_WID, B.CUSTOMER_CODE, A.LOAN_WID, TRANSACTION_WID, TRANS_AMT TRA_CPV \
               FROM F88DWH.W_LOAN_TRANS_DTL_F A \
               LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID \
               WHERE DISBURSE_DATE_WID <= {date_wid} \
               AND TRANS_DATE_WID <= {date_wid} \
               AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) \
               AND ACTION_CODE IN ('TRA_CPV_CIMB', 'TRA_CPV') \
               ".format(date_wid=date_wid)
    sql_str_4 = "SELECT DISTINCT DATE_WID, A.CUSTOMER_CODE, A.LOAN_WID, NVL(B.OVERDUE_DAYS, B.OVERDUE_DAYS_ADJUST) DPD \
               FROM F88DWH.W_LOAN_DTL_F A \
               LEFT JOIN F88DWH.W_LOAN_DAILY_F B ON A.LOAN_WID = B.LOAN_WID \
               WHERE B.YEAR_NUM = ROUND({date_wid} / 10000, 0) \
               AND B.MONTH_NUM = ROUND({date_wid} / 100, 0) - ROUND({date_wid} / 10000, 0) * 100 \
               AND B.DATE_WID = {date_wid} \
               AND A.DISBURSE_DATE_WID <= {date_wid} \
               ".format(date_wid=date_wid)
    
    # read sql - E
    print("2")
    dd_1 = connectdata(sql_str_1)
    dd_1.columns = [col.upper() for col in dd_1.columns]
    print("3")
    dd_2 = connectdata(sql_str_2)
    dd_2.columns = [col.upper() for col in dd_2.columns]
    print("4")
    dd_3 = connectdata(sql_str_3)
    dd_3.columns = [col.upper() for col in dd_3.columns]
    print("5")
    print(sql_str_4)
    dd_4 = connectdata(sql_str_4)
    dd_4.columns = [col.upper() for col in dd_4.columns]
    print("6")
    date_wid=int(date_wid)
    # transform - T
    dd_1['LOAN_LOANMAX_RATIO'] = dd_1['LOAN_AMT'] / dd_1['LOAN_MAX_AMT']
    dd_1['LTV'] = dd_1['LOAN_AMT'] / dd_1['APPRAISAL_AMT']
    dd_1['MADE_YEAR'] = dd_1['MADE_YEAR'].str.replace('[^0-9.]', '', regex = True).replace('.', '').replace('', np.nan).astype(float)
    dd_1.loc[(dd_1['MADE_YEAR'] > int(np.floor(date_wid / 10000))) | (dd_1['MADE_YEAR'] < 1980), 'MADE_YEAR'] = np.nan
    dd_1['MADE_YEAR'] = np.floor(date_wid / 10000) - dd_1['MADE_YEAR'].astype(float)
    dd_1['MOB'] = np.floor((pd.to_datetime(date_wid, format='%Y%m%d') - pd.to_datetime(dd_1['DISBURSE_DATE_WID'], format='%Y%m%d')).dt.days / 30)
    
    # load - L
    dd_10 = dd_1.groupby(['DATE_WID', 'CUSTOMER_CODE'])[['LOAN_LOANMAX_RATIO', 'LTV', 'MADE_YEAR', 'LOAN_AMT', 'MOB',
                                                        'LOAN_WID', 'TERM_FREQUENCY', 'TERM_FREQUENCY_DAYS', 'ASSET_TYPE_17']].agg(
    {
        'LOAN_LOANMAX_RATIO': ['mean', 'max'], 
        'LTV': ['mean', 'min'],
        'MADE_YEAR': 'mean',
        'LOAN_AMT': ['mean', 'max', 'sum'],
        'MOB': 'min',
        'LOAN_WID': 'nunique',
        'TERM_FREQUENCY': 'mean',
        'TERM_FREQUENCY_DAYS': 'sum',
        'ASSET_TYPE_17': 'sum',
    }).reset_index()
    ## rename
    name = dd_10.columns
    nas = []
    for na in name:
        nas.append(''.join(na))
    dd_10.columns = nas
    
    dd_20 = dd_2.groupby(['DATE_WID', 'CUSTOMER_CODE'])[['COLLATERAL_AMT', 'COLLATERAL_AMT_ACTIVE', 'CIMB_FUND', 
                                                        'DISBURSE_AMT_CLOSED', 'DISBURSE_AMT', 'INTEREST_RATE',
                                                        'ASSET_TYPE_15']].agg(
    {
        'COLLATERAL_AMT': ['mean', 'max', 'min'],
        'COLLATERAL_AMT_ACTIVE': ['mean', 'max', 'min'],
        'CIMB_FUND': 'sum',
        'DISBURSE_AMT_CLOSED': 'max',
        'DISBURSE_AMT': 'mean',
        'INTEREST_RATE': 'max',
        'ASSET_TYPE_15': 'max',
    }).reset_index()
    ## rename
    name = dd_20.columns
    nas = []
    for na in name:
        nas.append(''.join(na))
    dd_20.columns = nas
    
    dd_30 = dd_3.groupby(['DATE_WID', 'CUSTOMER_CODE']).TRA_CPV.agg('sum').reset_index()
    
    dd_40 = dd_4.groupby(['DATE_WID', 'CUSTOMER_CODE']).DPD.agg('max').reset_index()
        
    dd_0 = dd_10.merge(dd_20, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
    .merge(dd_30, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
    .merge(dd_40, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
    
    dd_0['TRA_CPV_LOAN_AMT_sum_RATIO'] = dd_0['TRA_CPV'] / dd_0['LOAN_AMTsum']
    dd_0.loc[lambda x: pd.isna(x.TRA_CPV) & ~pd.isna(x.LOAN_AMTsum), 'TRA_CPV_LOAN_AMT_sum_RATIO'] = .0
    print("7")
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)
    dd_0.to_csv(os.path.join(path,'Data/' + str(date_wid) + 'dd_0.csv'), index=False)
    dd_0.to_pickle(os.path.join(path,'Data/' + str(date_wid) + 'dd_0.pkl'))
    return dd_0
    

def main():
    df = ETL(20231210)
if __name__ == "__main__":
    main()
    
