# def up2db():
import pandas as pd
import os
import cx_Oracle
import csv
from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
import time
import numpy as np
from airflow.providers.oracle.hooks.oracle import OracleHook


dsn_tns = cx_Oracle.makedsn('*********', '1521', 'orcldwh1') # if needed, place an 'r' before any parameter in order to address special characters such as '\'.
conn = cx_Oracle.connect(user=r'F88DWH', password='Z5SITGJBEX5B', dsn=dsn_tns) # if needed, place an 'r' before any parameter in order to address special characters such as '\'. For example, if your user name contains '\', you'll need to place 'r' before the user name: user=r'User Name'

cursor = conn.cursor()
#print(conn.version)
# print(os.path.join(os.getcwd(),'file/result.csv'))
def insert_many(date_wid):
    # Predefine the memory areas to match the table definition.
    # This can improve performance by avoiding memory reallocations.
    # Here, one parameter is passed for each of the columns.
    # "None" is used for the ID column, since the size of NUMBER isn't
    # variable.  The "25" matches the maximum expected data size for the
    # NAME column
    #cursor.setinputsizes(None, 25)
    
    # Adjust the number of rows to be inserted in each iteration
    # to meet your memory and performance requirements
    batch_size = 10000
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)
    with open(os.path.join(path,'Data/'+str(date_wid)+'_result.csv'), 'r') as csv_file:
        #csv_reader = csv.reader(csv_file, delimiter=',')
        df = pd.read_csv(csv_file,sep= ',', header = None) #to start from the top(first) row 
        df = df.rename(columns = {'ASSET_TYPE_15max':'ASSET_TYPE_15MAX'})
        csv_reader = df.astype(str).values.tolist()
        line_count = 0
        for row in csv_reader:
            if line_count == 0:
                print(f'Column names are {", ".join(row)}')
            if line_count == 1:
                print(f'Data names are {", ".join(row)}')
                break
            line_count += 1
            
        print(df.head(10))
        sql = "insert into HUYNS.W_B_SCORE_F(DATE_WID,CUSTOMER_CODE,DPD, ASSET_TYPE_15max, SCORE, RANK) values (:DATE_WID, :CUSTOMER_CODE, :DPD,:ASSET_TYPE_15MAX,:SCORE,:RANK)"
        data = []
        for line in csv_reader:
            data.append((line[0], line[1]))
            #print(data)
            if len(data) % batch_size == 0:
                cursor.executemany(sql, data)
                for error in cursor.getbatcherrors():
                    print("Error", error.message, "at row offset", error.offset)
                data = []
        print(pd.DataFrame(data).head(10))
        if data:
            cursor.executemany(sql, data)
            for error in cursor.getbatcherrors():
                print("Error", error.message, "at row offset", error.offset)
        con.commit()
        
# print(os.path.join(os.getcwd(),'file/result.csv'))
def up2db(date_wid,conn_id='oracle_f88_dwh'):
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)
    #hook = BaseHook.get_hook(conn_id = conn_id)
    #data = pd.read_csv(os.path.join(path,'Data/'+str(date_wid)+'_result.csv'))
    hook = OracleHook(conn_id= 'oracle_f88_dwh')
    conn = hook.get_connection(conn_id= 'oracle_f88_dwh')
    
    #data = pd.read_csv(os.path.join(path,'Data/'+str(date_wid)+'_result.csv'))
    #data = data.drop(['Unnamed: 0'], axis =1)
    #data = data.dropna()
    
    # hook = BaseHook.get_hook(conn_id)
    username = conn.login
    password = conn.password
    host = conn.host
    port = conn.port
    schema = conn.schema
    # conn_uri = hook.get_uri()
    # connection = create_engine(conn_uri, echo=True)
    # data.to_sql('W_B_SCORE_F', connection, if_exists='replace', index=False)
    # print(os.path.join(os.getcwd(),'file/result.csv'))
    # hook = BaseHook.get_hook(conn_id)
    # conn_uri = hook.get_uri()
    # connection = create_engine('oracle+cx_oracle://TESTDWH:QSkNAmA2bzZxCLS4KFho@************:1521/orcldwh1', echo=True)
    dsn = cx_Oracle.makedsn(host, port, service_name=schema)
    connection = cx_Oracle.connect(username, password, dsn)
    cursor=connection.cursor()
    # data.to_sql('Score', connection, if_exists='replace', index=False)
    
    #data = [list(x) for x in data.values]
    #vt=0
    #for i in data:
    #    tmp_tuple=(int(i[0]),int(i[1]),(float(i[2]) if not np.isnan(float(i[2])) else None),int(i[3]),float(i[4]),str(i[5]))
    #    data[vt]=tmp_tuple
    #    vt=vt+1
    #batch_size=10000
    query="TRUNCATE TABLE F88DWH.W_B_SCORE_F"
    #cursor.execute(query)
    #connection.commit()
    #for pos in range (0,len(data),batch_size):
    #    dt=data[pos:min(pos+batch_size,len(data))]
    #    query = f"""INSERT INTO F88DWH.W_B_SCORE_F ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15MAX","SCORE","RANK")  VALUES (:1,:2,:3,:4,:5,:6)"""
    #    cursor.executemany(query, dt)
    
    #data = pd.read_csv(os.path.join(path,'Data/'+str(date_wid)+'_result.csv'))
    #query = "select * from F88DWH.W_B_SCORE_LOGS_F"
    #data_score_main = pd.read_sql(query,con=connection)
    #data_selected= data[['CUSTOMER_CODE','DATE_WID','RANK']]
    #merged_data = pd.merge(data_score_main, data_selected, on='CUSTOMER_CODE', how='left')
    #mask = (merged_data['DATE_WID_x'] < merged_data['DATE_WID_y']) & (merged_data['RANK_x'] != merged_data['RANK_y'])
    #data_recorded = merged_data.loc[mask, ['DATE_WID_y','CUSTOMER_CODE','DPD', 'ASSET_TYPE_15max','SCORE','RANK_y']].copy()
    #data_recorded['CUSTOMER_CODE'] = data_recorded['CUSTOMER_CODE'].astype(int)
    #data_recorded['DATE_WID_y'] = data_recorded['DATE_WID_y'].astype(int)
    #data_recorded['RANK_y'] = data_recorded['RANK_y'].astype(str)
    #dt_recorded = [(str(x[2]),int(x[1]),int(x[0])) for x in data_recorded.values]
    #data_recorded = [list(x) for x in data_recorded.values]
    #vt=0
    #for i in data_recorded:
    #    tmp_tuple=(int(i[0]),int(i[1]),(float(i[2]) if not np.isnan(float(i[2])) else None),int(i[3]),float(i[4]),str(i[5]))
    #    data_recorded[vt]=tmp_tuple
    #    vt=vt+1
    #data_recorded.to_csv("merge.csv")
    #for pos in range (0,len(data_recorded),batch_size):
    #    dt=data_recorded[pos:min(pos+batch_size,len(data))]
    #    query = f"""INSERT INTO F88DWH.W_B_SCORE_LOGS_F ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15max","SCORE","RANK")  VALUES (:1,:2,:3,:4,:5,:6)"""
    #    cursor.executemany(query, dt)
    query_check="Select count(1) from F88DWH.W_B_SCORE_F where DPD is not null"
    dd_1 = pd.read_sql(query_check, connection)
    print(dd_1.loc[0][0])
    if dd_1.loc[0][0] == 0:
            cursor.execute(query)
            connection.commit()
    cursor.close()

    
def up2db_v2(date_wid,conn_id='oracle_f88_dwh'):
      path = os.path.abspath(__file__)
      path = os.path.dirname(path)
      data = pd.read_csv(os.path.join(path,'Data/'+str(date_wid)+'_result.csv'))
      data.rename(columns = {'ASSET_TYPE_15max':'ASSET_TYPE_15MAX'}, inplace = True)
      hook = BaseHook.get_hook(conn_id)
      conn_uri = hook.get_uri()
      connection = create_engine(conn_uri, echo=True)
      start_pos = 0
      batch_size = 10000
      print(data.head())
      while start_pos < len(data):
            data_insert = data[start_pos:start_pos +batch_size]
            start_pos += batch_size
            print("Here")
            data_insert.to_sql('W_B_SCORE_F', connection, if_exists='append', index=False)
      #query = """MERGE INTO Huyns."Score_main" s 
      #USING TESTDWH."Score" t
      #ON (s.CUSTOMER_CODE = t.CUSTOMER_CODE)
      #WHEN MATCHED THEN
      #UPDATE SET s.rank = t.rank, s.DATE_WID = t.DATE_WID
      #WHERE s.DATE_WID < t.date_WID AND DBMS_LOB.COMPARE(s.rank, t.rank) <> 0
      #WHEN NOT MATCHED THEN
      #INSERT (DATE_WID, CUSTOMER_CODE, DPD,"ASSET_TYPE_15max",Score,Rank)
      #VALUES (t.DATE_WID, t.CUSTOMER_CODE, t.DPD,t."ASSET_TYPE_15max",t.Score,t.Rank)"""
      #cursor = connection.connect()
      #cursor.execute(query)
      #cursor.close() 
def up2db_pkl(date_wid,conn_id='oracle_f88_dwh'):
      path = os.path.abspath(__file__)
      path = os.path.dirname(path)
      # read pre-pickled object from file and save to table
      data = pd.read_pickle(os.path.join(path,'Data/'+str(date_wid)+'_result.pkl'))
      #query = """MERGE INTO Huyns."Score_main" s 
      #USING TESTDWH."Score" t
      #ON (s.CUSTOMER_CODE = t.CUSTOMER_CODE)
      #WHEN MATCHED THEN
      #UPDATE SET s.rank = t.rank, s.DATE_WID = t.DATE_WID
      #WHERE s.DATE_WID < t.date_WID AND DBMS_LOB.COMPARE(s.rank, t.rank) <> 0
      #WHEN NOT MATCHED THEN
      #INSERT (DATE_WID, CUSTOMER_CODE, DPD,"ASSET_TYPE_15max",Score,Rank)
      #VALUES (t.DATE_WID, t.CUSTOMER_CODE, t.DPD,t."ASSET_TYPE_15max",t.Score,t.Rank)"""
      #cursor = connection.connect()
      #cursor.execute(query)
      #cursor.close()    
    
def main():
    df = up2db(20231210)
    #df = up2db(20230522)
if __name__ == "__main__":
    main()