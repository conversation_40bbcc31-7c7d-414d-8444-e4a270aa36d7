import pandas as pd
import os, sys
import cx_Oracle
import csv
from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
import time
import numpy as np
from airflow.providers.oracle.hooks.oracle import OracleHook
from DAGs.utils import write_df_to_oracle
from datetime import  datetime
from airflow.models import Variable

ETL_DIR = os.path.abspath(os.path.dirname(__file__))
MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'Bscore/models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ''))
sys.path.append(ETL_DIR)
sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

table_bscore = Variable.get("TABLE_BSCORE_VERSION3")
# print(os.path.join(os.getcwd(),'file/result.csv'))
def up2db(date_wid,conn_id='oracle_f88_dwh'):
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)
    #hook = BaseHook.get_hook(conn_id = conn_id)
    #data = pd.read_csv(os.path.join(path,'Data/'+str(date_wid)+'_result.csv'))
    hook = OracleHook(oracle_conn_id= conn_id)
    connection = hook.get_conn()
    cursor=connection.cursor()

    data = pd.read_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'))
    print(data.head())
    # data = data.drop(['Unnamed: 0'], axis =1)
    # data = data.dropna()
    # hook = BaseHook.get_hook(conn_id)
    # username = conn.login
    # password = conn.password
    # host = conn.host
    # port = conn.port
    # schema = conn.schema
    # # conn_uri = hook.get_uri()
    # connection = create_engine(conn_uri, echo=True)
    # data.to_sql('W_B_SCORE_F', connection, if_exists='replace', index=False)
    # print(os.path.join(os.getcwd(),'file/result.csv'))
    # hook = BaseHook.get_hook(conn_id)
    # conn_uri = hook.get_uri()
    # connection = create_engine('oracle+cx_oracle://TESTDWH:QSkNAmA2bzZxCLS4KFho@************:1521/orcldwh1', echo=True)
    # dsn = cx_Oracle.makedsn(host, port, service_name=schema)
    # connection = cx_Oracle.connect(username, password, dsn)
    if data['DPD'].notna().sum() > 200000:
        query = f"TRUNCATE TABLE {table_bscore}"
        cursor.execute(query)
        connection.commit()
    ##
        CREATED_DATE = datetime.today().strftime('%Y-%m-%d %H:%M:%S')
        if 'DATE_WID' not in data.columns:
            data['DATE_WID'] = date_wid
        if 'CREATED_DATE' not in data.columns:
            data['CREATED_DATE'] = CREATED_DATE
        write_df_to_oracle(table_name=table_bscore, oracle_conn_id='oracle_f88_dwh', df=data)

        # data.to_sql('Score', connection, if_exists='replace', index=False)
        # try:
        #     data = [list(x) for x in data.values]
        #     vt=0
        #     for i in data:
        #         tmp_tuple=(int(i[0]),int(i[1]),(float(i[2]) if not np.isnan(float(i[2])) else None),int(i[3]),float(i[4]),str(i[5]) ,str(i[6]),str(i[7]))
        #         data[vt]=tmp_tuple
        #         vt=vt+1
        # except Exception as e:
        #     raise(f"Chuyen doi tuple loi tai {i}: {e}")
        # batch_size=10000
        #
        # for pos in range (0,len(data),batch_size):
        #     dt=data[pos:min(pos+batch_size,len(data))]
        #     query = f"""INSERT INTO F88DWH.W_B_SCORE_F ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15MAX","SCORE","RANK","BSCORE","CREATED_DATE")  VALUES (:1,:2,:3,:4,:5,:6,:7,:8)"""
        #     cursor.executemany(query, dt)
        #     #try:
        #     #    cursor.executemany(query, dt)
        #     #except cx_Oracle.DatabaseError as e:
        #     #    print("Error at:", dt)
        #     #    print("Detail Error:", e)
        #     connection.commit()
        print(f"Da insert vao F88DWH.W_B_SCORE_F_VERSION3 thanh cong")
        #data = pd.read_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'))
        #query = "select * from F88DWH.W_B_SCORE_LOGS_F"
        #data_score_main = pd.read_sql(query,con=connection)
        #data_selected= data[['CUSTOMER_CODE','DATE_WID','RANK']]
        #merged_data = pd.merge(data_score_main, data_selected, on='CUSTOMER_CODE', how='left')
        #mask = (merged_data['DATE_WID_x'] < merged_data['DATE_WID_y']) & (merged_data['RANK_x'] != merged_data['RANK_y'])
        #data_recorded = merged_data.loc[mask, ['DATE_WID_y','CUSTOMER_CODE','DPD', 'ASSET_TYPE_15max','SCORE','RANK_y']].copy()
        #data_recorded['CUSTOMER_CODE'] = data_recorded['CUSTOMER_CODE'].astype(int)
        #data_recorded['DATE_WID_y'] = data_recorded['DATE_WID_y'].astype(int)
        #data_recorded['RANK_y'] = data_recorded['RANK_y'].astype(str)
        #dt_recorded = [(str(x[2]),int(x[1]),int(x[0])) for x in data_recorded.values]
        #data_recorded = [list(x) for x in data_recorded.values]
        #vt=0
        #for i in data_recorded:
        #    tmp_tuple=(int(i[0]),int(i[1]),(float(i[2]) if not np.isnan(float(i[2])) else None),int(i[3]),float(i[4]),str(i[5]))
        #    data_recorded[vt]=tmp_tuple
        #    vt=vt+1
        #data_recorded.to_csv("merge.csv")
        #for pos in range (0,len(data_recorded),batch_size):
        #    dt=data_recorded[pos:min(pos+batch_size,len(data))]
        #    query = f"""INSERT INTO F88DWH.W_B_SCORE_LOGS_F ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15max","SCORE","RANK")  VALUES (:1,:2,:3,:4,:5,:6)"""
        #    cursor.executemany(query, dt)
        #dt_reordered = [(x[1], x[5], x[0], x[0], x[5], x[0],x[1],x[2], x[3], x[4],x[5]) for x in data]
        #for pos in range (0,len(dt_reordered),batch_size):
        #    dt=dt_reordered[pos:min(pos+batch_size,len(dt_reordered))]
        #    query = """MERGE INTO F88DWH.W_B_SCORE_LOGS_F s
        #    USING dual
        #    ON (s."CUSTOMER_CODE" = :2)
        #    WHEN MATCHED THEN
        #    UPDATE SET s."RANK" = :6, s."DATE_WID" = :1
        #    WHERE s."DATE_WID"< :1 AND DBMS_LOB.COMPARE(:6, s."RANK") <> 0
        #    WHEN NOT MATCHED THEN
        #    INSERT ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15max","SCORE","RANK")
        #    VALUES (:1, :2, :3,:4,:5,:6)"""
        #    cursor.executemany(query, dt)
            #try:
            #    cursor.executemany(query, dt)
            #except cx_Oracle.DatabaseError as e:
            #    print("Error at:", dt)
            #    print("Detail Error:", e)
            #connection.commit()
        #query_merge ="MERGE INTO F88DWH.W_B_SCORE_F a\
         #USING\
         #(SELECT CIFCODE_ACTIVE , min(MIN_SCORE) AS MIN_SCORE, max(MIN_RANK) AS MIN_RANK\
         #FROM (\
         #SELECT a.CIFCODE_ACTIVE, b.BSCORE  AS ACTIVE_SCORE, c.BSCORE AS INACTIVE_SCORE,\
         #CASE WHEN b.BSCORE < c.BSCORE THEN  b.BSCORE ELSE c.BSCORE END MIN_SCORE,\
         #CASE WHEN b.BSCORE < c.BSCORE THEN  b.RANK  ELSE c.RANK  END MIN_RANK\
         #FROM F88DWH.W_CIF_INACTIVE_ACTIVE_F a\
         #LEFT JOIN F88DWH.W_B_SCORE_F b ON a.CIFCODE_ACTIVE = b.CUSTOMER_CODE\
         #LEFT JOIN F88DWH.W_B_SCORE_F c ON a.CIFCODE_INACTIVE  = c.CUSTOMER_CODE)a\
         #GROUP BY CIFCODE_ACTIVE) b ON (a.CUSTOMER_CODE = b.CIFCODE_ACTIVE)\
         #WHEN MATCHED THEN UPDATE set a.BSCORE  = b.MIN_SCORE,a.RANK = b.MIN_RANK"
        #cursor.execute(query_merge)
        #connection.commit()
        run_procedure = "BEGIN F88DWH.update_score_backup_version3(); END;"
        cursor.execute(run_procedure, )
        connection.commit()

        cursor.close()
        try:
            print("Start delete file score")
            file_path = os.path.join(ORIGIN_DIR, 'Data', f'{date_wid}_result.csv')

            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"Đã xóa: {file_path}")
            else:
                print(f"Không tìm thấy file: {file_path}")
        except:
            print("Error: can not delete file")
    else:
        print(f"Khong thuc hien insert vao F88DWH.W_B_SCORE_F_VERSION3")

    
#def main():
#    df = up2db(20240719)
#    df = up2db(20230523)
#if __name__ == "__main__":
#    main()