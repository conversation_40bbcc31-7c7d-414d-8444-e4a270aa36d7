import pandas as pd
import os, sys
import cx_Oracle
import csv
from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
import time
import numpy as np
from airflow.providers.oracle.hooks.oracle import OracleHook

#ETL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../ETL'))
#MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__)))
#sys.path.append(ETL_DIR)
#sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

# print(os.path.join(os.getcwd(),'file/result.csv'))
def up2db(date_wid,conn_id='oracle_f88_dwh'):
    path = os.path.abspath(__file__)
    path = os.path.dirname(path)

    hook = OracleHook(conn_id= 'oracle_f88_dwh')
    conn = hook.get_connection(conn_id= 'oracle_f88_dwh')
    data = pd.read_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'))

    username = conn.login
    password = conn.password
    host = conn.host
    port = conn.port
    schema = conn.schema

    dsn = cx_Oracle.makedsn(host, port, service_name=schema)
    connection = cx_Oracle.connect(username, password, dsn)
    cursor=connection.cursor()
 
    data = [list(x) for x in data.values]
    vt=0
    for i in data:
        tmp_tuple=(int(i[0]),str(i[1]),(int(i[2]) if not np.isnan(int(i[2])) else None),float(i[3]),str(i[4]),str(i[5]))  #(float(i[2]) if not np.isnan(float(i[2])) else None)
        data[vt]=tmp_tuple
        vt=vt+1
    batch_size=10000
    query="TRUNCATE TABLE F88DWH.W_EWS_SCORE_F"
    cursor.execute(query)
    connection.commit()
    for pos in range (0,len(data),batch_size):
        dt=data[pos:min(pos+batch_size,len(data))]
        query = f"""INSERT INTO F88DWH.W_EWS_SCORE_F ("DATE_WID","CUSTOMER_CODE","CURRENT_DPD","SCORE","RISK_GROUP","DATE_CREATED")  VALUES (:1,:2,:3,:4,:5,:6)"""
        cursor.executemany(query, dt)
        #try:
        #    cursor.executemany(query, dt)
        #except cx_Oracle.DatabaseError as e:
        #    print("Error at:", dt)
        #    print("Detail Error:", e)
        connection.commit()
    #data = pd.read_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'))
    #query = "select * from F88DWH.W_B_SCORE_LOGS_F"
    #data_score_main = pd.read_sql(query,con=connection)
    #data_selected= data[['CUSTOMER_CODE','DATE_WID','RANK']]
    #merged_data = pd.merge(data_score_main, data_selected, on='CUSTOMER_CODE', how='left')
    #mask = (merged_data['DATE_WID_x'] < merged_data['DATE_WID_y']) & (merged_data['RANK_x'] != merged_data['RANK_y'])
    #data_recorded = merged_data.loc[mask, ['DATE_WID_y','CUSTOMER_CODE','DPD', 'ASSET_TYPE_15max','SCORE','RANK_y']].copy()
    #data_recorded['CUSTOMER_CODE'] = data_recorded['CUSTOMER_CODE'].astype(int)
    #data_recorded['DATE_WID_y'] = data_recorded['DATE_WID_y'].astype(int)
    #data_recorded['RANK_y'] = data_recorded['RANK_y'].astype(str)
    #dt_recorded = [(str(x[2]),int(x[1]),int(x[0])) for x in data_recorded.values]
    #data_recorded = [list(x) for x in data_recorded.values]
    #vt=0
    #for i in data_recorded:
    #    tmp_tuple=(int(i[0]),int(i[1]),(float(i[2]) if not np.isnan(float(i[2])) else None),int(i[3]),float(i[4]),str(i[5]))
    #    data_recorded[vt]=tmp_tuple
    #    vt=vt+1
    #data_recorded.to_csv("merge.csv")
    #for pos in range (0,len(data_recorded),batch_size):
    #    dt=data_recorded[pos:min(pos+batch_size,len(data))]
    #    query = f"""INSERT INTO F88DWH.W_B_SCORE_LOGS_F ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15max","SCORE","RANK")  VALUES (:1,:2,:3,:4,:5,:6)"""
    #    cursor.executemany(query, dt)
    #dt_reordered = [(x[1], x[5], x[0], x[0], x[5], x[0],x[1],x[2], x[3], x[4],x[5]) for x in data]
    #for pos in range (0,len(dt_reordered),batch_size):
    #    dt=dt_reordered[pos:min(pos+batch_size,len(dt_reordered))]
    #    query = """MERGE INTO F88DWH.W_B_SCORE_LOGS_F s 
    #    USING dual
    #    ON (s."CUSTOMER_CODE" = :2)
    #    WHEN MATCHED THEN
    #    UPDATE SET s."RANK" = :6, s."DATE_WID" = :1
    #    WHERE s."DATE_WID"< :1 AND DBMS_LOB.COMPARE(:6, s."RANK") <> 0
    #    WHEN NOT MATCHED THEN
    #    INSERT ("DATE_WID","CUSTOMER_CODE","DPD","ASSET_TYPE_15max","SCORE","RANK")
    #    VALUES (:1, :2, :3,:4,:5,:6)"""
    #    cursor.executemany(query, dt)
        #try:
        #    cursor.executemany(query, dt)
        #except cx_Oracle.DatabaseError as e:
        #    print("Error at:", dt)
        #    print("Detail Error:", e)
        #connection.commit()
    #query_merge = "MERGE INTO F88DWH.W_EWS_SCORE_F a\
     #               USING\
      #              (\
       #              Select CIFCODE_ACTIVE,\
        #              MAX_SCORE,\
         #            CASE WHEN nvl(MAX_SCORE,0) <= 0.00114 THEN 'Thap'\
          #                 WHEN MAX_SCORE <= 0.00225 THEN 'Trung binh'\
           #                WHEN MAX_SCORE <= 0.00372 THEN 'Cao'\
            #               ELSE 'Rat cao'\
             #              END RANK_\
              #      from\
               #      (SELECT CIFCODE_ACTIVE , max(MAX_SCORE) AS MAX_SCORE\
     #FROM (\
     #SELECT a.CIFCODE_ACTIVE, b.SCORE  AS ACTIVE_SCORE, c.SCORE AS INACTIVE_SCORE,\
     #CASE WHEN b.SCORE < c.SCORE THEN  c.SCORE ELSE b.SCORE END MAX_SCORE\
     #FROM F88DWH.W_CIF_INACTIVE_ACTIVE_F a\
     #LEFT JOIN F88DWH.W_EWS_SCORE_F b ON a.CIFCODE_ACTIVE = b.CUSTOMER_CODE\
     #LEFT JOIN F88DWH.W_EWS_SCORE_F c ON a.CIFCODE_INACTIVE  = c.CUSTOMER_CODE)a\
     #GROUP BY CIFCODE_ACTIVE)) b ON (a.CUSTOMER_CODE = b.CIFCODE_ACTIVE)\
     #WHEN MATCHED THEN UPDATE set a.SCORE  = b.MAX_SCORE, a.RISK_GROUP = b.RANK_"  
    #cursor.execute(query_merge)
    #connection.commit()
          
    query_check="Select count(1) from F88DWH.W_EWS_SCORE_F where CURRENT_DPD is not null"
    dd_1 = pd.read_sql(query_check, connection)
    #print(dd_1)
    if dd_1.loc[0][0] < 200000:
            query="TRUNCATE TABLE F88DWH.W_EWS_SCORE_F"
            cursor.execute(query)
            query2 = "Insert into F88DWH.W_EWS_SCORE_F Select * from F88DWH.W_EWS_SCORE_BK_F"
            cursor.execute(query2)
            connection.commit()
    cursor.close()
    
#def main():
#    df = up2db(20231220)
#    df = up2db(20230523)
#if __name__ == "__main__":
#    main()