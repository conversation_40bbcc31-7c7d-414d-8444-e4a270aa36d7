import pandas as pd
import numpy as np



from sqlalchemy import create_engine
from airflow.hooks.base import BaseHook
import re
import datetime
import os, sys


#ETL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../ETL'))
#MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../models'))
ORIGIN_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__)))
#sys.path.append(ETL_DIR)
#sys.path.append(MODEL_DIR)
sys.path.append(ORIGIN_DIR)

print(ORIGIN_DIR)

def ETL(date_wid, conn_id='oracle_f88_dwh'):

    date_wid=int(date_wid)
    hook = BaseHook.get_hook(conn_id)
    conn_uri = hook.get_uri()
    connection = create_engine(conn_uri, echo=True)
    
    # sql string
    sql_str_1 = """SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, A.ASSET_TP_WID, A.ASSET_WID, A.LOAN_ASSET_NM, A.APPRAISAL_AMT, 
    A.LOAN_AMT, A.LOAN_MAX_AMT, DISBURSE_DATE_WID, NVL(CAST(A.MADE_YEAR AS VARCHAR(20)), SUBSTR(LOAN_ASSET_NM, -4)) MADE_YEAR, 
    CASE WHEN TERM_FREQUENCY_UNIT = 'Days' THEN ROUND(TERM_FREQUENCY / 30, 1) ELSE TERM_FREQUENCY END TERM_FREQUENCY, 
    CASE WHEN TERM_FREQUENCY_UNIT = 'Days' THEN 1 ELSE 0 END TERM_FREQUENCY_DAYS, 
    CASE WHEN ASSET_TYPE_WID = 17 THEN 1 ELSE 0 END ASSET_TYPE_17 
    FROM F88DWH.W_LOAN_ASSET_F A 
    LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID 
    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID
    WHERE B.DISBURSE_DATE_WID <= {date_wid} AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) AND NVL(A.LOAN_MAX_AMT, 0) != 0 
    AND NVL(A.APPRAISAL_AMT, 0) != 0 AND A.STATUS = 1 AND ASSET_TP_WID IN (15, 17) AND A."SOURCE" IN ('LOS')""".format(date_wid=date_wid)

    sql_str_2 = """SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, LOAN_WID, COLLATERAL_AMT, 
    CASE WHEN FUND_NAME = 'CIMB Fund' THEN 1 ELSE 0 END CIMB_FUND, 
    CASE WHEN CLOSED_DATE_WID IS NOT NULL AND CLOSED_DATE_WID <= {date_wid} THEN DISBURSE_AMT ELSE NULL END DISBURSE_AMT_CLOSED, 
    CASE WHEN CLOSED_DATE_WID IS NOT NULL AND CLOSED_DATE_WID <= {date_wid} THEN NULL ELSE COLLATERAL_AMT END COLLATERAL_AMT_ACTIVE, 
    DISBURSE_AMT, ROUND(INTEREST_RATE, 1) INTEREST_RATE, CASE WHEN ASSET_TYPE_WID = 15 THEN 1 ELSE 0 END ASSET_TYPE_15 
    FROM F88DWH.W_LOAN_DTL_F B LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID
    WHERE DISBURSE_DATE_WID <= {date_wid}""".format(date_wid=date_wid)

    sql_str_3 = """SELECT DISTINCT {date_wid} DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, TRANSACTION_WID, TRANS_AMT TRA_CPV 
    FROM F88DWH.W_LOAN_TRANS_DTL_F A 
    LEFT JOIN F88DWH.W_LOAN_DTL_F B ON A.LOAN_WID = B.LOAN_WID 
    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON B.CUSTOMER_WID = C.CUSTOMER_WID
    WHERE DISBURSE_DATE_WID <= {date_wid} AND TRANS_DATE_WID <= {date_wid} AND (B.CLOSED_DATE_WID IS NULL OR B.CLOSED_DATE_WID > {date_wid}) 
    AND ACTION_CODE IN ('TRA_CPV_CIMB', 'TRA_CPV')""".format(date_wid=date_wid)

    sql_str_4 = """SELECT DISTINCT DATE_WID, C.CUSTOMER_CODE, A.LOAN_WID, NVL(B.OVERDUE_DAYS, B.OVERDUE_DAYS_ADJUST) DPD 
    FROM F88DWH.W_LOAN_DTL_F A 
    LEFT JOIN F88DWH.W_LOAN_DAILY_F B ON A.LOAN_WID = B.LOAN_WID 
    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) C ON A.CUSTOMER_WID = C.CUSTOMER_WID
    WHERE B.YEAR_NUM = ROUND({date_wid} / 10000, 0) AND B.MONTH_NUM = ROUND({date_wid} / 100, 0) - ROUND({date_wid} / 10000, 0) * 100 
    AND B.DATE_WID = {date_wid} AND A.DISBURSE_DATE_WID <= {date_wid}""".format(date_wid=date_wid)

    sql_str_5 = """SELECT CUSTOMER_CODE, MAX(MAX_DPD_EVR) MAX_DPD_EVR 
    FROM F88DWH.W_CUSTOMER_MAX_DPD_12MONTHS 
    WHERE THANG <= {date_wid} 
    GROUP BY CUSTOMER_CODE """.format(date_wid=date_wid)

    sql_str_6 = """SELECT DISTINCT {date_wid} DATE_WID, B.CUSTOMER_CODE, A.LOAN_WID, A.ASSET_TYPE_WID, round(A.DISBURSE_AMT/A.COLLATERAL_AMT, 2) LTV, 
    (FLOOR({date_wid}  / 10000) - FLOOR(A.FROM_DATE_WID / 10000)) * 12 + 
    (FLOOR({date_wid} / 100) - FLOOR({date_wid} / 10000) * 100 - FLOOR(A.FROM_DATE_WID / 100) + FLOOR(A.FROM_DATE_WID / 10000) * 100) MOB,
    NVL(A.OVERDUE_DAYS, 0) CURRENT_DPD
    FROM F88DWH.W_LOAN_DAILY_F A
    LEFT JOIN (SELECT DISTINCT CUSTOMER_WID, CUSTOMER_CODE FROM F88DWH.VW_W_CUSTOMER_D) B ON A.CUSTOMER_WID = B.CUSTOMER_WID
    WHERE A.DATE_WID = {date_wid} AND A.YEAR_NUM = ROUND({date_wid} / 10000, 0) AND A.MONTH_NUM = ROUND({date_wid} / 100, 0) - ROUND({date_wid} / 10000, 0)*100
    AND NVL(A.COLLATERAL_AMT, 0) != 0""".format(date_wid=date_wid)

    # read sql - E
    dd_1 = pd.read_sql(sql_str_1, connection)
    dd_2 = pd.read_sql(sql_str_2, connection)
    dd_3 = pd.read_sql(sql_str_3, connection)
    dd_4 = pd.read_sql(sql_str_4, connection)
    dd_5 = pd.read_sql(sql_str_5, connection)
    dd_6 = pd.read_sql(sql_str_6, connection)

    #upper
    dd_1.columns = dd_1.columns.str.upper()
    dd_2.columns = dd_2.columns.str.upper()
    dd_3.columns = dd_3.columns.str.upper()
    dd_4.columns = dd_4.columns.str.upper()
    dd_5.columns = dd_5.columns.str.upper()
    dd_6.columns = dd_6.columns.str.upper()
    
    # transform - T
    dd_1['LOAN_LOANMAX_RATIO'] = dd_1['LOAN_AMT'] / dd_1['LOAN_MAX_AMT']
    dd_1['LTV'] = dd_1['LOAN_AMT'] / dd_1['APPRAISAL_AMT']
    dd_1['MADE_YEAR'] = dd_1['MADE_YEAR'].astype(str)
    dd_1.loc[(dd_1['MADE_YEAR'] > str(int(np.floor(date_wid / 10000)))) | (dd_1['MADE_YEAR'] < '1980'), 'MADE_YEAR'] = np.nan
    dd_1['MADE_YEAR'] = dd_1['MADE_YEAR'].str.replace('[A-Za-z\),]', '', regex = True).astype(float)
    dd_1.loc[(dd_1['MADE_YEAR'] > int(np.floor(date_wid / 10000))) | (dd_1['MADE_YEAR'] < 1980), 'MADE_YEAR'] = np.nan
    dd_1['MADE_YEAR'] = np.floor(date_wid / 10000) - dd_1['MADE_YEAR'].astype(float)
    dd_1['MOB'] = np.floor((pd.to_datetime(date_wid, format='%Y%m%d') - pd.to_datetime(dd_1['DISBURSE_DATE_WID'], format='%Y%m%d')).dt.days / 30)
    
    # load - L
    dd_10 = dd_1.groupby([['DATE_WID', 'CUSTOMER_CODE']])['LOAN_LOANMAX_RATIO', 'LTV', 'MADE_YEAR', 'LOAN_AMT', 'MOB',
                                                        'LOAN_WID', 'TERM_FREQUENCY', 'TERM_FREQUENCY_DAYS', 'ASSET_TYPE_17'].agg(
    {
        'LOAN_LOANMAX_RATIO': ['mean', 'max'], 
        'LTV': ['mean', 'min'],
        'MADE_YEAR': 'mean',
        'LOAN_AMT': ['mean', 'max', 'sum'],
        'MOB': 'min',
        'LOAN_WID': 'nunique',
        'TERM_FREQUENCY': 'mean',
        'TERM_FREQUENCY_DAYS': 'sum',
        'ASSET_TYPE_17': 'sum',
    }).reset_index()
    ## rename
    name = dd_10.columns
    nas = []
    for na in name:
        nas.append(''.join(na))
    dd_10.columns = nas
    
    dd_20 = dd_2.groupby([['DATE_WID', 'CUSTOMER_CODE']])['COLLATERAL_AMT', 'COLLATERAL_AMT_ACTIVE', 'CIMB_FUND', 
                                                        'DISBURSE_AMT_CLOSED', 'DISBURSE_AMT', 'INTEREST_RATE',
                                                        'ASSET_TYPE_15'].agg(
    {
        'COLLATERAL_AMT': ['mean', 'max', 'min'],
        'COLLATERAL_AMT_ACTIVE': ['mean', 'max', 'min'],
        'CIMB_FUND': 'sum',
        'DISBURSE_AMT_CLOSED': 'max',
        'DISBURSE_AMT': 'mean',
        'INTEREST_RATE': 'max',
        'ASSET_TYPE_15': 'max',
    }).reset_index()
    ## rename
    name = dd_20.columns
    nas = []
    for na in name:
        nas.append(''.join(na))
    dd_20.columns = nas
    
    dd_30 = dd_3.groupby([['DATE_WID', 'CUSTOMER_CODE']]).TRA_CPV.agg('sum').reset_index()
    
    dd_40 = dd_4.groupby([['DATE_WID', 'CUSTOMER_CODE']]).DPD.agg('max').reset_index()
        
    dd_0 = dd_10.merge(dd_20, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
    .merge(dd_30, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
    .merge(dd_40, how='outer', on=['DATE_WID', 'CUSTOMER_CODE'])\
    .merge(dd_5, how='outer', on=['CUSTOMER_CODE'])
    
    dd_0['TRA_CPV_LOAN_AMT_sum_RATIO'] = dd_0['TRA_CPV'] / dd_0['LOAN_AMTsum']
    dd_0.loc[pd.isna(dd_0.TRA_CPV) & ~pd.isna(dd_0.LOAN_AMTsum), 'TRA_CPV_LOAN_AMT_sum_RATIO'] = .0
    
    dd = dd_0.merge(dd_6, how='left', on=['DATE_WID', 'CUSTOMER_CODE'])

    return dd

def score(data):
    nvar = ['DATE_WID', 'CUSTOMER_CODE', 'LOAN_WID', 'CURRENT_DPD']
    var = ['ASSET_TYPE_WID', 'LTV', 'MOB',
           'LOAN_LOANMAX_RATIOmean', 'LOAN_LOANMAX_RATIOmax', 'LTVmean', 'LTVmin',
           'MADE_YEARmean', 'MOBmin',
           'LOAN_WIDnunique', 'TERM_FREQUENCYmean', 'TERM_FREQUENCY_DAYSsum',
           'ASSET_TYPE_17sum', 'CIMB_FUNDsum', 'INTEREST_RATEmax',
           'ASSET_TYPE_15max', 'DPD', 'MAX_DPD_EVR',
           'TRA_CPV_LOAN_AMT_sum_RATIO']
    svar = ['LOAN_AMTmean', 'LOAN_AMTmax', 'LOAN_AMTsum', 'COLLATERAL_AMTmean', 'COLLATERAL_AMTmax',
            'COLLATERAL_AMTmin', 'COLLATERAL_AMT_ACTIVEmean', 'COLLATERAL_AMT_ACTIVEmax', 'COLLATERAL_AMT_ACTIVEmin', 
            'DISBURSE_AMT_CLOSEDmax', 'DISBURSE_AMTmean', 'TRA_CPV', ]

    # split 15/17
    dd_15 = data.loc[data.ASSET_TYPE_WID == 15]
    dd_17 = data.loc[data.ASSET_TYPE_WID == 17]

    # scaler
    scaler_15 = load('scaler_15.joblib')
    scaler_17 = load('scaler_17.joblib')

    X = pd.concat((
        pd.DataFrame(scaler_15.transform(dd_15[svar]), columns=svar, index=dd_15.index)\
            .merge(dd_15[nvar + var], left_index=True, right_index=True),
            pd.DataFrame(scaler_17.transform(dd_17[svar]), columns=svar, index=dd_17.index)\
                .merge(dd_17[nvar + var], left_index=True, right_index=True)
                ))

    X.fillna(-9, inplace=True)
    X.loc[X.LTV == np.inf, 'LTV'] = X.loc[X.LTV != np.inf, 'LTV'].mean()

    xgb_model = load('model_clf_15.joblib')
    proba = xgb_model.predict_proba(X.loc[:, lambda x: ~x.columns.isin(nvar)])[:,1]

    X = X[nvar].assign(SCORE = proba)
    X.loc[X.CURRENT_DPD > 0, 'SCORE'] = 1.
    
    X = X.groupby(['DATE_WID', 'CUSTOMER_CODE'])[['CURRENT_DPD', 'SCORE']].agg('max').reset_index()

    X['RISK_GROUP'] = pd.cut(X.SCORE, bins=[0, 0.00114, 0.00225, 0.00372, 1], labels=['Thap', 'Trung binh', 'Cao', 'Rat cao'])
    
    X.to_csv(os.path.join(ORIGIN_DIR,'Data/'+str(date_wid)+'_result.csv'), index=False)

    return X
    
def main():
    df = ETL(20240123)
    X = score(df)
#    df = up2db(20230523)
if __name__ == "__main__":
    main()

