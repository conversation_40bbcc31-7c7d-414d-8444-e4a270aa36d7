import airflow
from datetime import timedelta,datetime

import pyarrow
from airflow import DAG
from airflow.utils.dates import days_ago
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator

import pandas
import numpy as np
from joblib import load
from sqlalchemy import create_engine
from configparser import ConfigParser
from airflow.operators.bash import BashOperator
from airflow.models import Variable

import sys
import os
from airflow.operators.email import EmailOperator
from airflow.utils.trigger_rule import TriggerRule

#ETL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../ETL'))
#MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../models'))

ETL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'src/Case/Bscore'))
MODEL_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), 'src/Case/Bscore/models'))
sys.path.append(ETL_DIR)
sys.path.append(MODEL_DIR)
venv_path = Variable.get("VENV_AIRFLOW")

from etl import ETL
from score import score
from up2db import up2db

default_args = {
    'owner': 'F88-DE',
    # "email": ["<EMAIL>"],
    # "email_on_failure": True,
    # "email_on_retry": True,
    "retries": 3,
    "retry_delay": timedelta(minutes=2),
}
tags = ['score']
dag_spark = DAG(
        dag_id = "B_SCORE_ETL_Version3",
        default_args=default_args,
        schedule_interval='15 7 * * *',
        dagrun_timeout=timedelta(minutes=60),
        description='run bscore in airflow',
        start_date=datetime(2025, 1, 6),
        #tz="Asia/Ho_Chi_Minh",
        catchup=False,
        tags=tags
)
start = EmptyOperator(task_id='Start')

date_wid =  (datetime.today()-timedelta(days = 1)).strftime("%Y%m%d")

#file1 = open('/home/<USER>/sourcecode/bscore/ETL/date_wid.txt', 'w')
file1 = open(os.path.abspath(os.path.join(os.path.dirname(__file__), 'date_wid.txt')), "w")

file1.write(date_wid)
file1.close()


t3 = PythonOperator(
    task_id='run_etl',
    python_callable=ETL,
    dag=dag_spark,
    op_kwargs={'date_wid':date_wid},
    trigger_rule=TriggerRule.ALL_SUCCESS
    ,do_xcom_push=False
    #provide_context=True
)

#t4 = PythonOperator(
#    task_id='run_score',
#    python_callable=score,
#    dag=dag_spark,
#    op_kwargs={'date_wid':date_wid},
#    trigger_rule=TriggerRule.ALL_SUCCESS
#    ,do_xcom_push=False
    #provide_context=True
#)
t4=bash_task = BashOperator(
    task_id='run_bash_command',
    bash_command=f"source {venv_path} && python {ETL_DIR}/score.py",
    dag=dag_spark,
)
t5 = PythonOperator(
    task_id='run_up2db',
    python_callable=up2db,
    dag=dag_spark,
    op_kwargs={'date_wid':date_wid}, # tomorrow_ds_nodash yesterday_ds_nodash
    trigger_rule=TriggerRule.ALL_SUCCESS
    ,do_xcom_push=False
    #provide_context=True
)


#email = EmailOperator(
#    task_id='send_email_success',
#    to='<EMAIL>',
#    subject='Alert Bscore wworkflow !',
#    html_content=""" <h3>Success!!!</h3> """,
#    trigger_rule=TriggerRule.ALL_SUCCESS,
#    dag=dag_spark
#    ) 
end = EmptyOperator(task_id='End')

start >> t3 >> t4 >> t5  >> end #>> end