SELECT
	po.PAWN_ONLINE_WID AS "MÃ ĐƠN POL",
	po.SHOP_NAME AS PGD,
	po.SHOP_REV_DT AS "NGÀY ĐƠN CHUYỂN PGD",
	PO.NHOM_NGUON AS "NHÓM NGUỒN",
	PO.NGUON_PHU AS "NGUỒN PHỤ",
	PO.LST_STR_STATUS_CONTENT AS "TRẠNG THÁI CUỐI",
	COUNT(DISTINCT(SUBSTR(PR.CREATED_DT,0,8))) AS "SỐ NGÀY PGD GỌI"
FROM W_PAWN_ONLINE_TLS_F po
LEFT JOIN W_ONLINE_PROCESS_F pr ON po.PAWN_ONLINE_WID = pr.INT_POL_PAWN_ID
WHERE FORM_CREATED_DT >= '1-NOV-22' AND STATUS = 2
GROUP BY
	po.SHOP_NAME,
	PO.NHOM_NGUON,
	PO.NGUON_PHU,
	po.PAWN_ONLINE_WID,
	po.SHOP_REV_DT,
	PO.LST_STR_STATUS_CONTENT
ORDER BY PO.SHOP_NAME ASC