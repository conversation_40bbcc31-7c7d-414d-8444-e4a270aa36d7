import datetime
import logging
import os
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from Provider.OracleProvider.operators.GetDataOperators import GetDataOracleOperator
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'XLN_WEEKLY_EARLY_MONTH'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
description = 'Báo cáo Data XLN đầu tháng weekly - TuyenDN'
tags = ['xln', 'weekly']
department = 'Project - XLN'
file_name = 'EARLY_MONTH'
report_name = 'Data XLN đầu tháng'
overwrite = False
freq = None
sql, saved_folder = set_kwargs(parent_dir, file_name)
_type = 'Data Warehouse to SharePoint - Report F88'


def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date - datetime.timedelta(1)
    DATE_WID = time.strftime("%Y%m%d")
    DATE_ = time.strftime("%d-%b-%y")
    YEAR_NUM, MONTH_NUM = DATE_WID[:4], DATE_WID[4:6]
    params = {}
    params.update({'YEAR_NUM': YEAR_NUM, 'MONTH_NUM': MONTH_NUM, 'DATE_': DATE_})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)

    extract = GetDataOracleOperator(task_id=f'extract_data_from_dwh_{file_name}',
                                    oracle_conn_id='oracle_f88_dwh',
                                    sql=sql,
                                    saved_folder=saved_folder,
                                    file_name=file_name,
                                    report_name=report_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    )

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> set_params >> extract >> load >> end
