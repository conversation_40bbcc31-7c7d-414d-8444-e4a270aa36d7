select to_Date(t.date_wid, 'rrrr-mm-dd') as "<PERSON>ày xuất",
       t.loan_wid,
       t.integration_id,
       c.customer_code as CifCode,
       t.loan_code as "Contract_code",
       to_Date(t.from_date_wid, 'rrrr-mm-dd') as FromDate,
       t.to_date as ToDate,
       c.customer_nm as "Tên khách hàng",
       a.asset_tp_nm as "Loại tài sản",
       l.loan_asset_nm as "Tài sản",
       s.shop_code as ShopCode,
       s.shop_nm as ShopName,
       case
         when t.overdue_days >= 15 then
          lc.province_nm
         else
          null
       end as "Tỉnh",
       case
         when t.overdue_days >= 15 then
          lc.district_nm
         else
          null
       end as "Quận/Huyện",
       case
         when t.overdue_days >= 15 then
          lc.ward_nm
         else
          null
       end as "Phường/xã",
       case
         when t.overdue_days >= 15 then
          lc.cus_address
         else
          null
       end as "Địa chỉ",
       round(t.principal_remain) "MoneyCurrent",
       den_han.to_date as "<PERSON>ịch đến hạn",
       round(den_han.principal_amt) as "<PERSON>ốc đến hạn",
       round(den_han.interest_amt + den_han.interest_vat) as "Lãi đến hạn",
       t.overdue_days as "Số ngày quá hạn",
       round(last_schedule.principal_amt) as "Gốc kỳ xa nhất",
       round(last_schedule.interest_amt + last_schedule.interest_vat) as "Lãi kỳ xa nhất",
       CASE
            WHEN to_Date(t.from_date_wid, 'rrrr-mm-dd') < '16-feb-22'
                THEN
                     CASE
                        WHEN t.overdue_days = 1 THEN
                          1e5
                        WHEN t.overdue_days = 2 THEN
                          2e5
                        WHEN t.overdue_days >= 3 THEN
                          3e5
                       END
            ELSE
                0.08 * (t.principal_remain)
       END "Phí phạt",
       ar.region as "Miền",
       l.appraisal_amt as "Giá thẩm định",
       pk.package_nm Package_code,
       t.status
  from w_loan_daily_f t
  left outer join (select *
                     from (select row_number() over(partition by t.loan_wid order by t.paper_id, t.integration_id) rank_,
                                  t.loan_wid,
                                  t.paper_id,
                                  p.province_nm,
                                  p2.district_nm,
                                  p3.ward_nm,
                                  t.cus_address
                             from w_loan_customer_f t
                             left outer join w_province_d p
                               on t.province_wid = p.province_wid
                             left outer join w_district_d p2
                               on t.district_wid = p2.district_wid
                             left outer join w_ward_d p3
                               on t.ward_wid = p3.ward_wid
                               where t.loan_wid is not null
                           ) a
                    where a.rank_ = 1) LC
    on T.loan_wid = LC.loan_wid
  left outer join w_customer_d c
    on t.customer_wid = c.customer_wid
  left outer join w_asset_tp_d a
    on t.asset_type_wid = a.asset_tp_wid
  left outer join (SELECT
    *
FROM
    (
    SELECT
    	LS.loan_asset_nm,
        LS.LOAN_WID ,
        LS.APPRAISAL_AMT ,
        LS.ATTR_AMT ,
        LS.CREATE_DT ,
        RANK () OVER(PARTITION BY LS.LOAN_WID ORDER BY LS.CREATE_DT DESC) RANK_
    FROM
        W_LOAN_ASSET_F LS
    WHERE
        1 = 1
        AND LS.STATUS = 1
        AND LS.loan_asset_code IS NOT NULL
        AND LS.SOURCE = 'LOS'
   ) LS2
WHERE
    LS2.RANK_ = 1) l
    on t.loan_wid = l.loan_wid
  left outer join w_shop_d s
    on t.shop_wid = s.shop_wid
  left outer join w_area_manager_d ar
    on s.shop_code = trim(ar.shop_id)
  left outer join w_package_d pk
    on t.package_wid = pk.package_wid
  left outer join (select t.loan_wid,
                          t.loan_code,
                          t.from_date,
                          t.to_date,
                          t.principal_amt,
                          t.interest_amt,
                          t.interest_vat,
                          rank() over(partition by t.loan_wid order by t.to_date) rank_
                     from w_loan_sch_dtl_f t
                    where 1 = 1
                      and t.completed_paid_date is null
                      and t.to_date >=
                          trunc(to_date(:DATE_), 'MM')
                      and t.to_date <= trunc(last_day(:DATE_))) den_han
    on t.loan_wid = den_han.loan_wid
  left outer join (
                   ----- Quán hạn kỳ xa nhất -----
                   select *
                     from (select t.loan_wid,
                                   t.loan_code,
                                   t.from_date,
                                   t.to_date,
                                   t.principal_amt,
                                   t.interest_amt,
                                   t.interest_VAT,
                                   rank() over(partition by t.loan_wid order by t.to_date) rank_
                              from w_loan_sch_dtl_f t
                             where 1 = 1
                               and t.completed_paid_date is null
                               and t.to_date <= to_date(:DATE_)) t2
                    where t2.rank_ = 1) last_schedule
    on t.loan_wid = last_schedule.loan_wid
 where 1 = 1
   and a.asset_tp_code in ('00000017', '00000015')
   and t.year_num = :YEAR_NUM
   and t.month_num = :MONTH_NUM
   and to_Date(t.date_wid, 'rrrr-mm-dd') = :DATE_
--   and t.loan_wid = 5528843