select a.<PERSON> ,
	a.<PERSON> ,
	a.<PERSON> ,
	b.<PERSON> ,
	a.<PERSON>
from Pawn a
left join PawnDisburstHistory b
on a.id =b.PawnId
where date(a.CreatedDate)= :date_1
and a.PawnAssetName like '%Ô tô%'
-- split
select b.<PERSON> ,
	b.Extend1 ,
	b.Extend2 ,
	b.Extend3 ,
	case when a.Status =1 then 'chưa đăng ký'
	     when a.Status =2 then 'đã đăng ký'
	     when a.Status =3 then 'điều chỉnh'
	     when a.Status =-1 then 'xóa'
	end Status_
from estore.AssetSecuredTransactions a
left join  estore.Asset b
on a.AssetCode = b.WarehouseAssetCode
where date(a.CreatedDate)=:date_1 and
b.AssetName like '%Ô tô%'
and a.Status in (1,-1)
