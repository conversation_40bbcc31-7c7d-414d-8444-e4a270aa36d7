import datetime
from datetime import date , timedelta
import logging
import os
import pandas as pd
from UliPlot.XLSX import auto_adjust_xlsx_column_width
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.microsoft.mssql.hooks.mssql import MsSqlHook
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'VH_CHIHO_BANKTRANSCTIONS'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
description = 'Báo cáo tài khoản KH'
tags = ['vh', 'realtime']
department = 'Project - Vận hành'
file_name = 'CHIHO_BANKTRANSACTIONS'
report_name = '<PERSON><PERSON><PERSON> cáo thông tin TK KH'
overwrite = True
freq = None
_type = 'Data Warehouse to SharePoint - Report F88'
sql_temp, saved_folder = set_kwargs(parent_dir, file_name)
if not os.path.exists(os.path.join(saved_folder, report_name)):
    os.makedirs(os.path.join(saved_folder, report_name))
chiho_conn_id = 'mysql_chiho'


def extract_data(ds, **kwargs):
    chiho_hook = MySqlHook(mysql_conn_id=chiho_conn_id)
    df_chiho = chiho_hook.get_pandas_df(sql=sql)
    logging.info('Query data from LOS successfully!')
    logging.info(f'LOS has {df_los.shape[0]}')

time = date.today() - timedelta(1)


DATE_WID = time.strftime("%Y%m%d")


def extract_data(ds, **kwargs):
    chiho_hook = MySqlHook(mysql_conn_id=chiho_conn_id)
    df_chiho = chiho_hook.get_pandas_df(sql=sql_temp)
    logging.info('Query data from LOS successfully!')
    logging.info(f'LOS has { df_chiho.shape[0]}')
    logging.info(f'LOS has {df_chiho.shape[0]}')
    df_chiho.columns = ['TransactionCode', 'TransactionType', 'TransactionDate', 'ShopCode', 'ShopName', 'ContractNumber', 'BenName', 'BenBankName', 'BenAccount']
    writer = pd.ExcelWriter(os.path.join(saved_folder, report_name, file_name + '.xlsx'), engine='xlsxwriter')
    df_chiho.to_excel(writer, index=False, sheet_name='Sheet1')
    auto_adjust_xlsx_column_width(df_chiho, writer, sheet_name='Sheet1', margin=0, index=False)
    writer.save()

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    extract = PythonOperator(task_id='Extract_Data_from_CHIHO', python_callable=extract_data)

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> extract >> load >> end
