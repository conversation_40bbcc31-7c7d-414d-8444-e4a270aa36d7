import datetime
import logging
import os
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import Python<PERSON>perator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.microsoft.mssql.hooks.mssql import Ms<PERSON>qlHook
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs
parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'VH_REALTIME_CONTRACT_LS'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 9,13,16,19 * * *'
#schedule_interval = None
description = 'Báo cáo dư nợ VH realtime - ManhLV'
tags = ['vh', 'realtime']
department = 'Project - Vận hành'
file_name = 'CONTRACT_LS'
report_name = 'Báo cáo Danh sách HĐ mới phát sinh'
overwrite = True
freq = None
_type = 'Data Warehouse to SharePoint - Report F88'
sql_temp, saved_folder = set_kwargs(parent_dir, file_name)
if not os.path.exists(os.path.join(saved_folder, report_name)):
    os.makedirs(os.path.join(saved_folder, report_name), exist_ok=True)
los_conn_id = 'mysql_los'
pos_conn_id = 'mssql_pos'
sql_ls = sql_temp.split('--split')
sql_los = """select  a.CodeNo ,
   a.PawnStatusName ,
   case when a.CreatedDate is not null then DATE_FORMAT(a.CreatedDate, '%Y-%m-%d') end CreatedDate,
   case when a.DisburseDate is not null then DATE_FORMAT(a.DisburseDate, '%Y-%m-%d') end   DisburseDate,
   a.ShopName ,
   a.LoanPackageName ,
       CaSE
		WHEN a.FundSource='05' THEN 'CIMB Fund'
		WHEN a.FundSource='01' THEN 'F88 Fund'
       END AS FundSource
from Pawn a
left join PawnDisburstHistory b
on a.id =b.PawnId
where year(a.CreatedDate)>=2023
and a.PawnAssetName like '%Ô tô%'"""
sql_pos = """select b.CodeNo ,
	b.Extend1 ,
	b.Extend2 ,
	b.Extend3 ,
	case when a.Status =1 then 'chưa đăng ký'
	     when a.Status =2 then 'đã đăng ký'
	     when a.Status =3 then 'điều chỉnh'
	     when a.Status =-1 then 'xóa'
	end Status_
from estore.AssetSecuredTransactions a 
left join  estore.Asset b 
on a.AssetCode = b.WarehouseAssetCode 
where YEAR(b.CreateDate)>=2023 and 
b.AssetName like '%Ô tô%'
and a.Status in (1,-1)"""

def extract_data(ds, **kwargs):
    los_hook = MySqlHook(mysql_conn_id=los_conn_id)
    los_df = los_hook.get_pandas_df(sql=sql_los)
    pos_hook = MsSqlHook(mssql_conn_id=pos_conn_id)
    pos_df = pos_hook.get_pandas_df(sql=sql_pos)
    data_df = pos_df.merge(los_df, on='CodeNo', how='left')
    data_df.columns = ('Ma_HD','Bien_so','So_khung','So_may','Trang_thai_GDĐB','Trang_thai_HD','Ngay_tao','Ngay_GN','PGD', 'Ten_Goi_Vay', 'Nguon_Von')
    data_df.to_excel(os.path.join(saved_folder, report_name, file_name + '.xlsx'), sheet_name='sheet name', index=False)
    # column = [('Ma_HD','Bien_so','So_khung','So_may','Trang_thai_GDĐB','Trang_thai_HD','Ngay_tao','Ngay_GN','PGD')]
    # data = column + list(data_df.itertuples(index=False))
    # wb = Workbook()
    # wb.new_sheet("sheet name", data=data)
    # wb.save(os.path.join(saved_folder, report_name, file_name + '.xlsx'))

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    extract = PythonOperator(task_id='Extract_Data_from_LOS_and_MIFOS', python_callable=extract_data)

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> extract >> load >> end
