select
	T4.KENH,
	case when HS_PENDING is null then 0 else HS_PENDING end "Số HĐ",
	case when SL_CV is null then 0 else SL_CV end "Số lượng CV TDTD",
	case when HS_PENDING is null or SL_CV is null then 0 else round(HS_PENDING/SL_CV,1) end "PENDING/CV",
	hs_oldest "Hs cũ nhất" ,
	total_t "HĐ tiếp nhận ngày t",
	approve_t "HĐ duyệt ngày t ",
	concat(CONVERT(round(per_t*100,1),char),'%') "%Phê duyệt ngày t",
	total_t1 "HĐ tiếp nhận ngày t-1",
	approve_t1 "HĐ duyệt ngày t-1",
	concat(CONVERT(round(per_t1*100,1),char),'%') "%Phê duyệt ngày t-1s"
	from
(
	SELECT
		Z.KENH,COUNT(1) HS_PENDING, min(createddate) hs_oldest
	FROM
	(select
		case
			when LoanPackageName in
			('CIMB_DKXM_Laodongtudo'
			,'F88_DKXM_Laodongtudo'
			,'CIMB_DKXM_Congnhan'
			,'F88_DKXM_Congnhan'
			,'CIMB_DKXM_Vienchuc_VP'
			,'F88_DKXM_Vienchuc_VP'
			,'CIMB_DKXM_Taixecongnghe'
			,'F88_DKXM_Taixecongnghe'
			,'CIMB_DKXM_Tieuthuong'
			,'F88_DKXM_Tieuthuong'
			,'CIMB_DKXM_miencavet'
		) THEN  'ML'
		WHEN LoanPackageName IN
		(
			'DKXM_Vay tiền mặt mua hàng_MWG',
			'DKXM_Vay tiền mặt siêu tốc_MWG',
			'DKXM_VMH_MWG'
		) THEN  'MWG'
		WHEN LoanPackageName = 'F88_DKXM_E2E' THEN  'E2E'
		WHEN LoanPackageName = 'Online lending - App vay' THEN  'APPVAY'
		WHEN LoanPackageName = 'App vay_MB' THEN  'APPVAY_MB'
		WHEN KENH = 'DKOT' THEN 'OTO'
		WHEN LoanPackageName = 'DKXM_Direct Sale' THEN 'DRS'
		END KENH,
		createddate
	from
	(
	    select a.createddate ,b.CodeNo, b.CustomerName,b.LoanPackageName, b.ShopName ,  b.CategoryName ,b.PawnAssetName,  b.pawnstatuscode, b.PriceLoan,
	case
			when b.loanpackagename like '%E2E%' then 'E2E'
			when b.loanpackagename in (
				'F88_DKXM_Laodongtudo',
				'F88_DKXM_Tieuthuong',
				'F88_DKXM_Congnhan',
				'F88_DKXM_Vienchuc_VP',
				'F88_DKXM_Taixecongnghe'
				)
			or b.LoanPackageName in (
				'CIMB_ DKXM_Vienchuc_VP',
				'CIMB_DKXM_Tieuthuong',
				'CIMB_DKXM_Laodongtudo',
				'CIMB_DKXM_Taixecongnghe',
				'CIMB_DKXM_Congnhan',
				'CIMB_DKXM_Vienchuc_VP',
				'CIMB_DKXM_miencavet') then 'DKXM'
			when b.loanpackagename like '%DKOT%' or b.LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO') then 'DKOT'
			when b.LoanPackageName in ('DKXM_Direct Sale','DKXM_FMCG','DKXM_VMH_MWG','DKXM_Vay tiền mặt siêu tốc_MWG','Online lending - App vay','DKXM_Vay tiền mặt mua hàng_MWG','App vay_MB') then 'AGENT'
		end as kenh
	    from PawnStateWorkFlow a
	    left join Pawn  b on a.PawnId = b.id
	    where 1=1
		and `Role` like '%TDTD%'
	    and a.ModifiedDate is null
	    and a.PawnId not in (select pawnid from PawnStateWorkFlow where ActionOfUser in ('approve','disapprove','cancel'))
	    and a.createddate >= subdate(CURDATE() , 1)
	    order by a.createddate desc
	)x
	where x.pawnstatuscode is null
	)Z
	GROUP BY Z.KENH
) T1
LEFT JOIN
(
	select z.kenh, COUNT(DISTINCT ModifiedUserName) SL_CV
	from
	(SELECT
		case
			when LoanPackageName in
			('CIMB_DKXM_Laodongtudo'
			,'F88_DKXM_Laodongtudo'
			,'CIMB_DKXM_Congnhan'
			,'F88_DKXM_Congnhan'
			,'CIMB_DKXM_Vienchuc_VP'
			,'F88_DKXM_Vienchuc_VP'
			,'CIMB_DKXM_Taixecongnghe'
			,'F88_DKXM_Taixecongnghe'
			,'CIMB_DKXM_Tieuthuong'
			,'F88_DKXM_Tieuthuong'
			,'CIMB_DKXM_miencavet'
		) THEN  'ML'
		WHEN LoanPackageName IN
		(
			'DKXM_Vay tiền mặt mua hàng_MWG',
			'DKXM_Vay tiền mặt siêu tốc_MWG',
			'DKXM_VMH_MWG'
		) THEN  'MWG'
		WHEN LoanPackageName = 'F88_DKXM_E2E' THEN  'E2E'
		WHEN LoanPackageName = 'Online lending - App vay' THEN  'APPVAY'
		WHEN LoanPackageName = 'App vay_MB' THEN  'APPVAY_MB'
		WHEN loanpackagename like '%DKOT%' or LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO')then 'OTO'
		WHEN LoanPackageName = 'DKXM_Direct Sale' THEN 'DRS'
		end KENH,
		p.LoanPackageName ,
		`Role` ,
		pswf.ModifiedDate ,
		pswf.ActionOfUser ,
		pswf.ModifiedUserName
	FROM PawnStateWorkFlow pswf
	LEFT JOIN Pawn p ON pswf.PawnId = p.Id
	WHERE
	(p.LoanPackageName IN
	(
		'CIMB_DKXM_Laodongtudo'
		,'F88_DKXM_Laodongtudo'
		,'CIMB_DKXM_Congnhan'
		,'F88_DKXM_Congnhan'
		,'CIMB_DKXM_Vienchuc_VP'
		,'F88_DKXM_Vienchuc_VP'
		,'CIMB_DKXM_Taixecongnghe'
		,'F88_DKXM_Taixecongnghe'
		,'CIMB_DKXM_Tieuthuong'
		,'F88_DKXM_Tieuthuong'
		,'CIMB_DKXM_miencavet'
		,'DKXM_Vay tiền mặt mua hàng_MWG'
		,'DKXM_Vay tiền mặt siêu tốc_MWG'
		,'DKXM_VMH_MWG'
		,'F88_DKXM_E2E'
		,'Online lending - App vay'
		,'App vay_MB'
		,'DKXM_Direct Sale'
	)
	or loanpackagename like '%DKOT%' or LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO')
	)
	AND pswf.`Role` like '%TDTD%'
	and pswf.ModifiedDate is not null
	AND pswf.ModifiedDate >= DATE_SUB(NOW(),INTERVAL 30 minute)
	)z
	group by z.kenh
) T3 ON T1.KENH = T3.KENH
right join
(
select
	kenh,
	sum(total_t) total_t,
	sum(approve_t) approve_t,
	sum(approve_t)/sum(total_t) per_t,
	sum(total_t1) total_t1,
	sum(approve_t1) APPROVE_T1,
	sum(approve_t1)/sum(total_t1) PER_t1
from
	(
	select
	kenh,
	case when dt = 't' then total end total_t,
	case when dt = 't' then approve end approve_t,
	-- case when dt = 't' then per end per_t,
	case when dt = 't-1' then total end total_t1,
	case when dt = 't-1' then approve end approve_t1
	-- case when dt = 't-1' then per end per_t1
	from
	(
	select
	kenh , dt, count(distinct codeno) total, sum(approve) approve, sum(approve)/count(1) per
	-- distinct(actionofuser)
	from
		(SELECT
		distinct
				case
					when LoanPackageName in
					('CIMB_DKXM_Laodongtudo'
					,'F88_DKXM_Laodongtudo'
					,'CIMB_DKXM_Congnhan'
					,'F88_DKXM_Congnhan'
					,'CIMB_DKXM_Vienchuc_VP'
					,'F88_DKXM_Vienchuc_VP'
					,'CIMB_DKXM_Taixecongnghe'
					,'F88_DKXM_Taixecongnghe'
					,'CIMB_DKXM_Tieuthuong'
					,'F88_DKXM_Tieuthuong'
					,'CIMB_DKXM_miencavet'
				) THEN  'ML'
				WHEN LoanPackageName IN
				(
					'DKXM_Vay tiền mặt mua hàng_MWG',
					'DKXM_Vay tiền mặt siêu tốc_MWG',
					'DKXM_VMH_MWG'
				) THEN  'MWG'
				WHEN LoanPackageName = 'F88_DKXM_E2E' THEN  'E2E'
				WHEN LoanPackageName = 'Online lending - App vay' THEN  'APPVAY'
				WHEN LoanPackageName = 'App vay_MB' THEN  'APPVAY_MB'
				WHEN loanpackagename like '%DKOT%' or LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO')then 'OTO'
				WHEN LoanPackageName = 'DKXM_Direct Sale' THEN 'DRS'
				end KENH,
				p.codeno,
				case when date(pswf.ModifiedDate) = CURDATE() then 't' else 't-1' end "dt",
				case when pswf.ActionOfUser = 'approve' then 1 else 0 end approve
			FROM PawnStateWorkFlow pswf
			LEFT JOIN Pawn p ON pswf.PawnId = p.Id
			WHERE
			(p.LoanPackageName IN
			(
				'CIMB_DKXM_Laodongtudo'
				,'F88_DKXM_Laodongtudo'
				,'CIMB_DKXM_Congnhan'
				,'F88_DKXM_Congnhan'
				,'CIMB_DKXM_Vienchuc_VP'
				,'F88_DKXM_Vienchuc_VP'
				,'CIMB_DKXM_Taixecongnghe'
				,'F88_DKXM_Taixecongnghe'
				,'CIMB_DKXM_Tieuthuong'
				,'F88_DKXM_Tieuthuong'
				,'CIMB_DKXM_miencavet'
				,'DKXM_Vay tiền mặt mua hàng_MWG'
				,'DKXM_Vay tiền mặt siêu tốc_MWG'
				,'DKXM_VMH_MWG'
				,'F88_DKXM_E2E'
				,'Online lending - App vay'
				,'App vay_MB'
				,'DKXM_Direct Sale'
			)
			or loanpackagename like '%DKOT%' or LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO')
			)
			AND pswf.`Role` like '%TDTD%'
			and pswf.ModifiedDate is not null
			and pswf.ModifiedDate >= SUBDATE(CURDATE(),1)
		-- 	AND pswf.ModifiedDate >= DATE_SUB(NOW(),INTERVAL 1 HOUR)
		) x
	group by dt, kenh
	)
	y
)z
group by kenh
) T4 ON T1.KENH=T4.KENH
left join
(
select max(z.ModifiedDate) LAST_HOUR, z.kenh
	from
	(SELECT
		case
			when LoanPackageName in
			('CIMB_DKXM_Laodongtudo'
			,'F88_DKXM_Laodongtudo'
			,'CIMB_DKXM_Congnhan'
			,'F88_DKXM_Congnhan'
			,'CIMB_DKXM_Vienchuc_VP'
			,'F88_DKXM_Vienchuc_VP'
			,'CIMB_DKXM_Taixecongnghe'
			,'F88_DKXM_Taixecongnghe'
			,'CIMB_DKXM_Tieuthuong'
			,'F88_DKXM_Tieuthuong'
			,'CIMB_DKXM_miencavet'
		) THEN  'ML'
		WHEN LoanPackageName IN
		(
			'DKXM_Vay tiền mặt mua hàng_MWG',
			'DKXM_Vay tiền mặt siêu tốc_MWG',
			'DKXM_VMH_MWG'
		) THEN  'MWG'
		WHEN LoanPackageName = 'F88_DKXM_E2E' THEN  'E2E'
		WHEN LoanPackageName = 'Online lending - App vay' THEN  'APPVAY'
		WHEN LoanPackageName = 'App vay_MB' THEN  'APPVAY_MB'
		WHEN loanpackagename like '%DKOT%' or LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO')then 'OTO'
		WHEN LoanPackageName = 'DKXM_Direct Sale' THEN 'DRS'
		end KENH,
		p.LoanPackageName ,
		`Role` ,
		pswf.ModifiedDate ,
		pswf.ActionOfUser ,
		pswf.ModifiedUserName
	FROM PawnStateWorkFlow pswf
	LEFT JOIN Pawn p ON pswf.PawnId = p.Id
	WHERE
	(p.LoanPackageName IN
	(
		'CIMB_DKXM_Laodongtudo'
		,'F88_DKXM_Laodongtudo'
		,'CIMB_DKXM_Congnhan'
		,'F88_DKXM_Congnhan'
		,'CIMB_DKXM_Vienchuc_VP'
		,'F88_DKXM_Vienchuc_VP'
		,'CIMB_DKXM_Taixecongnghe'
		,'F88_DKXM_Taixecongnghe'
		,'CIMB_DKXM_Tieuthuong'
		,'F88_DKXM_Tieuthuong'
		,'CIMB_DKXM_miencavet'
		,'DKXM_Vay tiền mặt mua hàng_MWG'
		,'DKXM_Vay tiền mặt siêu tốc_MWG'
		,'DKXM_VMH_MWG'
		,'F88_DKXM_E2E'
		,'Online lending - App vay'
		,'App vay_MB'
		,'DKXM_Direct Sale'
	)
	or loanpackagename like '%DKOT%' or LoanPackageName in ('CIMB_DKOT_KHCN','Cầm cố OTO')
	)
	AND pswf.`Role` like '%TDTD%'
	and pswf.ModifiedDate is not null
-- 	AND pswf.ModifiedDate >= DATE_SUB(NOW(),INTERVAL 1 HOUR)
	)z
	group by z.kenh
) T2 ON T4.KENH = T2.KENH