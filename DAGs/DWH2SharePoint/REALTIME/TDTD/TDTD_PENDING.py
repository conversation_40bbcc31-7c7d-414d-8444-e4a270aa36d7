import datetime
import logging
import os
import pandas as pd
from UliPlot.XLSX import auto_adjust_xlsx_column_width
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'TDTD_REALTIME_PENDING'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None
description = 'báo cáo TDTD pending real-time - SonTB2'
tags = ['tdtd', 'realtime']
department = 'Project - Thẩm định tín dụng'
file_name = 'TDTD_PENDING'
report_name = 'realtime'
overwrite = True
freq = None
sql, saved_folder = set_kwargs(parent_dir, file_name)
if not os.path.exists(os.path.join(saved_folder, report_name)):
    os.makedirs(os.path.join(saved_folder, report_name))
los_conn_id = 'mysql_los'
_type = 'Data Warehouse to SharePoint - Report F88'


def extract_data(ds, **kwargs):
    los_hook = MySqlHook(mysql_conn_id=los_conn_id)
    df_los = los_hook.get_pandas_df(sql=sql)
    logging.info('Query data from LOS successfully!')
    logging.info(f'LOS has {df_los.shape[0]}')
    df_los.columns = ['Channel', 'So_HD', 'CV_TDTD', 'PENDING_CV', 'HS_OLDEST', 'HD_TN_NGAY_T', 'HT_DUYET_NGAYT', '%PHEDUYET_NGAYT', 'HD_TN_NGAY_T-1', 'HT_DUYET_NGAYT-1', '%PHEDUYET_NGAYT-1']
    writer = pd.ExcelWriter(os.path.join(saved_folder, report_name, file_name + '.xlsx'), engine='xlsxwriter')
    df_los.to_excel(writer, index=False, sheet_name='Sheet1')
    auto_adjust_xlsx_column_width(df_los, writer, sheet_name='Sheet1', margin=0, index=False)
    writer.save()


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    extract = PythonOperator(task_id='Extract_Data_from_LOS', python_callable=extract_data)

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> extract >> load >> end