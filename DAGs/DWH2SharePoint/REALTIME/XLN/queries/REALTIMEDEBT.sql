with thuc_thu as
(
	(
		select # lay hd f88 co' so tien thu tu` ngay 10/01/2024
			1 AS GIAY_NHAN_NO
			, b.contract_code AS HOP_DONG_CAM_CO
			, a.amount as SO_TIEN_GD
			, a.transaction_date as THOI_GIAN_GIAO_DICH
			, a.code_description as LOAI_GIAO_DICH
			, a.contract_id
			, a.code_master_data
		
		from m_contract_transaction a
		left Join m_contract b on  a.contract_id = b.id   
		where 
			1=1
			and a.transaction_date >= DATE(DATE_FORMAT(SYSDATE(), '%Y-%m-01'))
			and a.transaction_date <= LAST_DAY(SYSDATE()) 
			and a.amount>0 
			and b.close_date is NULL 
-- 			AND b.contract_code ='31188759931'
			and a.code_master_data in 
			('NAP_TIEN_DU_HDCC','DONG_HDCC_CD','DONG_HDCC_CDNX','BAN_THANH_LY_HDCC')
	)
	
	
	
	union all 	
	
	(
		select # lay hd f88 bi close trong thang
			1 AS GIAY_NHAN_NO
			, b.contract_code AS HOP_DONG_CAM_CO
			, a.amount as SO_TIEN_GD
			, a.transaction_date as THOI_GIAN_GIAO_DICH
			, a.code_description as LOAI_GIAO_DICH
			, a.contract_id
			, a.code_master_data
		
		from m_contract_transaction a
		left Join m_contract b on  a.contract_id = b.id   
		where 
			1=1
			and a.transaction_date >= DATE(DATE_FORMAT(SYSDATE(), '%Y-%m-01'))
			and a.transaction_date <= LAST_DAY(SYSDATE()) 
			and a.amount>0 
			
			and b.close_date >= DATE(DATE_FORMAT(SYSDATE(), '%Y-%m-01'))
			and b.close_date <= LAST_DAY(SYSDATE()) 
			
-- 			AND b.contract_code ='31188759931'
			and a.code_master_data in 
			('CHUYEN_TIEN_DU_HDCC_GNN','CHUYEN_TIEN_DU_HDCC_CIF')
	)
	union all
	
	(
		select # lay hd CIMB, CIMB CHUA GOLIVE REVOLVING NEN CHUA CO HOP DONG CAM CO, HDCC VAN~ LA GIAY NHAN NO
			b.external_id AS GIAY_NHAN_NO
			, b.external_id AS HOP_DONG_CAM_CO
-- 			, d.contract_code as HOP_DONG_CAM_CO
		
			, a.amount  as SO_TIEN_GD 
			, a.transaction_date as THOI_GIAN_GIAO_DICH
			, a.action_name as LOAI_GIAO_DICH 
			, b.contract_id
			, a.action_code
		from m_loan_transaction a
		left join m_loan b  on  a.loan_id=b.id  
		where
			1=1
-- 			and b.external_id='40605218894'
			and a.transaction_date >= DATE(DATE_FORMAT(SYSDATE(), '%Y-%m-01'))
			and a.transaction_date <= LAST_DAY(SYSDATE()) 
			and b.fund_id=5 # 5 la CIMB fund
			and  a.action_code in 
			(
				'TRA_CPV','NAP_TIEN_DU_HD','TRA_CPV_CIMB','DONG_HD_CD','DONG_HD_CD_CIMB','DONG_HD_DH',
				'TRA_CPV_HDNX',
				'DONG_HD',
				'DONG_HD_CDNX',
				'TRA_CPV_CIMB_F88',
				'NAP_TIEN_DU_HD_HDNX',
				'DONG_HD_CIMB_F88_WO',
				'TRA_CPV_CIMB_F88_WO',
				'BAN_THANH_LY_HDNX',
				'DONG_HD_CIMB_F88',
				'DONG_HDNX',
				'BAN_THANH_LY_HDNX_CIMB_SMN',
				'TRA_BOT_GOC',
				'BAN_THANH_LY_CIMB_SMN',
				'BAN_THANH_LY',
				'BAN_THANH_LY_HDNX_CIMB_F88',
				'BAN_THANH_LY_CIMB_F88',
				'NAP_TIEN_DU_HD_NGUON_THANH_LY',
				'DONG_HD_CD_NGUON_THANH_LY',
				'DONG_HD_CD_CIMB_NGUON_THANH_LY',
				'TRA_CPV_CIMB_NGUON_THANH_LY',
				'DONG_HD_CD_VMH',
				'DONG_HD_CIMB'
			)
	)

)
, tien_du_dau_ngay as
(	
	SELECT DISTINCT 
		a.in_advance
		, case 
				when c.fund_id = 5 then c.external_id
				else b.contract_code
		end as contract_code
		, a.job_time_type
	from m_document_contract_ods a
	left join m_contract b on a.contract_id = b.id
	left join m_loan c on b.id= c.contract_id
	WHERE
		1=1
		and a.job_time_type=1 #1 la tien du dau ngay , 2 la tien du cuoi ngay 
-- 		and b.contract_code ='41253142105'
)
, tien_du_cuoi_ngay as
(	
	SELECT DISTINCT 
		a.in_advance
		, case 
				when c.fund_id = 5 then c.external_id
				else b.contract_code
		end as contract_code
		, a.job_time_type
	from m_document_contract_ods a
	left join m_contract b on a.contract_id = b.id
	left join m_loan c on b.id= c.contract_id
	WHERE
		1=1
		and a.job_time_type=2 #1 la tien du dau ngay , 2 la tien du cuoi ngay 
-- 		and b.contract_code ='41253142105'
)	
, TIEN_NGAY_QUA_HAN AS
(
	(
		 select 
		  		t.contract_code
-- 	            , 
-- 	            	sum((ifnull(t.principal_amount, 0) + ifnull(interest_amount, 0) + ifnull(penalty_charges_amount, 0) + ifnull(t.fee_charges_amount, 0))) -
-- 	              sum((ifnull(t.principal_completed_derived, 0) +
-- 	                  ifnull(t.interest_completed_derived, 0) +
-- 	                  ifnull(t.penalty_charges_completed_derived, 0) +
-- 	                  ifnull(t.fee_charges_completed_derived, 0))) as TIEN_QUA_HAN  
		  		, t.total_outstanding as TIEN_QUA_HAN 
	            ,max(t.over_due_days) AS SO_NGAY_QUA_HAN
	      from (
	          		select 
	          				a.contract_id
	          				, b.contract_code
	                       	, a.installment
	                       	, DATE_FORMAT(a.duedate, "%Y-%m-%d") duedate
	                       	, a.completed_derived
	                       	, a.principal_amount
	                       	, a.principal_completed_derived
	                       	, a.interest_amount
	                       	, a.interest_completed_derived
	                       	, a.penalty_charges_amount
	                       	, a.penalty_charges_completed_derived
	                       	, a.fee_charges_amount
	                       	, a.fee_charges_completed_derived
	                       	, DATEDIFF(CURRENT_DATE,a.duedate) over_due_days
	                       	, a.total_outstanding
	                       	, b.fund_id
	                from m_contract_repayment_schedule a
	                INNER join m_contract b on a.contract_id = b.id
	                where 
	                	1=1
	                	and a.completed_derived = 0
	                    and b.contract_status_id = 300
	                    and a.duedate <= sysdate()
	                    and b.fund_id<>5
	                    
	--                     and b.contract_code ='40759703914'
	                 
	            ) t
	     group by 
	     		 t.contract_code
	)
	union all 
	(
		  select 	
		  		  t.contract_code,
                  sum((ifnull(t.principal_amount, 0) +
                      ifnull(interest_amount, 0) +
                      ifnull(penalty_charges_amount, 0) +
                      ifnull(t.fee_charges_amount, 0))) -
                  sum((ifnull(t.principal_completed_derived, 0) +
                      ifnull(t.interest_completed_derived, 0) +
                      ifnull(t.penalty_charges_completed_derived, 0) +
                      ifnull(t.fee_charges_completed_derived, 0))) as TIEN_QUA_HAN ,
                     max(t.over_due_days) SO_NGAY_QUA_HAN
          from (
	          		select 
	                       b.external_id as contract_code,
	                       a.installment,
	                       DATE_FORMAT(a.duedate, "%Y-%m-%d") duedate,
	                       a.completed_derived,
	                       a.principal_amount,
	                       a.principal_completed_derived,
	                       a.interest_amount,
	                       a.interest_completed_derived,
	                       a.penalty_charges_amount,
	                       a.penalty_charges_completed_derived,
	                       a.fee_charges_amount,
	                       a.fee_charges_completed_derived,
	                       DATEDIFF(CURRENT_DATE,a.duedate) over_due_days
	                from m_loan_repayment_schedule a
	                INNER join m_loan b on a.loan_id = b.id 
	                where a.completed_derived = 0
	                     and b.loan_status_id = 300
	                     and a.duedate <= sysdate()
	                     and b.fund_id=5# 5 la` CIMB
               ) t
          group by t.contract_code
	)
     		
    
)
, DANH_SACH_HD AS
(
	select DISTINCT x.contract_code
	from
		(
			select contract_code 
			from tien_du_dau_ngay
			
			union all
			
			select HOP_DONG_CAM_CO 
			from thuc_thu
		
			union all
			
			select contract_code 
			from tien_du_cuoi_ngay
			
			union all
			
			select contract_code 
			from TIEN_NGAY_QUA_HAN
		) x
)
, ds_hd_cimb as
(
		SELECT  
			a.external_id	as code_no
			, b.contract_code 
			, a.fund_id
		from m_loan a
		left join m_contract b on a.contract_id =b.id 
		WHERE 
			1=1
			and a.external_id <> b.contract_code 
			and a.fund_id=5 # 5 la CIMB fund
)
, SUM_SO_TIEN_GD AS 
(
		SELECT 
			x.GIAY_NHAN_NO
			, x.HOP_DONG_CAM_CO
			, sum(x.SO_TIEN_GD) as SO_TIEN_GD
			, max(x.THOI_GIAN_GIAO_DICH) as THOI_GIAN_GIAO_DICH
			, x.LOAI_GIAO_DICH
		from 
		(
				SELECT 
					'.' as GIAY_NHAN_NO
					, k.contract_code AS HOP_DONG_CAM_CO
					, ifnull(a.SO_TIEN_GD, 0) as SO_TIEN_GD
					, date(ifnull(a.THOI_GIAN_GIAO_DICH, 0)) as THOI_GIAN_GIAO_DICH
					, '.' as LOAI_GIAO_DICH
				from DANH_SACH_HD k
				left join thuc_thu a on k.contract_code=a.HOP_DONG_CAM_CO
				WHERE 
					1=1
					and k.contract_code IS NOT NULL
		) x  
		GROUP by 
			x.GIAY_NHAN_NO
			, x.HOP_DONG_CAM_CO
			, x.LOAI_GIAO_DICH
)
	

	SELECT 
		x.GIAY_NHAN_NO
		, x.HOP_DONG_CAM_CO
		, x.SO_TIEN_GD
		, x.THOI_GIAN_GIAO_DICH
		, x.LOAI_GIAO_DICH
	
		, ifnull(b.in_advance, 0) as TIEN_DU_DAU_NGAY
		, ifnull(c.in_advance, 0) as TIEN_DU_CUOI_NGAY
		, ( d.TIEN_QUA_HAN- ifnull(c.in_advance, 0) ) as TIEN_QUA_HAN‌
		, d.SO_NGAY_QUA_HAN
	FROM SUM_SO_TIEN_GD x 
	left join tien_du_dau_ngay b on x.HOP_DONG_CAM_CO=b.contract_code
	left join tien_du_cuoi_ngay c on x.HOP_DONG_CAM_CO=c.contract_code
	left join TIEN_NGAY_QUA_HAN d on x.HOP_DONG_CAM_CO = d.contract_code



	


