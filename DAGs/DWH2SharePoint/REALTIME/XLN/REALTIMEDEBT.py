import datetime
import logging
import os
import pandas as pd
from UliPlot.XLSX import auto_adjust_xlsx_column_width
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs
from SendMail.SendMail import send_email

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'XLN_REALTIME_DEBT'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 8-22 * * *'
description = 'Báo cáo dư nợ XLN realtime - TuyenDN'
tags = ['xln', 'realtime']
department = 'Project - XLN'
file_name = 'REALTIMEDEBT'
report_name = 'Báo cáo dư nợ XLN'
overwrite = True
freq = None
sql_temp, saved_folder = set_kwargs(parent_dir, file_name)
if not os.path.exists(os.path.join(saved_folder, report_name)):
    os.makedirs(os.path.join(saved_folder, report_name))
sql_mifos = sql_temp#
#sql_los = ''
#los_conn_id = 'mysql_los'
mifos_conn_id = 'mysql_mifos'
_type = 'Data Warehouse to SharePoint - Report F88'
#if '-- Query Split --' in sql_temp:
    #sql_temp = sql_temp.split('-- Query Split --')
    #for s in sql_temp:
       # if 'MIFOS' in s:
         #   sql_mifos = s
       # else:
         #   sql_los = s

def extract_data(ds, **kwargs):
    mifos_hook = MySqlHook(mysql_conn_id=mifos_conn_id)
    #los_hook = MySqlHook(mysql_conn_id=los_conn_id)
    df_mifos = mifos_hook.get_pandas_df(sql=sql_mifos)
    logging.info('Query data from MIFOS successfully!')
    logging.info(f'MIFOS has {df_mifos.shape[0]}')
    #df_los = los_hook.get_pandas_df(sql=sql_los)
    #logging.info('Query data from LOS successfully!')
    #logging.info(f'LOS has {df_los.shape[0]}')
    #df_mifos.columns = ['id', 'CodeNo', 'office_id', 'SHOP_NAME', 'total_outstanding_derived', 'Money_outstanding_over',
                        #'Status', 'Money_Advance', 'Money In', 'over_due_days', 'Transaction_date']
    #df_los.columns = ['CodeNo', 'Amount']
    #result = df_mifos.merge(df_los, on='CodeNo', how='left')
    result = df_mifos
   
    #columns = ['MIFOS_ID', 'Mã Hợp Đồng', 'PGD_ID', 'Tên PGD', 'Dư nợ', 'Sô tiền đang quá hạn', 'Trạng thái HĐ',
               #'Tiền dư', 'Tiền thu', 'Số ngày quá hạn', 'Ngày giao dịch', 'Tiền treo CK']
    #result.columns = columns
    writer = pd.ExcelWriter(os.path.join(saved_folder, report_name, file_name + '.xlsx'), engine='xlsxwriter')
    result.to_excel(writer, index=False, sheet_name='Sheet1')
    logging.info('export to excel done!')
    auto_adjust_xlsx_column_width(result, writer, sheet_name='Sheet1', margin=0, index=False)
    writer.save()


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         on_failure_callback=send_email,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool',
             'retries': 3,
             'retry_delay': datetime.timedelta(minutes=1),
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    extract = PythonOperator(task_id='Extract_Data_from_LOS_and_MIFOS', python_callable=extract_data)

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> extract >> load >> end
