import datetime
import os
import logging
import calendar
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.amazon.aws.hooks.redshift_sql import RedshiftSQLHook
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs
from pyexcelerate import Workbook
from Provider.utils import *

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'KT_MONTHLY_DU_PHONG_RUI_RO'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 0 1 * *'
description = 'Báo cáo dự phòng rủi ro monthly - ManhLV'
tags = ['kt', 'monthly']
department = 'Project - KT'
file_name = 'DU_PHONG_RUI_RO'
report_name = 'Báo cáo dự phòng rủi ro monthly'
overwrite = True
freq = 'monthly'
_type = 'Data Warehouse to SharePoint - Report F88'
sql, saved_folder = set_kwargs(parent_dir, file_name)
redshift_conn_id = 'f88_redshift'

def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date
    # last_day_of_month = time.replace(day=calendar.monthrange(time.year, time.month)[1]).strftime("%Y%m%d")
    last_day_of_month = (kwargs['dag_run'].logical_date - datetime.timedelta(1)).strftime('%Y%m%d')
    params = {}
    params.update({'last_day_of_month': last_day_of_month})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')
def download_to_sharepoint(**kwargs):
    ti = kwargs['ti']
    params = ti.xcom_pull(task_ids='Set_Parameters_for_SQL', key=file_name)
    redshift_hook = RedshiftSQLHook(redshift_conn_id=redshift_conn_id)
    df = redshift_hook.get_pandas_df(sql)
    column = [('date_wid','integration_id','office_code','office_name','loan_code','loan_id','client_code','client_name','asset_category_code','asset_category_name','from_date','to_date','due_date','interest_rate','collateral_value','principal_amount','dpd','quote_rate','provision_amount','group_debt','original_payment_method','run_job_date','create_date','createdby_id','loan_product' ,'amount_buy_debt' )]
    data = column + list(df.itertuples(index=False))
    wb = Workbook()
    wb.new_sheet("sheet name", data=data)
    execution_date = params['last_day_of_month']
    year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
    saved_folder_tmp, file_name_tmp = set_saved_folder_and_file_name(overwrite, freq,
                                                             saved_folder,
                                                             file_name, report_name, year,
                                                             month, day)
    wb.save(os.path.join(saved_folder_tmp, file_name_tmp))



with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         catchup=False,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)

    extract = PythonOperator(task_id='download_to_sharepoint',
                                python_callable=download_to_sharepoint)

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> set_params >> extract >> load >> end
