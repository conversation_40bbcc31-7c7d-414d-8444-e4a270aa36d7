import datetime
import os
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import Python<PERSON>perator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from DAGs.utils import DAGMonitor
import shutil
import glob
from Provider.OracleProvider.operators.GetDataOperators import GetData<PERSON>racleOperator
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from SendMail.SendMail import dagmonitor_on_fail_callback

parent_dir = os.path.dirname(os.path.abspath(__file__))
DAG_ID = 'MAIN_MONTHLY_DWH2SHAREPOINT'
description = 'Main Dag for MONTHLY DWH2SHAREPOINT'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None  # '50 7 * * *'
tags = ['monthly', 'main']
freq = 'MONTHLY'
pattern_type='DWH2SHAREPOINT'

def set_params(**context):
    return DAGMonitor.set_params(context['dag_run'].logical_date)


def delete_downloaded() -> None:
    folder_paths = glob.glob(os.path.join(parent_dir, '*', 'Downloaded'))

    for folder_path in folder_paths:
        if folder_path.__contains__('QLV') or folder_path.__contains__('CIMB'):
            continue
        try:
            shutil.rmtree(folder_path)
            print(f"Deleted folder: {folder_path}")
        except OSError as e:
            print(f"Error deleting folder: {folder_path} - {e}")
    shutil.rmtree(os.path.join(parent_dir, 'Downloaded'), ignore_errors=True)


with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'none_skipped'
         },
         start_date=start_date,
         catchup=False,
         schedule_interval=schedule_interval,
         max_active_runs=1,
         tags=tags
         ) as main_dag:
    start_main = EmptyOperator(task_id="Start")
    end_main = EmptyOperator(task_id="End")

    # Define the PythonOperator to execute set_params
    set_params_task = PythonOperator(
        task_id='set_params_task',
        python_callable=set_params,
        provide_context=True
    )
    delete_downloaded = PythonOperator(
        task_id='delete_downloaded',
        python_callable=delete_downloaded
    )

    list_sub_main_name = DAGMonitor.get_distinct_main_dagid(freq=freq, pattern_type=pattern_type)
    list_sub_main_dag = []
    #1, Get all distinct main_dag_id of daily ->main_dag_id_daily_lst
    #2. Loop main_dag_id_daily_lst -> x {get by main dag id}
    #3. delete_downloaded
    for sub_main_dag_id in list_sub_main_name:
        sub_main_description = f'Main Dag for {sub_main_dag_id}'
        tags = [sub_main_dag_id.split('.')[-1] if '.' in sub_main_dag_id else sub_main_dag_id.split('_')[-1], 'daily', 'main']

        dag_monitor_data = DAGMonitor.get_dag_monitor_data(main_dag_id=sub_main_dag_id)

        list_sub_dag = []
        for sub_dag in dag_monitor_data:
            sql_list = sub_dag.query.split('--split')
            if len(sql_list) > 1:
                query_input = dict({index: value for index, value in enumerate(sql_list)})
            else:
                query_input = sub_dag.query
            local_saved_folder = os.path.join(parent_dir, sub_dag.save_folder)
            with DAG(dag_id=f'{sub_dag.dag_id}',
                    schedule_interval=sub_dag.schedule_interval,
                    start_date=sub_dag.start_date,
                    description=sub_dag.description,
                    dagrun_timeout=datetime.timedelta(minutes=45),
                    catchup=False,
                    max_active_runs=1,
                    default_args={
                        'owner': 'F88-DE',
                        'retries': 3,
                        'retry_delay': datetime.timedelta(minutes=1),
                    },
                    on_failure_callback=dagmonitor_on_fail_callback,
                    tags=sub_dag.tags) as dag:
                extract = GetDataOracleOperator(
                    task_id=f"extract_data_oracle_{sub_dag.dag_id}",
                    oracle_conn_id=sub_dag.source_conn_id,
                    sql=query_input,
                    saved_folder=local_saved_folder,
                    file_name=sub_dag.file_name,
                    report_name=sub_dag.report_name,
                    overwrite=sub_dag.overwrite,
                    freq=sub_dag.freq,
                    dag=dag)
                load = UploadSharePointOperator(
                    task_id=f'load_file_into_sharepoint_{sub_dag.dag_id}',
                    sharepoint_conn_id=sub_dag.destination_conn_id,
                    department=sub_dag.department,
                    report_name=sub_dag.report_name,
                    local_saved_folder=local_saved_folder,
                    local_file_name=sub_dag.file_name,
                    overwrite=sub_dag.overwrite,
                    freq=sub_dag.freq,
                    _type=sub_dag.type,
                    dag=dag)
                extract >> load
            list_sub_dag.append(f'{sub_dag.dag_id}')

        with DAG(dag_id=sub_main_dag_id, description=sub_main_description,
                default_args={
                    'owner': 'F88-DE',
                },
                start_date=start_date,
                catchup=False,
                schedule_interval=None,
                max_active_runs=1,
                tags=tags
                ) as sub_main_dag:
            start_sub_main = EmptyOperator(task_id='Start', dag=sub_main_dag)

            end_sub_main = EmptyOperator(task_id='End', dag=sub_main_dag)

            list_tasks = []
            for item in list_sub_dag:
                trigger_dag = TriggerDagRunOperator(task_id=f"{item}_task", trigger_dag_id=f"{item}", wait_for_completion=True, poke_interval=5, deferrable=True,
                                                    conf={"set_params_task": "{{ dag_run.conf['set_params_task'] if 'set_params_task' in dag_run.conf else None }}"})
                list_tasks.append(trigger_dag)

            start_sub_main >> list_tasks >> end_sub_main
        
        trigger_dag = TriggerDagRunOperator(task_id=sub_main_dag_id, trigger_dag_id=sub_main_dag_id, wait_for_completion=True,
                                            poke_interval=5, deferrable=True,
                                            conf={
                                                "set_params_task": "{{ task_instance.xcom_pull(task_ids='set_params_task') }}"})
        list_sub_main_dag.append(trigger_dag)

    prev_task = set_params_task
    start_main >> prev_task
    for i, task in enumerate(list_sub_main_dag):
        prev_task >> task
        prev_task = task
    prev_task >> delete_downloaded >> end_main
