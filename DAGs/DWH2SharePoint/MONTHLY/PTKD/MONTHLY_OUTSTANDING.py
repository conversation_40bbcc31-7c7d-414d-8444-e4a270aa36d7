import datetime
import os
import logging
import calendar
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from Provider.OracleProvider.operators.GetDataOperators import GetDataOracleOperator
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'PTKD_MONTHLY_MONTHLY_OUTSTANDING'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 0 1 * *'
description = 'Báo cáo Monthly Outstanding report monthly - TuyenDN'
tags = ['ptkd', 'monthly']
department = 'Project - PTKD'
file_name = 'MONTHLY_OUTSTANDING'
report_name = 'Monthly Outstanding report'
overwrite = True
freq = 'monthly'
_type = 'Data Warehouse to SharePoint - Report F88'
sql, saved_folder = set_kwargs(parent_dir, file_name)


def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date
    last_day_of_month = time.replace(day=calendar.monthrange(time.year, time.month)[1]).strftime("%Y%m%d")
    YEAR_NUM, MONTH_NUM = last_day_of_month[:4], last_day_of_month[4:6]
    params = {}
    params.update({'last_day_of_month': last_day_of_month, 'year1': YEAR_NUM, 'month1': MONTH_NUM})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         catchup=False,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)

    extract = GetDataOracleOperator(task_id=f'extract_data_from_dwh_{file_name}',
                                    oracle_conn_id='oracle_f88_dwh',
                                    sql=sql,
                                    saved_folder=saved_folder,
                                    file_name=file_name,
                                    report_name=report_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    )

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> set_params >> extract >> load >> end
