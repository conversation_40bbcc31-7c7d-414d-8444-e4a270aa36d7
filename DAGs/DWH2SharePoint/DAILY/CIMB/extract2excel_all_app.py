import json
import numpy as np
from datetime import datetime, timedelta
import requests
from DAGs.DWH2SharePoint.DAILY.CIMB.Service import *
from airflow.hooks.oracle_hook import OracleHook
from airflow.hooks.mysql_hook import MySqlHook
from airflow.hooks.base import BaseHook
import re
import pandas as pd
import os

def main_extract_all_app(conn_id='oracle_f88_dwh', mysql_conn_id='mysql_los', **kwargs):
    mysql_hook = MySqlHook(mysql_conn_id=mysql_conn_id)
    ngay_hom_nay = kwargs['dag_run'].logical_date
    print(f'ngay run: {str(ngay_hom_nay)}')
    query1 = """WITH claim_id AS(
                    SELECT
                    	PartnerLoanId AS cimbLoanId
                    FROM
                    	QuickOffer qo
                    WHERE
                    	Partner = 'CIMB'
                    	AND CreatedDate >= DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)
                    	AND CreatedDate < \'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\'
                    	AND PartnerLoanId IS NOT NULL
                    UNION
                    SELECT
                    	PartnerLoanId
                    FROM
                    	Pawn
                    WHERE
                    	FundSource = '05'
                    	AND (PawnStatusCode IS NULL
                    		OR PawnStatusCode = ''
                    		OR PawnStatusCode = '10')
                    	AND FromDate = DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)),
                    	h1 as (
                    select
                    	A.*,
                    	B.Value
                    from
                    	Pawn A
                    left join Period B on
                    	A.PeriodId = B.Id
                    where
                    	A.PartnerLoanId in (
                    	select
                    		*
                    	from
                    		claim_id) 
                    	),
                    h2 AS (
                    SELECT
                    	A.cimbLoanId CIMBLOANID,
                    	B.PartnerAccountId PartnerAccountId,
                    	B.CodeNo CodeNo,
                    	Case
                    		when B.Status in(-1, 1) then B.PriceLoan
                    		when B.Status = 0 then C.PriceDebitAmount
                    		else Null
                    	End as request_amount,
                    	Case
                    		when B.Status in(-1, 1) then B.PeriodValue
                    		when B.Status = 0 then C.value
                    		else Null
                    	End as request_tenor
                    FROM
                    	claim_id A
                    LEFT JOIN QuickOffer B on
                    	A.cimbLoanId = B.PartnerLoanId
                    LEFT JOIN h1 C on
                    	A.cimbLoanId = C.PartnerLoanId
                    )select
                    	*
                    from
                    	h2"""
    conn=mysql_hook.get_conn()
    print('Start: read sql mysql_los to data frame')
    df = pd.read_sql(query1, conn,index_col=None)
    print(f'df:{df.head()}')

    ngay_hom_qua=ngay_hom_nay - timedelta(days=1)
    query2 = """ select cimbLoanId, creationTime ,status,errorDetail,cancelledTime,approvedTime,message,instantApproval,
    manualApprovalReasons FROM STGPROD.CIMB_All_APP WHERE TRUNC(extractDate) = DATE \'""" + ngay_hom_qua.strftime(
    "%Y-%m-%d") + "\'"
    hook = OracleHook(oracle_conn_id=conn_id)
    conn2=hook.get_conn()
    print('Start: read sql STGPROD.CIMB_All_APP to data frame ')
    df2 = pd.read_sql(query2, conn2,index_col=None)
    print(f'df2:{df2.head()}')
    df2['CIMBLOANID'] = df2['CIMBLOANID'].astype(str)
    df['CIMBLOANID'] = df['CIMBLOANID'].astype(str)
    merged_df = pd.merge(df2, df, on='CIMBLOANID',how='left')
    current_file_path = os.path.abspath(__file__)
    base_path  = os.path.dirname(current_file_path)
    saved_folder = create_year_month_day_folders(base_path, ngay_hom_qua)
    file_name = 'API_All_APP.xlsx'
    writer = pd.ExcelWriter(os.path.join(saved_folder, file_name), engine='xlsxwriter',
                                options={'strings_to_urls': False})
    merged_df.to_excel(writer, index=False)
    writer.save()
    
    
def create_folder(path):
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)

def create_year_month_day_folders(base_path, logical_date):
    year = str(logical_date.year)
    month = logical_date.strftime('%m')
    day = logical_date.strftime('%d')
    download = os.path.join(base_path, 'Downloaded')
    report = os.path.join(download , 'Báo cáo đối soát ALL APP')
    year_path = os.path.join(report, year)
    month_path = os.path.join(year_path, month)
    day_path = os.path.join(month_path, day)
    
    create_folder(download)
    create_folder(report)
    create_folder(year_path)
    create_folder(month_path)
    create_folder(day_path)
    return day_path

