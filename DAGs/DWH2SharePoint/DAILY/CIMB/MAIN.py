import os
from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.operators.python import PythonOperator
import logging
from airflow.hooks.oracle_hook import OracleHook
import calendar

DAG_ID = 'MAIN_DAILY_CIMB'
description = 'Main Dag for Call API CIMB - From TruongNV8 with love'
start_date = datetime(2022, 1, 1)
schedule_interval = '0 8 * * *'
tags = ['cimb', 'api', 'main']
with DAG(dag_id=DAG_ID, description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'none_skipped'
         },
         start_date=start_date,
         catchup=False,
         schedule_interval=schedule_interval,
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id="Start")
    end = EmptyOperator(task_id="End")
    t2 = TriggerDagRunOperator(task_id=f"Main_CIMB_All_App", 
        trigger_dag_id='CIMB_All_App',wait_for_completion=True)
    t3 = TriggerDagRunOperator(task_id=f"Main_CIMB_CONTRACT", 
        trigger_dag_id='CIMB_CONTRACT',wait_for_completion=True)
    start >> t2 >> t3 >>end