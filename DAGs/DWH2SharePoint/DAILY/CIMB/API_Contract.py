import json
import logging

import mysql.connector
import numpy as np
from datetime import datetime, timedelta
import requests
import re
from DAGs.DWH2SharePoint.DAILY.CIMB.Service import *
from airflow.hooks.oracle_hook import OracleHook
import pandas as pd


def main_contract(conn_id='oracle_f88_dwh', **kwargs):
    public_key, key, iv, headers = all_necessary_things()
    cert_file = public_key[0]
    private_key_file = public_key[1]
    authorization = public_key[2]
    indentifier = public_key[3]
    rsa_key = public_key[4]
    hook = OracleHook(oracle_conn_id=conn_id)
    ngay_hien_tai = kwargs['dag_run'].logical_date
    hook.run("delete from STGPROD.\"CIMB_ALL_CONTRACT\" WHERE TRUNC(\"extractDate\") = DATE \'" + ngay_hien_tai.strftime(
        "%Y-%m-%d") + "\'", autocommit=True)
    # print("delete from testdwh.\"All_App\" WHERE TRUNC(extractDate) = DATE \'"+ngay_hom_qua.strftime("%Y-%m-%d")+"\'") 
    date_bobys = "{\"snapshotDate\": \"" + ngay_hien_tai.strftime(
        "%Y-%m-%d") + "\",\"page\": \"1\",\"pageSize\": \"2500\"}"
    # Encrypt body
    encrypted_text1 = encrypt(date_bobys, key, iv)
    # API Loan list prod : https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/loan-ms/api-compress/active-loan-list

    response_loan = requests.post("https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/partner-system-api/protected/inquiry/loan/v3/active-loan-list",
                                  data=encrypted_text1, headers=headers, cert=(cert_file, private_key_file))
    # print(response_loan)
    data_loan = None
    logging.info(f"response_loan:{response_loan}")
    logging.info(f"response status:{response_loan.status_code}")

    if response_loan.status_code == 201:
        content = response_loan.text
        data_loan = decrypt(content, key, iv)
        data_loan = json.loads(data_loan)
        totalpage = int(data_loan['data']['totalPages'])
        print("--------------------------------------------------------")
        for pos in range(0, totalpage):
            date_bobys = "{\"snapshotDate\": \"" + ngay_hien_tai.strftime("%Y-%m-%d") + "\",\"page\": \"" + str(
                pos + 1) + "\",\"pageSize\": \"2500\"}"
            logging.info(f"date_bobys:{date_bobys}")
            encrypted_text1 = encrypt(date_bobys, key, iv)
            response_loan = requests.post(
                "https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/partner-system-api/protected/inquiry/loan/v3/active-loan-list",
                data=encrypted_text1, headers=headers, cert=(cert_file, private_key_file))
            content = response_loan.text
            data_loan = decrypt(content, key, iv)
            data_loan = json.loads(data_loan)
            list_data_loan = data_loan['data']['list']
            exe_dt_loan = [(int(x['loanId']) if not str(x['loanId']) == '' else None,
                            float(x['rpymFee']) if not str(x['rpymFee']) == '' else None,
                            int(x['loanTenor']) if not str(x['loanTenor']) == '' else None,
                            int(x['loanAmount']) if not str(x['loanAmount']) == '' else None, \
                            str(x['loanStatus']) if not str(x['loanStatus']) == '' else None,
                            int(x['overdueDays']) if not int(x['overdueDays']) == '' else None,
                            str(x['schInterest']) if not str(x['schInterest']) == '' else None,
                            float(x['interestRate']) if not float(x['interestRate']) == '' else None, \
                            float(x['maturityDate']) if not str(x['maturityDate']) == '' else None,
                            int(x['applicationId']) if not str(x['applicationId']) == '' else None,
                            int(x['currentPeriod']) if not str(x['currentPeriod']) == '' else None,
                            str(x['loanCloseDate']) if not str(x['loanCloseDate']) == '' else None, \
                            int(x['partnerUserId']) if not str(x['partnerUserId']) == '' else None,
                            datetime.strptime(x['dataCreateTime'], '%Y%m%d %H:%M:%S %f') if not str(
                                x['dataCreateTime']) == '' else None,
                            int(x['repaidInterest']) if not x['repaidInterest'] == None else None,
                            int(x['repaidPrincipal']) if not str(x['repaidPrincipal']) == '' else None, \
                            int(x['overdueInterest ']) if not str(x['overdueInterest ']) == '' else None,
                            int(x['partnerRequestId']) if not str(x['partnerRequestId']) == '' else None,
                            int(x['nextRepaymentDate']) if not str(x['nextRepaymentDate']) == '' else None,
                            int(x['overduePrincipal ']) if not str(x['overduePrincipal ']) == '' else None, \
                            int(x['outstandingPrincipal']) if not x['outstandingPrincipal'] == None else None,
                            int(x['repaidPenaltyInterest']) if not x['repaidPenaltyInterest'] == None else None,
                            ngay_hien_tai \
                            ) for x in list_data_loan]
            hook.bulk_insert_rows(table="STGPROD.\"CIMB_ALL_CONTRACT\"", rows=exe_dt_loan,
                                  target_fields=["\"loanId\"", "\"rpymFee\"", \
                                                 "\"loanTenor\"", "\"loanAmount\"", "\"loanStatus\"", "\"overdueDays\"",
                                                 "\"schInterest\"", "\"interestRate\"", "\"maturityDate\"",
                                                 "\"applicationId\"", "\"currentPeriod\"", \
                                                 "\"loanCloseDate\"", "\"partnerUserId\"", "\"dataCreateTime\"",
                                                 "\"repaidInterest\"", "\"repaidPrincipal\"", "\"overdueInterest \"",
                                                 "\"partnerRequestId\"", \
                                                 "\"nextRepaymentDate\"", "\"overduePrincipal \"",
                                                 "\"outstandingPrincipal\"", "\"repaidPenaltyInterest\"",
                                                 "\"extractDate\""], commit_every=5000)

