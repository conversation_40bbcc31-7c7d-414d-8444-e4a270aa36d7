import json
import mysql.connector
import numpy as np
from datetime import datetime, timedelta
import requests
import re
from DAGs.DWH2SharePoint.DAILY.CIMB.Service import *
from airflow.hooks.oracle_hook import OracleHook
import pandas as pd
from airflow.hooks.mysql_hook import My<PERSON>ql<PERSON><PERSON>

def main_extract_contract(conn_id='oracle_f88_dwh', mysql_conn_id='mysql_los', **kwargs):
    mysql_hook = MySqlHook(mysql_conn_id=mysql_conn_id)
    conn=mysql_hook.get_conn()
    hook = OracleHook(oracle_conn_id=conn_id)
    conn2=hook.get_conn()
    ngay_hien_tai = kwargs['dag_run'].logical_date
    # ngay_hom_qua=ngay_hom_nay - timedelta(days=1)
    query1 = """select PartnerLoanId applicationId,CodeNo,CustomerCode  from QuickOffer"""
    df = pd.read_sql(query1, conn,index_col=None)
    query2 = """select 
              "applicationId","dataCreateTime",
              "loanStatus", "loanAmount", "overduePrincipal ","loanCloseDate",
              "maturityDate","interestRate","overdueDays","nextRepaymentDate",
              "loanTenor","repaidPrincipal","repaidInterest","repaidPenaltyInterest",
              "rpymFee","schInterest","outstandingPrincipal","overdueInterest "
              from STGPROD.CIMB_All_CONTRACT A where TRUNC(A."extractDate") = TO_DATE(\'""" + ngay_hien_tai.strftime("%Y-%m-%d") + """\',\'yyyy-mm-dd\')"""
    df2 = pd.read_sql(query2, conn2,index_col=None)
    #df2['applicationId'] = df2['applicationId'].replace('', pd.NA)
    #df['applicationId'] = df['applicationId'].replace('', pd.NA)
    df2['applicationId'] = df2['applicationId'].astype(str)
    df['applicationId'] = df['applicationId'].astype(str)
    merged=pd.merge(df2, df, on='applicationId', how='left')
    current_file_path = os.path.abspath(__file__)
    base_path  = os.path.dirname(current_file_path)
    logical_date = kwargs['dag_run'].logical_date- timedelta(days=1)
    saved_folder = create_year_month_day_folders(base_path, logical_date)
    file_name = 'API_All_Contract.xlsx'
    writer = pd.ExcelWriter(os.path.join(saved_folder, file_name), engine='xlsxwriter',
                                options={'strings_to_urls': False})
    merged.to_excel(writer, index=False)
    writer.save()
    
    
def create_folder(path):
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)

def create_year_month_day_folders(base_path, logical_date):
    year = str(logical_date.year)
    month = logical_date.strftime('%m')
    day = logical_date.strftime('%d')
    download = os.path.join(base_path, 'Downloaded')
    report = os.path.join(download , 'Báo cáo đối soát ALL CONTRACT')
    year_path = os.path.join(report, year)
    month_path = os.path.join(year_path, month)
    day_path = os.path.join(month_path, day)
    
    create_folder(download)
    create_folder(report)
    create_folder(year_path)
    create_folder(month_path)
    create_folder(day_path)
    return day_path