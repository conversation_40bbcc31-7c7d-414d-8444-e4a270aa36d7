import json
import logging

import numpy as np
from datetime import datetime, timedelta
import requests
from DAGs.DWH2SharePoint.DAILY.CIMB.Service import *
from airflow.hooks.oracle_hook import OracleHook
from airflow.hooks.mysql_hook import MySqlHook
from airflow.hooks.base import BaseHook
import re
import pandas as pd


def main_all_app(conn_id='oracle_f88_dwh', mysql_conn_id='mysql_los', **kwargs):
    mysql_hook = MySqlHook(mysql_conn_id=mysql_conn_id)
    public_key, key, iv, headers = all_necessary_things()
    cert_file = public_key[0]
    private_key_file = public_key[1]
    authorization = public_key[2]
    indentifier = public_key[3]
    rsa_key = public_key[4]
    ngay_hom_nay = kwargs['dag_run'].logical_date
    query = """
            SELECT
            PartnerLoanId AS cimbLoanId
            FROM
            QuickOffer qo
            WHERE
            Partner = 'CIMB'
            AND ((CreatedDate >= DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)
            AND CreatedDate < \'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\')
            or
            (ModifiedDate >= DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)
            AND ModifiedDate < \'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\'))
            AND PartnerLoanId IS NOT NULL
            UNION
            SELECT
            PartnerLoanId
            FROM
            Pawn WHERE        
            FundSource = '05'
            AND (PawnStatusCode IS NULL
            OR PawnStatusCode = ''
            OR PawnStatusCode = '10')
            AND (FromDate = DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)
            or (ModifiedDate >= DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)
            AND ModifiedDate < \'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\')
            or (CreatedDate  >= DATE_ADD(\'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\', INTERVAL -1 DAY)
            AND CreatedDate  < \'""" + ngay_hom_nay.strftime("%Y-%m-%d") + """\'))"""

    # mysql_connection = mysql.connector.connect(
    #         host='prod-lending.cluster-ro-cwrlnzxvikho.ap-southeast-1.rds.amazonaws.com',
    #         database='los_prod',
    #         user='datalake_u',
    #         password='PFmL6wlaYFfikMFM6afE')
    # mysql_cursor = mysql_connection.cursor()
    # mysql_cursor.execute(query)
    # cimbid = mysql_cursor.fetchall()
    # mysql_cursor.close()
    # mysql_connection.close()
    
    cimbid = mysql_hook.get_records(query)

    # conn2 = cx_Oracle.connect("TESTDWH","QSkNAmA2bzZxCLS4KFho", "************:1521/orcldwh1")
    # cur = conn2.cursor()
    hook = OracleHook(oracle_conn_id=conn_id)
    conn2=hook.get_conn()
    cur = conn2.cursor()  
    # ngay_hien_tai = datetime.now()
    ngay_hom_qua = ngay_hom_nay- timedelta(days=1)
    ngay_hom_qua = ngay_hom_qua.replace(tzinfo=None)
    # print(ngay_hom_qua)
    cur.execute("delete from STGPROD.CIMB_All_APP WHERE TRUNC(extractDate) = DATE \'" + ngay_hom_qua.strftime("%Y-%m-%d") + "\'")
    conn2.commit()
    # hook.run("delete from TESTDWH.\"All_App\" WHERE TRUNC(\"extractDate\") = DATE \'" + ngay_hom_qua.strftime(
    #     "%Y-%m-%d") + "\'", autocommit=True)

    # conn2.commit()    

    for pos in range(0, len(cimbid), 50):
        body_text = (
            """{"loanIds":["""
            + ",".join(
                [
                    '"' + str("".join(re.findall(r"\d", str(cimbid[i])))) + '"'
                    for i in range(pos, min(len(cimbid),pos + 50))
                ]
            )
            + """],"partnerLoanIds":null}"""
        )
        ###
        logging.info(f"bodytext:{body_text}")
        encrypted_text = encrypt(body_text, key, iv)
        body = encrypted_text
        logging.info(f"data:{body}")
        response = requests.post(
            "https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/partner-system-api/protected/onboarding/loan/v3/get-status",
            data=body,
            headers=headers,
            cert=(cert_file, private_key_file),
        )
        data_app = None
        logging.info(f"response:{response}")
        logging.info(f"response status:{response.status_code}")
        if response.status_code == 201:
            content = response.text
            data_app = decrypt(content, key, iv)
            data_app = json.loads(data_app)
        print(data_app["data"])
        print(data_app["data"]["loanRequestStatuses"])

        list_data_app = data_app["data"]["loanRequestStatuses"]
        # exe_dt_loan = [(
        #     int(x['partnerLoanId']) if not str(x['partnerLoanId'])=='' else None,
        #  int(x['cimbLoanId']) if not str(x['cimbLoanId'])=='' else None,
        # str(x['status']) if not str(x['status'])=='' else None,int(x['partnerRequestId']) if not str(x['partnerRequestId'])=='' else None,\
        #         str(x['message']) if not str(x['message'])=='' else None,int(x['offer']['loanAmount']) if not x['offer']['loanAmount']==None else None,int(x['offer']['loanTenor']) if not x['offer']['loanTenor']==None else None,float(x['offer']['interestRate']) if not x['offer']['interestRate']==None else None,\
        #         str(x['creditModel']) if not x['creditModel']==None else None,str(x['instantApproval']) if not x['instantApproval']==None else None,str(x['signContractUrl']) if not str(x['signContractUrl'])=='' else None,str(x['manualApprovalReasons']) if not x['manualApprovalReasons']==None else None,\
        #         str(x['errorDetail']) if not x['errorDetail']==None else None,str(x['contractSigningStatus']) if not str(x['contractSigningStatus'])=='' else None,
        #         str(x['additionalInfo']['creationTime']) if not x['additionalInfo']['creationTime']==None else None,str(x['additionalInfo']['cancelledTime']) if not x['additionalInfo'].get('cancelledTime', None)==None else None,str(x['additionalInfo']['completedTime']) if not x['additionalInfo'].get('completedTime', None)==None else None, str(x['additionalInfo']['startVerificationTime']) if not x['additionalInfo']['startVerificationTime']==None else None,str(x['additionalInfo']['approvedTime']) if not x['additionalInfo']['approvedTime']==None else None,\
        #         str(x['missingRequiredDocs']) if not str(x['missingRequiredDocs'])=='' else None,str(x['paymentEvidenceUrl']) if not x['paymentEvidenceUrl']==None else None,ngay_hom_qua\
        #         ) for x in list_data_app]


        # kienpv 20250106 update

        exe_dt_loan = [(
            int(x.get('partnerLoanId')) if str(x.get('partnerLoanId', '').strip()).isdigit() else None,
            int(x.get('cimbLoanId')) if str(x.get('cimbLoanId', '').strip()).isdigit() else None,
            str(x.get('status', '')).strip() if x.get('status') else None,
            int(x.get('partnerRequestId')) if str(x.get('partnerRequestId', '').strip()).isdigit() else None,
            str(x.get('message', '')).strip() if x.get('message') else None,
            int(x.get('offer', {}).get('loanAmount', 0)) if isinstance(x.get('offer', {}).get('loanAmount'),
                                                                       int) else None,
            int(x.get('offer', {}).get('loanTenor', 0)) if isinstance(x.get('offer', {}).get('loanTenor'),
                                                                      int) else None,
            float(x.get('offer', {}).get('interestRate', 0.0)) if isinstance(x.get('offer', {}).get('interestRate'),
                                                                             (int, float)) else None,
            str(x.get('creditModel', '')).strip() if x.get('creditModel') else None,
            str(x.get('instantApproval', '')).strip() if x.get('instantApproval') else None,
            str(x.get('signContractUrl', '')).strip() if x.get('signContractUrl') else None,
            str(x.get('manualApprovalReasons', '')).strip() if x.get('manualApprovalReasons') else None,
            str(x.get('errorDetail', '')).strip() if x.get('errorDetail') else None,
            str(x.get('contractSigningStatus', '')).strip() if x.get('contractSigningStatus') else None,
            str(x.get('additionalInfo', {}).get('creationTime', '')).strip() if x.get('additionalInfo', {}).get(
                'creationTime') else None,
            str(x.get('additionalInfo', {}).get('cancelledTime', '')).strip() if x.get('additionalInfo', {}).get(
                'cancelledTime') else None,
            str(x.get('additionalInfo', {}).get('completedTime', '')).strip() if x.get('additionalInfo', {}).get(
                'completedTime') else None,
            str(x.get('additionalInfo', {}).get('startVerificationTime', '')).strip() if x.get('additionalInfo',
                                                                                               {}).get(
                'startVerificationTime') else None,
            str(x.get('additionalInfo', {}).get('approvedTime', '')).strip() if x.get('additionalInfo', {}).get(
                'approvedTime') else None,
            str(x.get('missingRequiredDocs', '')).strip() if x.get('missingRequiredDocs') else None,
            str(x.get('paymentEvidenceUrl', '')).strip() if x.get('paymentEvidenceUrl') else None,
            ngay_hom_qua
        ) for x in list_data_app]
        tablename = 'STGPROD.CIMB_All_APP'

        target_fields = [
                'PARTNERLOANID',
                'CIMBLOANID',
                'STATUS',
                'PARTNERREQUESTID',
                'MESSAGE',
                'LOANAMOUNT',
                'LOANTENOR',
                'INTERESTRATE',
                'CREDITMODEL',
                'INSTANTAPPROVAL',
                'SIGNCONTRACTURL',
                'MANUALAPPROVALREASONS',
                'ERRORDETAIL',
                'CONTRACTSIGNINGSTATUS',
                'CREATIONTIME',
                'CANCELLEDTIME',
                'COMPLETEDTIME',
                'STARTVERIFICATIONTIME',
                'APPROVEDTIME',
                'MISSINGREQUIREDDOCS',
                'PAYMENTEVIDENCEURL',
                'EXTRACTDATE'
            ]
        values_base = target_fields if target_fields else exe_dt_loan[0]
        prepared_stm = "insert into {tablename} {columns} values ({values})".format(
        tablename=tablename,
        columns="({})".format(", ".join(target_fields)) if target_fields else "",
        values=", ".join(":%s" % i for i in range(1, len(values_base) + 1)),
            )
            # print(exe_dt_loan)
        cur.executemany(prepared_stm, exe_dt_loan)
        conn2.commit()
        
        # hook.bulk_insert_rows(table="TESTDWH.\"All_App\"", rows=exe_dt_loan,
        #                     target_fields=["\"partnerLoanId\"", "\"cimbLoanId\"", "\"status\"",
        #                                     "\"partnerRequestId\"", "\"message\"", "\"loanAmount\"", "\"loanTenor\"",
        #                                     "\"interestRate\"", "\"creditModel\"", "\"instantApproval\"",
        #                                     "\"signContractUrl\""
        #                                     , "\"manualApprovalReasons\""
        #                                     , "\"errorDetail\""
        #                                     ,"\"contractSigningStatus\""
        #                                     , "\"creationTime\""
        #                                     , "\"cancelledTime\"",
        #                                         "\"completedTime\"", "\"startVerificationTime\"", "\"approvedTime\"",
        #                                     "\"missingRequiredDocs\"", "\"paymentEvidenceUrl\"", "\"extractDate\""
        #                     ],
        #                     commit_every=5000)

