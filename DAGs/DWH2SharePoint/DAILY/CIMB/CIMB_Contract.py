import os
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
import logging
from airflow.hooks.oracle_hook import OracleHook
import calendar
from DAGs.DWH2SharePoint.DAILY.CIMB.API_Contract import main_contract
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from Provider.OracleProvider.operators.GetDataOperators import GetDataOracleOperator
from DAGs.utils import set_kwargs
from DAGs.DWH2SharePoint.DAILY.CIMB.extract2excel_all_contract import *

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'CIMB_CONTRACT'
description = 'Run CIMB All Contract - From TruongNV8 with love'
start_date = datetime(2023, 10, 1)
schedule_interval = None
tags = ['CIMB', 'DAILY']
overwrite = True
freq = 'daily'
_type = 'Data Warehouse to SharePoint - Report F88'
file_name = 'API_All_Contract'
report_name = '<PERSON><PERSON>o c<PERSON>o đối soát ALL CONTRACT'
department = 'Project - Vận hành'
saved_folder = os.path.join(parent_dir, 'Downloaded')
def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date - timedelta(days=1)
    DATE_WID = time.strftime("%Y%m%d")
    params = {}
    params.update({'DATE_WID': DATE_WID})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
        #  catchup=True,
         max_active_runs=1,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')
    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)
    extract2db = PythonOperator(task_id='extract2db',
                                python_callable=main_contract)
    extract = PythonOperator(task_id=f'extract_data_from_all_contract',
                                    python_callable=main_extract_contract
                                )
    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                sharepoint_conn_id='sharepoint_f88_data',
                                department=department,
                                report_name=report_name,
                                local_saved_folder=saved_folder,
                                local_file_name=file_name,
                                overwrite=overwrite,
                                freq=freq,
                                _type=_type
                                )
    end = EmptyOperator(task_id='End')

    start >> set_params >> extract2db >> extract >>load >> end
