import requests
import os
import sys
import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
from Crypto.Cipher import AES


class Authenticate:
    def __init__(self):
        self.base_url = "https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/authentication-ms/partners/authenticate"

    def get_certificates(self):
        current_dir = os.path.dirname(os.path.abspath(__file__))  # <PERSON><PERSON><PERSON> đường dẫn thư mục chứa mã hiện tại
        cert_file = os.path.join(current_dir, "CIMBF88.pem")
        private_key_file = os.path.join(current_dir,
                                        "CIMB_Key.key")  # Optional if the private key is included in the certificate file
        return (cert_file, private_key_file)

    def get_authenticate(self, api, cert_file, private_key_file):
        try:
            headers = {'partner-id': 'F88', 'api-key': 'd9b4a046-7b2d-47f8-b7ca-f63e5b09d736',
                       'Content-Type': 'application/json'}
            # Body dạng raw là {}
            payload = {}
            # Call API Get accessToken and accessTokenHash
            response = requests.post(api, headers=headers,json=payload, cert=(cert_file, private_key_file))

            # Check if the request was successful (status code 200)
            if response.status_code == 201:
                # Access the response data as JSON
                data = response.json()
                # Access specific data fields
                accessToken = data["data"]["accessToken"]
                # Kiểm tra sự tồn tại của accessTokenHash
                #kienpv -> respone thay doi -> fix cung neu nhu khong co
                if "accessTokenHash" not in data["data"]:
                    accessTokenHash = "F1rYYq/mWo0NfNPNVzxV1sMrCeNwaFGMqK2JCkcf5JU="
                else:
                    accessTokenHash = data["data"]["accessTokenHash"]

                return (accessToken, accessTokenHash)
            else:
                # Handle the error or other status codes
                print("API request failed with status code:", response.status_code)
        except requests.RequestException as e:
            print("API request error:", e)

    def get_rsa_public(self, api, authorization, identifier, cert_file, private_key_file):
        try:
            headers = {'partner-id': 'F88', 'api-key': 'd9b4a046-7b2d-47f8-b7ca-f63e5b09d736',
                       'Content-Type': 'application/json', 'authorization': authorization, 'x-identifier': identifier}
            response = requests.get(api, headers=headers, cert=(cert_file, private_key_file))
            # print(response)
            if response.status_code == 200:
                data = response.json()
                key = data["data"]["key"]
                return (key)
        except requests.RequestException as e:
            print("API request error:", e)

    def get_public_key(self):
        service = Authenticate()

        # Get certificates
        certificates = service.get_certificates()
        cert_file = certificates[0]
        private_key_file = certificates[1]
        # Kienpv ******** --- CIMB thay doi path lay publickey
        #"https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/authentication-ms/partners/authenticate"
        key_authen = service.get_authenticate(
            "https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/partner-system-api/private/authen/identity/v3/get-partner-token", certificates[0], certificates[1])
        authorization = key_authen[0]
        indentifier = key_authen[1]
        # Call API lấy về PUBLIC KEY để truyền vào API lấy danh sách hđ
        rsa_key = service.get_rsa_public(
            "https://api.cimbbankvn.com.vn:8068/prod/vn/cimb/authentication-ms/api/rsa-public", authorization,
            indentifier, cert_file, private_key_file)
        return (cert_file, private_key_file, authorization, indentifier, rsa_key)


class RSAHelper:
    def __init__(self):
        self.iv = "UMYgIDmiYVznwV6y"

    def encrypt(self, plain_text, public_key):
        plain_text_bytes = plain_text.encode('utf-8')

        public_key_bytes = public_key.encode('utf-8')
        public_key_obj = serialization.load_pem_public_key(public_key_bytes, backend=default_backend())

        cipher_text_bytes = b""
        chunk_size = public_key_obj.key_size // 8 - 11  # Adjust the chunk size based on key size
        for chunk_position in range(0, len(plain_text_bytes), chunk_size):
            chunk = plain_text_bytes[chunk_position:chunk_position + chunk_size]
            cipher_text_bytes += public_key_obj.encrypt(chunk, padding.PKCS1v15())

        return base64.b64encode(cipher_text_bytes).decode('utf-8')


def encrypt(plain_text, key, iv):
    aes = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
    padded_data = pad_data(plain_text.encode('utf-8'))
    cipher_text = aes.encrypt(padded_data)
    return base64.b64encode(cipher_text).decode('utf-8')


def decrypt(encrypted_text, key, iv):
    aes = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
    cipher_text = base64.b64decode(encrypted_text)
    decrypted_data = aes.decrypt(cipher_text)
    unpadded_data = unpad_data(decrypted_data)
    return unpadded_data.decode('utf-8')


def pad_data(data):
    block_size = AES.block_size
    padding_size = block_size - (len(data) % block_size)
    padding = bytes([padding_size]) * padding_size
    return data + padding


def unpad_data(data):
    padding_size = data[-1]
    return data[:-padding_size]


def all_necessary_things():
    service = Authenticate()
    RSA = RSAHelper()
    public_key = service.get_public_key()
    cert_file = public_key[0]
    private_key_file = public_key[1]
    authorization = public_key[2]
    indentifier = public_key[3]
    rsa_key = public_key[4]
    key = "UMYgIDmiYVznwV6yUMYgIDmiYVznwV6y"
    iv = "UMYgIDmiYVznwV6y"
    x_encryption_key = RSA.encrypt(iv, rsa_key)
    headers = {'partner-id': 'F88', 'api-key': 'd9b4a046-7b2d-47f8-b7ca-f63e5b09d736',
               'Content-Type': 'application/octet-stream', 'authorization': "Bearer " + authorization,
               'x-identifier': indentifier, 'x-encryption-key': x_encryption_key}
    return public_key, key, iv, headers
