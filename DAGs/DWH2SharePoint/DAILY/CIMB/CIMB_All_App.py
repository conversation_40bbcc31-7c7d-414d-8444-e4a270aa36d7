import os
from datetime import datetime,timedelta
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import Python<PERSON>perator
import logging
from airflow.hooks.oracle_hook import OracleHook
from airflow.hooks.mysql_hook import MySqlHook
import calendar
from DAGs.utils import set_kwargs
from DAGs.DWH2SharePoint.DAILY.CIMB.API_All_App import *
from DAGs.DWH2SharePoint.DAILY.CIMB.extract2excel_all_app import *
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from Provider.OracleProvider.operators.GetDataOperators import GetDataOracleOperator

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'CIMB_All_App'
description = 'Run CIMB All App - From TruongNV8 with love'
start_date = datetime(2022, 1, 1)
schedule_interval = None
tags = ['CIMB','DAILY']
overwrite = True
freq = 'daily'
_type = 'Data Warehouse to SharePoint - Report F88'
department = 'Project - Vận hành'
file_name = 'API_All_APP'
report_name = 'Báo cáo đối soát ALL APP'
saved_folder = os.path.join(parent_dir, 'Downloaded')


def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date - timedelta(days=1)
    DATE_WID = time.strftime("%Y%m%d")
    params = {}
    params.update({'DATE_WID': DATE_WID})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')
    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)
    run_api_all_app = PythonOperator(task_id='run_all_app',
                                     python_callable=main_all_app)
    extract = PythonOperator(task_id=f'extract_data_from_all_app',
                                    python_callable=main_extract_all_app
                                )

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                sharepoint_conn_id='sharepoint_f88_data',
                                department=department,
                                report_name=report_name,
                                local_saved_folder=saved_folder,
                                local_file_name=file_name,
                                overwrite=overwrite,
                                freq=freq,
                                _type=_type
                                )

    end = EmptyOperator(task_id='End')
    start >> set_params >> run_api_all_app >> extract >> load >> end
