SELECT -- DAILY
	t.loan_id WITHDRAW_ID,
	t2.name AS SHOPNAME,
	t.amount AS WITHDRAW_AMOUNT,
	DATE(created_date) DATE,
	WEEK(created_date) WEEK,
	MONTH(created_date) MONTH,
	YEAR(created_date) YEAR,
	CASE
		WHEN action_code LIKE '%%TRA_BOT_GOC%%' THEN 'Trả bớt gốc'
		WHEN action_code LIKE '%%DONG_HD%%' THEN 'Tất toán hợp đồng'
		WHEN action_code LIKE '%%THANH_LY%%' THEN 'Thanh lý'
	END WITHDRAW_ACTION
FROM
	m_loan_transaction t
LEFT JOIN m_office t2 ON
	t.office_id = t2.id
WHERE
	(action_code LIKE '%%TRA_BOT_GOC%%'
		OR action_code LIKE '%%DONG_HD%%'
		OR action_code LIKE '%%THANH_LY%%')
	AND DATE(created_date) = %(DATE_WID)s
