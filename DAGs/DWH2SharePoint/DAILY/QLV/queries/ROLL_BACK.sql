SELECT -- DWH
	t.<PERSON><PERSON><PERSON>_CODE ROLL_BACK_LOAN_CODE,
	t1.SHOP_NM SHOPNAME
FROM
	W_LOAN_DAILY_F t
LEFT JOIN W_SHOP_D t1 ON
	t1.SHOP_WID = t.SHOP_WID
WHERE
	YEAR_NUM = 2022
	AND MONTH_NUM = :MONTH_NUM
	AND DATE_WID = :DATE_WID
	AND OVERDUE_DAYS > 0
-- Query Split --
SELECT -- MIFOS
	t.code_no ROLL_BACK_LOAN_CODE,
	t.principal_outstanding ROLL_BACK_AMOUNT,
	t.office_name SHOPNAME,
	DATE(modifier_date) DATE,
	WEEK(modifier_date) WEEK,
	MONTH(modifier_date) MONTH,
	YEAR(modifier_date) YEAR
FROM
	m_document_ods t
WHERE
	DATE(modifier_date) = %(DATE_WID)s
	AND over_due_days < 0