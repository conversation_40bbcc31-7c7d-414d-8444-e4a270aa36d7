-- KPI_QUERY
SELECT 
	SHOP_NM,
	K<PERSON>,
	"Đơn vị",
	round("<PERSON>ụ<PERSON> tiêu",2) "<PERSON>ục tiêu",
	"Thực hiện",
	CASE 
		WHEN KPI = 'Tỷ lệ thu đúng hạn' THEN "Thực hiện"
		ELSE round("Thực hiện"/PROGRESS,2)
	END "Dự báo hoàn thành tháng",
	CASE
		WHEN KPI = 'Tỷ lệ thu đúng hạn' THEN CASE WHEN "Thực hiện" >= "Mục tiêu" THEN 'On track' ELSE 'Not on track' END
		ELSE CASE WHEN "Thực hiện"/PROGRESS >= "Mục tiêu"  THEN 'On track' ELSE 'Not on track' END 
	END "Trạng thái"	
FROM 
(
SELECT 
	a.TEN_SHOP SHOP_NM ,
	CASE 
		WHEN a."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN 'Tỷ lệ thu đúng hạn'
		WHEN a."Chỉ tiêu" = 'Giải ngân KHQL' THEN 'KH quay lại (GN)'
		WHEN a."Chỉ tiêu" = 'Số lượng KHM' THEN 'KH mới'
		WHEN a."Chỉ tiêu" = 'Tăng net' THEN 'Tăng net'
	END "KPI",
	CASE 
		WHEN a."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN '%'
		WHEN a."Chỉ tiêu" = 'Giải ngân KHQL' THEN 'Tỷ'
		WHEN a."Chỉ tiêu" = 'Số lượng KHM' THEN 'HĐ'
		WHEN a."Chỉ tiêu" = 'Tăng net' THEN 'Triệu'
	END "Đơn vị",
	CASE 
		WHEN a."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN to_number(SUBSTR(b.TY_LE_THU , 1, LENGTH(b.TY_LE_THU ) - 1))
		WHEN a."Chỉ tiêu" = 'Giải ngân KHQL' THEN round(b.GN_KHQL_TOTAL /1000,2)
		WHEN a."Chỉ tiêu" = 'Số lượng KHM' THEN b.KHM
		WHEN a."Chỉ tiêu" = 'Tăng net' THEN b.net
	END "Mục tiêu",
	CASE 
		WHEN a."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN round(a.VALUE_,4) * 100
		WHEN a."Chỉ tiêu" = 'Giải ngân KHQL' THEN round(a.VALUE_,2)
		WHEN a."Chỉ tiêu" = 'Số lượng KHM' THEN a.VALUE_
		WHEN a."Chỉ tiêu" = 'Tăng net' THEN round(a.VALUE_,2)
	END "Thực hiện",
	to_number(SUBSTR(c.PROGRESS, 1, LENGTH(c.PROGRESS) - 1))/100 PROGRESS
FROM 
(
---Tang Net---
SELECT
	Ten_shop
	, '2.1' as "Mã chỉ tiêu"
	, 'Tăng net' "Chỉ tiêu"
	, Value_
	,:DATE_WID DATE_WID
from
(
	WITH DNCK as
	(
	SELECT
		s.SHOP_NM  
		, sum(p.PRINCIPAL_REMAIN)/1e6 AS DPD0
		FROM F88DWH.W_LOAN_DAILY_F p
		LEFT JOIN F88DWH.W_LOAN_DTL_F a ON a.loan_wid=p.loan_wid
		left join F88DWH.w_shop_D s on p.shop_wid = s.shop_wid
		LEFT JOIN F88DWH.W_AREA_MANAGER_D a  ON trim(a.SHOP_ID) = s.SHOP_CODE
		WHERE 1=1
		AND p.DATE_WID = :DATE_WID
		AND p.YEAR_NUM = 2023 
		AND p.OVERDUE_DAYS <=0
		AND a.CHANNEL_CODE = 'KDML'
		and p.status = 300 
		AND p.IS_BAD_DEBT =0
		GROUP BY s.SHOP_NM 
	),
	DNDK as
	(
		SELECT
		s.SHOP_NM 
		, sum(p.PRINCIPAL_REMAIN)/1e6 AS DPD0
		FROM F88DWH.W_LOAN_DAILY_F p
		LEFT JOIN F88DWH.W_LOAN_DTL_F a ON a.loan_wid=p.loan_wid
		left join F88DWH.w_shop_D s on p.shop_wid = s.shop_wid
		LEFT JOIN F88DWH.W_AREA_MANAGER_D a  ON trim(a.SHOP_ID) = s.SHOP_CODE
		WHERE 1=1
		AND p.DATE_WID = TO_CHAR(TRUNC(TO_DATE(:DATE_WID,'yyyymmdd'),'mm')-1,'yyyymmdd')
		AND p.YEAR_NUM = 2023 
		AND p.OVERDUE_DAYS <=0
		AND a.CHANNEL_CODE = 'KDML'
		and p.status = 300 
		AND p.IS_BAD_DEBT =0
		GROUP BY s.SHOP_NM
	)
	SELECT 
		DNCK.SHOP_NM AS Ten_shop,
		'Tăng Net' AS "Chỉ tiêu"
		, sum(DNCK.DPD0 - DNDK.DPD0) AS Value_
	FROM DNCK
	LEFT JOIN DNDK ON DNCK.SHOP_NM = DNDK.SHOP_NM
	GROUP BY DNCK.SHOP_NM,
	'Tăng Net'
)
UNION ALL 
---SL KHM---
SELECT 
	s.shop_nm AS Ten_shop
	, '2.2' as "Mã chỉ tiêu"
	, 'Số lượng KHM' AS "Chỉ tiêu"
	, count(DISTINCT customer_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F wldf
LEFT JOIN F88DWH.w_shop_D s on wldf.shop_wid = s.shop_wid
WHERE 1=1
	AND DISBURSE_DATE_WID >= TO_CHAR(TRUNC(TO_DATE(:DATE_WID,'yyyymmdd'),'mm'),'yyyymmdd')
	AND DISBURSE_DATE_WID <= :DATE_WID
	AND CTR_TYPE = 'Mới'
	AND channel_code='KDML'
GROUP BY
	s.shop_nm
	, 'Số lượng KHM'
UNION ALL 
---Giai ngan KHQL---
SELECT 
	s.shop_nm AS Ten_shop
	, '2.3' as "Mã chỉ tiêu"
	, 'Giải ngân KHQL' AS "Chỉ tiêu"
	, sum(DISBURSE_AMT) / 1e9 AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F wldf
LEFT JOIN F88DWH.w_shop_D s on wldf.shop_wid = s.shop_wid
WHERE 1=1
	AND EXTRACT (MONTH FROM to_date(DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
	AND EXTRACT (YEAR FROM to_date(DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
	AND DISBURSE_DATE_WID <= :DATE_WID
	AND CTR_TYPE in ('Quay lại', 'Đáo')
	AND channel_code='KDML'
GROUP BY
	s.shop_nm
	, 'Giải ngân KHQL'
UNION ALL 
---Tỷ lệ thu đúng hạn
SELECT 
		SHOP_NM  AS Ten_shop,
		'2.4' AS "Mã chỉ tiêu",
		'Tỷ lệ thu đúng hạn' AS "Chỉ tiêu",
		Ty_le_thu_dung_han Value_,
		:DATE_WID DATE_WID
FROM 
(
--Tỷ lệ thu Total
WITH CAL AS
(
	SELECT
		DATE_WID ,
		DATE_TM ,
		YEAR_NUM ,
		MONTH_NUM ,
		TO_CHAR(DATE_TM,'YYYYMM') YEAR_MONTH ,
		EXTRACT( YEAR FROM DATE_TM +1) YEAR_NUM_N ,
		EXTRACT( MONTH FROM DATE_TM +1) MONTH_NUM_N ,
		TO_CHAR(DATE_TM+1,'YYYYMM') YEAR_MONTH_N ,
		TO_NUMBER(TO_CHAR(DATE_TM-1,'YYYYMMDD')) DATE_WID_P ,
		EXTRACT( YEAR FROM DATE_TM -1) YEAR_NUM_P ,
		EXTRACT( MONTH FROM DATE_TM -1) MONTH_NUM_P ,
		LAST_DOM_FLAG
	FROM
		F88DWH.W_CALENDAR_D
	WHERE
		DATE_TM >= ADD_MONTHS( TRUNC(to_date(:DATE_WID,'yyyymmdd'), 'MM')-1,-1)
		AND DATE_TM <= to_date(:DATE_WID,'yyyymmdd') 
)
, DN AS -- DNĐK BAO GỒM NHÓM TRONG HẠN (ĐẾN HẠN TRONG THÁNG TỚI) & NHÓM QUÁ HẠN (1-29 NGÀY)
(
	SELECT
	YEAR_NUM_N YEAR_NUM,
	MONTH_NUM_N MONTH_NUM,
	YEAR_MONTH_N YEAR_MONTH,
	CAL.DATE_TM,
	CAL.DATE_WID,
	LDA.LOAN_CODE,
	LDA.LOAN_WID, 
	LDA.SCHEDULE_NEXT_DATE,
	CAL.DATE_TM - LDA.OVERDUE_DAYS SCH_DT,
	LDA.PRINCIPAL_REMAIN,
	LDA.OVERDUE_DAYS OPENING_OVERDUE_DAYS,
	CASE WHEN LDA.OVERDUE_DAYS <= 0 THEN LDA.PRINCIPAL_REMAIN ELSE 0 END DNTH_DK,
	CASE WHEN LDA.OVERDUE_DAYS > 0 THEN LDA.PRINCIPAL_REMAIN ELSE 0 END DNQH_DK,
	CASE WHEN LDA.OVERDUE_DAYS <= 0 THEN 'TRONG HẠN' ELSE 'QUÁ HẠN' END OPENING_STATUS,
	ATP.ASSET_TP_NM,
	S.SHOP_CODE,
	S.SHOP_NM,
	CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
	CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
	LDT.FUND_NAME,
	LDT.CUSTOMER_WID,
	LDT.REFINANCED_TO ,
	LDT.PACKAGE_WID 
FROM
	CAL,
	F88DWH.W_LOAN_DAILY_F LDA,
	F88DWH.W_SHOP_D S,
	F88DWH.W_ASSET_TP_D ATP,
	F88DWH.W_LOAN_DTL_F LDT
WHERE
	CAl.LAST_DOM_FLAG = 1
	AND CAL.YEAR_NUM = LDA.YEAR_NUM
	AND CAL.MONTH_NUM = LDA.MONTH_NUM 
	AND CAL.DATE_WID = LDA.DATE_WID
	--
	AND LDA.OVERDUE_DAYS <= 29
	AND LDA.STATUS <> 603
	--
	AND S.SHOP_WID = LDT.SHOP_WID
	AND LDA.ASSET_TYPE_WID = ATP.ASSET_TP_WID
	AND ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
	AND (TO_CHAR(CAL.DATE_TM+1,'YYYYMM') = TO_CHAR(CAL.DATE_TM - LDA.OVERDUE_DAYS,'YYYYMM') OR LDA.OVERDUE_DAYS > 0)
	--
	AND LDT.LOAN_WID = LDA.LOAN_WID
),
H AS 
(
SELECT
	P.*,
	CASE
		WHEN PAK.PACKAGE_NM LIKE '%HOTRO%' THEN 'Đóng & đảo nợ'
	END LOAN_STATUS ,
	PAK2.PACKAGE_NM
FROM
(
(
	SELECT
		DN.YEAR_NUM,
		DN.MONTH_NUM,
		DN.YEAR_MONTH,
		DN.DATE_TM,
		DN.LOAN_CODE,
		DN.LOAN_WID,
		DN.SCH_DT,
		DN.PRINCIPAL_REMAIN/1E6 DU_NO,
--		DN.PRINCIPAL_REMAIN DU_NO,
		DN.OPENING_STATUS,
		DN.OPENING_OVERDUE_DAYS,
		LS.COMPLETED_PAID_DATE ,
		LDA.SCHEDULE_NEXT_DATE ENDING_SCHEDULE_NEXT_DATE,
		CASE 
			WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(DN.DATE_TM+1,'YYYYMM') THEN LS.OVERDUE_DAYS
			WHEN LS.OVERDUE_DAYS IS NOT NULL THEN ADD_MONTHS(DN.DATE_TM, 1)-DN.SCH_DT
			ELSE LDA.OVERDUE_DAYS
		END ENDING_OVERDUE_DAYS,
		CASE 
			 WHEN (CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA.OVERDUE_DAYS END) >=31 THEN 'ROLL31'
			 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
			 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(DN.DATE_TM+1,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
			 WHEN DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			 ELSE 'CHƯA THU ĐƯỢC'
		END COLLECTION_STATUS,
		CASE WHEN DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS ,
		DN.ASSET_TP_NM,
		DN.SHOP_CODE,
		DN.SHOP_NM,
		DN.CHANNEL_CODE,
		DN.PARTNER_CODE,
		DN.FUND_NAME,
		UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
		DN.REFINANCED_TO ,
		DN.PACKAGE_WID
	FROM
		DN
		LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON DN.LOAN_WID = LS.LOAN_WID
											AND DN.SCH_DT = LS.TO_DATE
		LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA ON DN.LOAN_WID = LDA.LOAN_WID
										   AND LS.COMPLETED_PAID_DATE IS NULL
										   AND LDA.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
										   AND LDA.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.DATE_WID = :DATE_WID
		LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON DN.CUSTOMER_WID = CUS.CUSTOMER_WID
)
UNION --Giải ngân mới và đến hạn trong kỳ
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		CAL.YEAR_MONTH,
		CAL.DATE_TM DISBURSE_DT,
		LDT.LOAN_CODE,
		LDT.LOAN_WID ,
		LS.TO_DATE SCH_DT,
		LDT.DISBURSE_AMT/1E6 DU_NO,
--		LDT.DISBURSE_AMT DU_NO,
		'GIẢI NGÂN MỚI' OPENING_STATUS,
		NULL OPENING_OVERDUE_DAYS,
		LS.COMPLETED_PAID_DATE ,
		NULL ENDING_SCHEDULE_NEXT_DATE,
		CASE 
			WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') THEN LS.OVERDUE_DAYS
			WHEN LS.OVERDUE_DAYS IS NOT NULL THEN ADD_MONTHS(TRUNC(LS.TO_DATE,'MM')-1,1)-LS.TO_DATE
			ELSE LDA.OVERDUE_DAYS
		END ENDING_OVERDUE_DAYS,
		CASE 
			 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
			 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
			 WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			 ELSE 'CHƯA THU ĐƯỢC'
		END COLLECTION_STATUS,
		CASE WHEN LS.TO_DATE >to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS,
		ATP.ASSET_TP_NM,
		S.SHOP_CODE,
		S.SHOP_NM,
		CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
		CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
		LDT.FUND_NAME ,
		UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
		LDT.REFINANCED_TO ,
		LDT.PACKAGE_WID 
	FROM F88DWH.W_LOAN_DTL_F LDT
		JOIN CAL ON LDT.DISBURSE_DATE_WID  = CAL.DATE_WID AND CAL.LAST_DOM_FLAG = 0
		LEFT JOIN F88DWH.W_ASSET_TP_D ATP ON LDT.ASSET_TYPE_WID = ATP.ASSET_TP_WID 
		LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON LDT.LOAN_WID = LS.LOAN_WID AND LS.SCHEDULE_NUM = 1
		LEFT JOIN F88DWH.W_SHOP_D S ON LDT.SHOP_WID = S.SHOP_WID
		LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON LDT.CUSTOMER_WID = CUS.CUSTOMER_WID 
		LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA ON LDT.LOAN_WID = LDA.LOAN_WID
										   AND LS.COMPLETED_PAID_DATE IS NULL
										   AND LDA.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.DATE_WID =:DATE_WID
	WHERE ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
		AND EXTRACT (YEAR FROM LS.TO_DATE)  = CAL.YEAR_NUM
		AND EXTRACT (MONTH FROM LS.TO_DATE)  = CAL.MONTH_NUM
		AND (LDT.DISBURSE_DATE_WID <> LDT.CLOSED_DATE_WID  OR LDT.CLOSED_DATE_WID IS NULL)
)
UNION --ROLLBACK và đến hạn trong kỳ
		(
		SELECT
			CAL.YEAR_NUM,
			CAL.MONTH_NUM,
			CAL.YEAR_MONTH,
			CAL.DATE_TM ROLL_BACK_DT,
			LDA.LOAN_CODE,
			LDA.LOAN_WID,
			LDA.SCHEDULE_NEXT_DATE SCH_DT,
			LDA.PRINCIPAL_REMAIN/1E6 DU_NO,
--			LDA.PRINCIPAL_REMAIN DU_NO,
			'ROLL BACK' OPENING_STATUS,
			LDA.OVERDUE_DAYS OPENING_OVERDUE_DAYS,
			LS.COMPLETED_PAID_DATE,
			LDA2.SCHEDULE_NEXT_DATE ENDING_SCHEDULE_NEXT_DATE,
			CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA2.OVERDUE_DAYS END ENDING_OVERDUE_DAYS,
			CASE 
				 WHEN (CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA2.OVERDUE_DAYS END) >=31 THEN 'ROLL31' 
				 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
			     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
				 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
				 WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
				 ELSE 'CHƯA THU ĐƯỢC'
			END COLLECTION_STATUS,
			CASE WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS,
			ATP.ASSET_TP_NM,
			S.SHOP_CODE,
			S.SHOP_NM,
			CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
			CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
			LDT.FUND_NAME ,
			UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
			LDT.REFINANCED_TO ,
			LDT.PACKAGE_WID 
		FROM
			F88DWH.W_LOAN_DAILY_F LDA
			JOIN CAL ON	LDA.YEAR_NUM = CAL.YEAR_NUM
					 AND LDA.MONTH_NUM = CAL.MONTH_NUM
					 AND LDA.DATE_WID = CAL.DATE_WID
			JOIN F88DWH.W_LOAN_DAILY_F LDA1 ON LDA1.YEAR_NUM = CAL.YEAR_NUM_P
											  AND LDA1.MONTH_NUM = CAL.MONTH_NUM_P
											  AND LDA1.DATE_WID = CAL.DATE_WID_P
											  AND LDA1.LOAN_WID = LDA.LOAN_WID
											  AND LDA1.OVERDUE_DAYS > 0
			LEFT JOIN F88DWH.W_LOAN_DTL_F LDT ON LDA.LOAN_WID = LDT.LOAN_WID 
			LEFT JOIN F88DWH.W_ASSET_TP_D ATP ON LDT.ASSET_TYPE_WID = ATP.ASSET_TP_WID 
			LEFT JOIN F88DWH.W_SHOP_D S ON LDT.SHOP_WID = S.SHOP_WID 
			LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON LDT.CUSTOMER_WID = CUS.CUSTOMER_WID 
			LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON LDA.LOAN_WID = LS.LOAN_WID
										  AND LDA.SCHEDULE_NEXT_DATE = LS.TO_DATE 
			LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA2 ON LDA.LOAN_WID = LDA2.LOAN_WID 
												 AND LS.COMPLETED_PAID_DATE IS NULL
												 AND LDA2.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
											     AND LDA2.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
											     AND LDA2.DATE_WID =:DATE_WID
		WHERE LDA.OVERDUE_DAYS <= 0
			AND ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
			AND EXTRACT (YEAR FROM LDA.SCHEDULE_NEXT_DATE)  = CAL.YEAR_NUM
			AND EXTRACT (MONTH FROM LDA.SCHEDULE_NEXT_DATE)  = CAL.MONTH_NUM
		)
) P
LEFT JOIN F88DWH.W_LOAN_DTL_F LDT ON P.REFINANCED_TO = LDT.INTEGRATION_ID
LEFT JOIN F88DWH.W_PACKAGE_D PAK ON LDT.PACKAGE_WID = PAK.PACKAGE_WID
LEFT JOIN F88DWH.W_PACKAGE_D PAK2 ON P.PACKAGE_WID = PAK2.PACKAGE_WID
)
SELECT 
		H.YEAR_NUM,
		H.MONTH_NUM,
		H.SHOP_CODE,
		H.SHOP_NM,	
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) AND H.COLLECTION_STATUS IN ('THU ĐÚNG HẠN','TẤT TOÁN HĐ TRƯỚC HẠN') THEN DU_NO END) DN_THU_DUNG_HAN,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) THEN DU_NO END) DN_CAN_THU ,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) AND H.COLLECTION_STATUS IN ('THU ĐÚNG HẠN','TẤT TOÁN HĐ TRƯỚC HẠN') THEN DU_NO END)/SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) THEN DU_NO END) Ty_le_thu_dung_han
FROM  H
	WHERE 1=1
		AND YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd') )
		AND YEAR_MONTH=to_char(to_date(:DATE_WID,'yyyymmdd'),'rrrrmm')
		AND CHANNEL_CODE='KDML'
		AND DEBT_STATUS='ĐÃ ĐẾN HẠN'
		AND H.OPENING_STATUS <>'QUÁ HẠN'
		AND loan_status IS NULL  
		AND h.PACKAGE_NM NOT IN ('F88_DKOT_HOTRO','F88_ĐKXM_HOTRO')
	GROUP BY 
		H.YEAR_NUM,
		H.MONTH_NUM,
		H.SHOP_CODE,
		H.SHOP_NM
)
) a 
LEFT JOIN sontb2.PGD_TARGET b ON a.TEN_SHOP = b."PGD" 
LEFT JOIN sontb2.WORKING_DAYS c ON a.date_wid = c.DATE_WID
where 1=1
--AND a.shop_nm in ('HNI17032.2A Trần Duy Hưng') 
AND c.DATE_WID = :DATE_WID
--AND a.ten_shop = 'SGN18003.935B Âu Cơ'
ORDER BY 
	CASE 
		WHEN a."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN 4
		WHEN a."Chỉ tiêu" = 'Giải ngân KHQL' THEN 3
		WHEN a."Chỉ tiêu" = 'Số lượng KHM' THEN 2
		WHEN a."Chỉ tiêu" = 'Tăng net' THEN 1
	END 
)

--split
--KD_QUERY
SELECT 
	DISTINCT 
	SHOP_NM,
	"Mã chỉ tiêu",
	"Key drivers",
	"Loại tài sản",
	"Đơn vị",
	"Mục tiêu",
	"Thực hiện",
	CASE 
		WHEN "Key drivers" = 'Tỷ lệ thu đúng hạn' THEN "Thực hiện"
		ELSE round("Thực hiện"/PROGRESS,2)
	END "Dự báo hoàn thành tháng",
	CASE
		WHEN "Key drivers" = 'Tỷ lệ thu đúng hạn' THEN CASE WHEN "Thực hiện" >= "Mục tiêu" THEN 'On track' ELSE 'Not on track' END
		WHEN "Key drivers" = 'Thời gian vay trung bình' THEN 'No Target'
		WHEN "Key drivers" = 'Ticketsize' AND lower("Loại tài sản") = 'Ticketsize' THEN 'No Target'
		ELSE CASE WHEN "Thực hiện"/PROGRESS >= "Mục tiêu"  THEN 'On track' ELSE 'Not on track' END 
	END "Trạng thái"	
FROM 
(
SELECT 
	A.TEN_SHOP SHOP_NM,
	A."Mã chỉ tiêu",
	A."Chỉ tiêu" "Key drivers",
	CASE 
		WHEN lower(A.LOAI_TAI_SAN) = 'total' THEN 'TỔNG' 
		WHEN lower(A.LOAI_TAI_SAN) = 'dkxm' THEN 'ĐKXM'
		WHEN lower(A.LOAI_TAI_SAN) = 'dkoto' THEN 'ĐKOTO'
	END "Loại tài sản",
	CASE 
		WHEN A."Chỉ tiêu" = 'Số lượng KHM' THEN 'HĐ'
		WHEN A."Chỉ tiêu" = 'SL KHQL' THEN 'HĐ'
		WHEN A."Chỉ tiêu" = 'Ticketsize' THEN 'Triệu VNĐ'
		WHEN A."Chỉ tiêu" = 'Thời gian vay trung bình' THEN 'Tháng'
		WHEN A."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN '%'
	END "Đơn vị",
	CASE 
		WHEN A."Chỉ tiêu" = 'Số lượng KHM' THEN round(A.VALUE_,0)
		WHEN A."Chỉ tiêu" = 'SL KHQL' THEN round(A.VALUE_,0)
		WHEN A."Chỉ tiêu" = 'Ticketsize' THEN round(A.VALUE_,2)
		WHEN A."Chỉ tiêu" = 'Thời gian vay trung bình' THEN round(A.VALUE_/30,2 )
		WHEN A."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN round(A.VALUE_,4) * 100
	END "Thực hiện",
	CASE 
		WHEN lower(A.LOAI_TAI_SAN) = 'total' THEN 
			CASE 
				WHEN A."Chỉ tiêu" = 'Số lượng KHM' THEN t.KHM
				WHEN A."Chỉ tiêu" = 'SL KHQL' THEN t.KHQL 
				WHEN A."Chỉ tiêu" = 'Ticketsize' THEN T.TICKETSIZE_TOTAL
				WHEN A."Chỉ tiêu" = 'Thời gian vay trung bình' THEN NULL 
				WHEN A."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN to_number(SUBSTR(t.TY_LE_THU , 1, LENGTH(t.TY_LE_THU ) - 1))
			END
		WHEN lower(A.LOAI_TAI_SAN) = 'dkxm' THEN 
			CASE 
				WHEN A."Chỉ tiêu" = 'Số lượng KHM' THEN t.KHM_DKXM 
				WHEN A."Chỉ tiêu" = 'SL KHQL' THEN t.KHQL_DKXM 
				WHEN A."Chỉ tiêu" = 'Ticketsize' THEN t.TICKETSIZE_DKXM 
				WHEN A."Chỉ tiêu" = 'Thời gian vay trung bình' THEN NULL 
				WHEN A."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN to_number(SUBSTR(t.TY_LE_THU , 1, LENGTH(t.TY_LE_THU ) - 1))
			END		
		WHEN lower(A.LOAI_TAI_SAN) = 'dkoto' THEN 
			CASE 
				WHEN A."Chỉ tiêu" = 'Số lượng KHM' THEN t.KHM_DKOTO  
				WHEN A."Chỉ tiêu" = 'SL KHQL' THEN t.KHQL_DKOT 
				WHEN A."Chỉ tiêu" = 'Ticketsize' THEN t.TICKETSIZE_DKOT 
				WHEN A."Chỉ tiêu" = 'Thời gian vay trung bình' THEN NULL 
				WHEN A."Chỉ tiêu" = 'Tỷ lệ thu đúng hạn' THEN to_number(SUBSTR(t.TY_LE_THU , 1, LENGTH(t.TY_LE_THU ) - 1))
			END	
		END "Mục tiêu",
	to_number(SUBSTR(c.PROGRESS, 1, LENGTH(c.PROGRESS) - 1))/100 PROGRESS
FROM 
(
SELECT 
s.shop_nm AS Ten_shop
,'2.2'AS "Mã chỉ tiêu"
, 'Số lượng KHM' AS "Chỉ tiêu"
, 'Total' AS Loai_tai_san
, count(DISTINCT customer_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F wldf
LEFT JOIN F88DWH.W_ASSET_TP_D watd ON wldf.ASSET_TYPE_WID = watd.ASSET_TP_WID 
LEFT JOIN F88DWH.w_shop_D s on wldf.shop_wid = s.shop_wid
WHERE 1=1
AND DISBURSE_DATE_WID >= TO_CHAR(TRUNC(TO_DATE(:DATE_WID,'yyyymmdd'),'mm'),'yyyymmdd')
AND DISBURSE_DATE_WID <= :DATE_WID
AND CTR_TYPE = 'Mới'
AND CHANNEL_CODE='KDML'
GROUP BY
s.shop_nm,
'Số lượng KHM'
UNION ALL 
--KHM THEO LOẠI TÀI SẢN
SELECT 
s.shop_nm AS Ten_shop
,CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '2.2.1'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '2.2.2'
ELSE 'Other' END AS  "Mã chỉ tiêu"
, 'Số lượng KHM' AS "Chỉ tiêu"
, CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
ELSE 'Other' END AS Loai_tai_san
, count(DISTINCT customer_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F wldf
LEFT JOIN F88DWH.W_ASSET_TP_D watd ON wldf.ASSET_TYPE_WID = watd.ASSET_TP_WID 
LEFT JOIN F88DWH.w_shop_D s on wldf.shop_wid = s.shop_wid
WHERE 1=1
AND DISBURSE_DATE_WID >= TO_CHAR(TRUNC(TO_DATE(:DATE_WID,'yyyymmdd'),'mm'),'yyyymmdd')
AND DISBURSE_DATE_WID <= :DATE_WID
AND CTR_TYPE = 'Mới'
AND CHANNEL_CODE='KDML'
GROUP BY
s.shop_nm,
CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '2.2.1'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '2.2.2'
ELSE 'Other' END,
'Số lượng KHM',
CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
ELSE 'Other' END 
UNION ALL
---SL KHQL TOTAL---
SELECT 
s.shop_nm AS Ten_shop
,'2.3' AS  "Mã chỉ tiêu"
, 'SL KHQL' AS "Chỉ tiêu"
, 'TOTAL' AS Loai_tai_san
, count(DISTINCT customer_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F wldf
LEFT JOIN F88DWH.w_shop_D s on wldf.shop_wid = s.shop_wid
LEFT JOIN F88DWH.W_ASSET_TP_D watd ON wldf.ASSET_TYPE_WID = watd.ASSET_TP_WID 
WHERE 1=1
	AND EXTRACT (MONTH FROM to_date(DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
	AND EXTRACT (YEAR FROM to_date(DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
	AND DISBURSE_DATE_WID <= :DATE_WID
	AND CTR_TYPE in ('Quay lại', 'Đáo')
	AND CHANNEL_CODE='KDML'
GROUP BY
s.shop_nm
,'2.3' 
, 'SL KHQL'
, 'TOTAL'
UNION ALL 
--SL KHQL THEO LOAI_TS
SELECT 
s.shop_nm AS Ten_shop
,CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '2.3.1'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '2.3.2'
ELSE 'Other' END AS  "Mã chỉ tiêu"
, 'SL KHQL' AS "Chỉ tiêu"
, CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
ELSE 'Other' END AS Loai_tai_san
, count(DISTINCT customer_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F wldf
LEFT JOIN F88DWH.w_shop_D s on wldf.shop_wid = s.shop_wid
LEFT JOIN F88DWH.W_ASSET_TP_D watd ON wldf.ASSET_TYPE_WID = watd.ASSET_TP_WID 
WHERE 1=1
	AND EXTRACT (MONTH FROM to_date(DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
	AND EXTRACT (YEAR FROM to_date(DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
	AND DISBURSE_DATE_WID <= :DATE_WID
	AND CTR_TYPE in ('Quay lại', 'Đáo')
	AND CHANNEL_CODE='KDML'
GROUP BY
s.shop_nm
, 'SL KHQL',
CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '2.3.1'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '2.3.2'
ELSE 'Other' END 
, CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
ELSE 'Other' END
UNION ALL 
----Ticketsize  TOTAL
SELECT
s.SHOP_NM AS Ten_shop
,'3.3' AS  "Mã chỉ tiêu"
, 'Ticketsize' AS "Chỉ tiêu"
, 'TOTAL' AS Loai_tai_san
, sum(DISBURSE_AMT) / 1e6 / count(DISTINCT p.loan_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F p
LEFT JOIN F88DWH.W_ASSET_TP_D watd ON p.ASSET_TYPE_WID = watd.ASSET_TP_WID 
LEFT JOIN F88DWH.w_shop_D s on p.shop_wid = s.shop_wid
LEFT JOIN F88DWH.W_AREA_MANAGER_D a  ON trim(a.SHOP_ID) = s.SHOP_CODE
WHERE 1=1
AND EXTRACT (YEAR FROM to_date(p.DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
AND EXTRACT (MONTH FROM to_date(p.DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
AND p.DISBURSE_DATE_WID <= :DATE_WID
AND P.CHANNEL_CODE='KDML'
GROUP BY 
s.SHOP_NM 
,'3.3' 
, 'Ticketsize' 
, 'TOTAL'
UNION ALL
-----Ticketsize THEO LOẠI TS
SELECT
s.SHOP_NM AS Ten_shop
,CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '3.3.1'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '3.3.2'
ELSE 'Other' END AS  "Mã chỉ tiêu"
, 'Ticketsize' AS "Chỉ tiêu"
, CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
ELSE 'Other' END AS Loai_tai_san
, sum(DISBURSE_AMT) / 1e6 / count(DISTINCT p.loan_code) AS Value_
	,:DATE_WID DATE_WID
FROM F88DWH.W_LOAN_DTL_F p
LEFT JOIN F88DWH.W_ASSET_TP_D watd ON p.ASSET_TYPE_WID = watd.ASSET_TP_WID 
LEFT JOIN F88DWH.w_shop_D s on p.shop_wid = s.shop_wid
LEFT JOIN F88DWH.W_AREA_MANAGER_D a  ON trim(a.SHOP_ID) = s.SHOP_CODE
WHERE 1=1
AND EXTRACT (YEAR FROM to_date(p.DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
AND EXTRACT (MONTH FROM to_date(p.DISBURSE_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
AND p.DISBURSE_DATE_WID <= :DATE_WID
AND P.CHANNEL_CODE='KDML'
GROUP BY 
s.SHOP_NM 
, 'Ticketsize'
,CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '3.3.1'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '3.3.2'
ELSE 'Other' END
, CASE 
	WHEN watd.ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
	WHEN watd.ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
ELSE 'Other' END
UNION ALL 
----Thời gian vay trung bìnH TOTAL
       SELECT 
        	shop_nm Ten_shop,
	        '3.4'  "Mã chỉ tiêu",
			 'Thời gian vay trung bình' AS "Chỉ tiêu",
			'TOTAL' Loai_tai_san,
	        sum(closed_period)/COUNT(DISTINCT LOAN_CODE) AS Value_
        	,:DATE_WID DATE_WID
FROM
        (
        SELECT  watd.ASSET_TP_NM ,
	       A.LOAN_CODE,
	       wsd.shop_code,
	       wsd.shop_nm,
	       DISBURSE_DATE_WID,
	       CLOSED_DATE_WID,
	       watd.ASSET_TP_CODE,
	       CASE WHEN CLOSED_DATE_WID IS NOT NULL THEN TO_DATE(CLOSED_DATE_WID,'yyyy-mm-dd')- to_date(DISBURSE_DATE_WID,'yyyy-mm-dd') ELSE 0 END as closed_period
        FROM F88DWH.W_LOAN_DTL_F A
        LEFT JOIN F88DWH.W_SHOP_D wsd
               ON A.SHOP_WID = wsd.SHOP_WID
        LEFT JOIN F88DWH.W_AREA_MANAGER_D wamd
              ON wsd.shop_code = TRIM(WAMD.SHOP_ID)
        LEFT JOIN F88DWH.W_ASSET_TP_D watd
               ON A.ASSET_TYPE_WID = watd.ASSET_TP_WID
        WHERE 1=1
	        AND EXTRACT (YEAR FROM to_date(A.CLOSED_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
			AND EXTRACT (MONTH FROM to_date(A.CLOSED_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
			AND A.CLOSED_DATE_WID <= :DATE_WID
	        AND a.channel_code='KDML'
--	        AND WATD.ASSET_TP_CODE IN ('00000015','00000017')
	        AND A.STATUS='600'
         ) a        
         GROUP BY 
        	shop_nm ,
	        '3.4'  ,
			 'Thời gian vay trung bình',
			'TOTAL' 
UNION ALL 
---Thời gian vay trung bìnH THEO LOAI_TS
       SELECT 
        	shop_nm Ten_shop,
	        CASE WHEN ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '3.4.1'
				 WHEN ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '3.4.2'
			ELSE 'Other' END AS  "Mã chỉ tiêu",
			 'Thời gian vay trung bình' AS "Chỉ tiêu",
			CASE 
				WHEN ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
				WHEN ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
			ELSE 'Other' END AS Loai_tai_san,
	        sum(closed_period)/COUNT(DISTINCT LOAN_CODE) AS Value_
        	,:DATE_WID DATE_WID
FROM
        (
        SELECT  watd.ASSET_TP_NM ,
	       A.LOAN_CODE,
	       wsd.shop_code,
	       wsd.shop_nm,
	       DISBURSE_DATE_WID,
	       CLOSED_DATE_WID,
	       watd.ASSET_TP_CODE,
	       CASE WHEN CLOSED_DATE_WID IS NOT NULL THEN TO_DATE(CLOSED_DATE_WID,'yyyy-mm-dd')- to_date(DISBURSE_DATE_WID,'yyyy-mm-dd') ELSE 0 END as closed_period
        FROM F88DWH.W_LOAN_DTL_F A
        LEFT JOIN F88DWH.W_SHOP_D wsd
               ON A.SHOP_WID = wsd.SHOP_WID
        LEFT JOIN F88DWH.W_AREA_MANAGER_D wamd
              ON wsd.shop_code = TRIM(WAMD.SHOP_ID)
        LEFT JOIN F88DWH.W_ASSET_TP_D watd
               ON A.ASSET_TYPE_WID = watd.ASSET_TP_WID
        WHERE 1=1
	        AND EXTRACT (YEAR FROM to_date(A.CLOSED_DATE_WID,'yyyymmdd')) = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
			AND EXTRACT (MONTH FROM to_date(A.CLOSED_DATE_WID,'yyyymmdd')) = EXTRACT (MONTH FROM to_date(:DATE_WID,'yyyymmdd'))
			AND A.CLOSED_DATE_WID <= :DATE_WID
	        AND a.channel_code='KDML'
--	        AND WATD.ASSET_TP_CODE IN ('00000015','00000017')
	        AND A.STATUS='600'
         ) a        
         GROUP BY 
        	shop_nm ,
	        CASE WHEN ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '3.4.1'
				 WHEN ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '3.4.2'
			 ELSE'Other' END ,
			 'Thời gian vay trung bình' ,
			CASE 
				WHEN ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
				WHEN ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
			ELSE 'Other' END 
UNION ALL 
--Tỷ lệ thu đúng hạn TOTAL 
SELECT 		
	shop_nm AS Ten_shop,
	'3.5' AS "Mã chỉ tiêu",
	'Tỷ lệ thu đúng hạn' AS "Chỉ tiêu",
	'TOTAL'  LOAI_TAI_SAN,
	Value_
	,:DATE_WID DATE_WID
FROM 
	(--Tỷ lệ thu Total
WITH CAL AS
(
	SELECT
		DATE_WID ,
		DATE_TM ,
		YEAR_NUM ,
		MONTH_NUM ,
		TO_CHAR(DATE_TM,'YYYYMM') YEAR_MONTH ,
		EXTRACT( YEAR FROM DATE_TM +1) YEAR_NUM_N ,
		EXTRACT( MONTH FROM DATE_TM +1) MONTH_NUM_N ,
		TO_CHAR(DATE_TM+1,'YYYYMM') YEAR_MONTH_N ,
		TO_NUMBER(TO_CHAR(DATE_TM-1,'YYYYMMDD')) DATE_WID_P ,
		EXTRACT( YEAR FROM DATE_TM -1) YEAR_NUM_P ,
		EXTRACT( MONTH FROM DATE_TM -1) MONTH_NUM_P ,
		LAST_DOM_FLAG
	FROM
		F88DWH.W_CALENDAR_D
	WHERE
		DATE_TM >= ADD_MONTHS( TRUNC(to_date(:DATE_WID,'yyyymmdd'), 'MM')-1,-1)
		AND DATE_TM <= to_date(:DATE_WID,'yyyymmdd') 
)
, DN AS -- DNĐK BAO GỒM NHÓM TRONG HẠN (ĐẾN HẠN TRONG THÁNG TỚI) & NHÓM QUÁ HẠN (1-29 NGÀY)
(
	SELECT
	YEAR_NUM_N YEAR_NUM,
	MONTH_NUM_N MONTH_NUM,
	YEAR_MONTH_N YEAR_MONTH,
	CAL.DATE_TM,
	CAL.DATE_WID,
	LDA.LOAN_CODE,
	LDA.LOAN_WID, 
	LDA.SCHEDULE_NEXT_DATE,
	CAL.DATE_TM - LDA.OVERDUE_DAYS SCH_DT,
	LDA.PRINCIPAL_REMAIN,
	LDA.OVERDUE_DAYS OPENING_OVERDUE_DAYS,
	CASE WHEN LDA.OVERDUE_DAYS <= 0 THEN LDA.PRINCIPAL_REMAIN ELSE 0 END DNTH_DK,
	CASE WHEN LDA.OVERDUE_DAYS > 0 THEN LDA.PRINCIPAL_REMAIN ELSE 0 END DNQH_DK,
	CASE WHEN LDA.OVERDUE_DAYS <= 0 THEN 'TRONG HẠN' ELSE 'QUÁ HẠN' END OPENING_STATUS,
	ATP.ASSET_TP_NM,
	S.SHOP_CODE,
	S.SHOP_NM,
	CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
	CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
	LDT.FUND_NAME,
	LDT.CUSTOMER_WID,
	LDT.REFINANCED_TO ,
	LDT.PACKAGE_WID 
FROM
	CAL,
	F88DWH.W_LOAN_DAILY_F LDA,
	F88DWH.W_SHOP_D S,
	F88DWH.W_ASSET_TP_D ATP,
	F88DWH.W_LOAN_DTL_F LDT
WHERE
	CAl.LAST_DOM_FLAG = 1
	AND CAL.YEAR_NUM = LDA.YEAR_NUM
	AND CAL.MONTH_NUM = LDA.MONTH_NUM 
	AND CAL.DATE_WID = LDA.DATE_WID
	--
	AND LDA.OVERDUE_DAYS <= 29
	AND LDA.STATUS <> 603
	--
	AND S.SHOP_WID = LDT.SHOP_WID
	AND LDA.ASSET_TYPE_WID = ATP.ASSET_TP_WID
	AND ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
	AND (TO_CHAR(CAL.DATE_TM+1,'YYYYMM') = TO_CHAR(CAL.DATE_TM - LDA.OVERDUE_DAYS,'YYYYMM') OR LDA.OVERDUE_DAYS > 0)
	--
	AND LDT.LOAN_WID = LDA.LOAN_WID
),
H AS 
(
SELECT
	P.*,
	CASE
		WHEN PAK.PACKAGE_NM LIKE '%HOTRO%' THEN 'Đóng & đảo nợ'
	END LOAN_STATUS ,
	PAK2.PACKAGE_NM
FROM
(
(
	SELECT
		DN.YEAR_NUM,
		DN.MONTH_NUM,
		DN.YEAR_MONTH,
		DN.DATE_TM,
		DN.LOAN_CODE,
		DN.LOAN_WID,
		DN.SCH_DT,
		DN.PRINCIPAL_REMAIN/1E6 DU_NO,
--		DN.PRINCIPAL_REMAIN DU_NO,
		DN.OPENING_STATUS,
		DN.OPENING_OVERDUE_DAYS,
		LS.COMPLETED_PAID_DATE ,
		LDA.SCHEDULE_NEXT_DATE ENDING_SCHEDULE_NEXT_DATE,
		CASE 
			WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(DN.DATE_TM+1,'YYYYMM') THEN LS.OVERDUE_DAYS
			WHEN LS.OVERDUE_DAYS IS NOT NULL THEN ADD_MONTHS(DN.DATE_TM, 1)-DN.SCH_DT
			ELSE LDA.OVERDUE_DAYS
		END ENDING_OVERDUE_DAYS,
		CASE 
			 WHEN (CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA.OVERDUE_DAYS END) >=31 THEN 'ROLL31'
			 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
			 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(DN.DATE_TM+1,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
			 WHEN DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			 ELSE 'CHƯA THU ĐƯỢC'
		END COLLECTION_STATUS,
		CASE WHEN DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS ,
		DN.ASSET_TP_NM,
		DN.SHOP_CODE,
		DN.SHOP_NM,
		DN.CHANNEL_CODE,
		DN.PARTNER_CODE,
		DN.FUND_NAME,
		UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
		DN.REFINANCED_TO ,
		DN.PACKAGE_WID
	FROM
		DN
		LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON DN.LOAN_WID = LS.LOAN_WID
											AND DN.SCH_DT = LS.TO_DATE
		LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA ON DN.LOAN_WID = LDA.LOAN_WID
										   AND LS.COMPLETED_PAID_DATE IS NULL
										   AND LDA.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
										   AND LDA.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.DATE_WID = :DATE_WID
		LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON DN.CUSTOMER_WID = CUS.CUSTOMER_WID
)
UNION --Giải ngân mới và đến hạn trong kỳ
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		CAL.YEAR_MONTH,
		CAL.DATE_TM DISBURSE_DT,
		LDT.LOAN_CODE,
		LDT.LOAN_WID ,
		LS.TO_DATE SCH_DT,
		LDT.DISBURSE_AMT/1E6 DU_NO,
--		LDT.DISBURSE_AMT DU_NO,
		'GIẢI NGÂN MỚI' OPENING_STATUS,
		NULL OPENING_OVERDUE_DAYS,
		LS.COMPLETED_PAID_DATE ,
		NULL ENDING_SCHEDULE_NEXT_DATE,
		CASE 
			WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') THEN LS.OVERDUE_DAYS
			WHEN LS.OVERDUE_DAYS IS NOT NULL THEN ADD_MONTHS(TRUNC(LS.TO_DATE,'MM')-1,1)-LS.TO_DATE
			ELSE LDA.OVERDUE_DAYS
		END ENDING_OVERDUE_DAYS,
		CASE 
			 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
			 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
			 WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			 ELSE 'CHƯA THU ĐƯỢC'
		END COLLECTION_STATUS,
		CASE WHEN LS.TO_DATE >to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS,
		ATP.ASSET_TP_NM,
		S.SHOP_CODE,
		S.SHOP_NM,
		CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
		CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
		LDT.FUND_NAME ,
		UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
		LDT.REFINANCED_TO ,
		LDT.PACKAGE_WID 
	FROM F88DWH.W_LOAN_DTL_F LDT
		JOIN CAL ON LDT.DISBURSE_DATE_WID  = CAL.DATE_WID AND CAL.LAST_DOM_FLAG = 0
		LEFT JOIN F88DWH.W_ASSET_TP_D ATP ON LDT.ASSET_TYPE_WID = ATP.ASSET_TP_WID 
		LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON LDT.LOAN_WID = LS.LOAN_WID AND LS.SCHEDULE_NUM = 1
		LEFT JOIN F88DWH.W_SHOP_D S ON LDT.SHOP_WID = S.SHOP_WID
		LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON LDT.CUSTOMER_WID = CUS.CUSTOMER_WID 
		LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA ON LDT.LOAN_WID = LDA.LOAN_WID
										   AND LS.COMPLETED_PAID_DATE IS NULL
										   AND LDA.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.DATE_WID =:DATE_WID
	WHERE ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
		AND EXTRACT (YEAR FROM LS.TO_DATE)  = CAL.YEAR_NUM
		AND EXTRACT (MONTH FROM LS.TO_DATE)  = CAL.MONTH_NUM
		AND (LDT.DISBURSE_DATE_WID <> LDT.CLOSED_DATE_WID  OR LDT.CLOSED_DATE_WID IS NULL)
)
UNION --ROLLBACK và đến hạn trong kỳ
		(
		SELECT
			CAL.YEAR_NUM,
			CAL.MONTH_NUM,
			CAL.YEAR_MONTH,
			CAL.DATE_TM ROLL_BACK_DT,
			LDA.LOAN_CODE,
			LDA.LOAN_WID,
			LDA.SCHEDULE_NEXT_DATE SCH_DT,
			LDA.PRINCIPAL_REMAIN/1E6 DU_NO,
--			LDA.PRINCIPAL_REMAIN DU_NO,
			'ROLL BACK' OPENING_STATUS,
			LDA.OVERDUE_DAYS OPENING_OVERDUE_DAYS,
			LS.COMPLETED_PAID_DATE,
			LDA2.SCHEDULE_NEXT_DATE ENDING_SCHEDULE_NEXT_DATE,
			CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA2.OVERDUE_DAYS END ENDING_OVERDUE_DAYS,
			CASE 
				 WHEN (CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA2.OVERDUE_DAYS END) >=31 THEN 'ROLL31' 
				 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
			     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
				 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
				 WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
				 ELSE 'CHƯA THU ĐƯỢC'
			END COLLECTION_STATUS,
			CASE WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS,
			ATP.ASSET_TP_NM,
			S.SHOP_CODE,
			S.SHOP_NM,
			CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
			CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
			LDT.FUND_NAME ,
			UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
			LDT.REFINANCED_TO ,
			LDT.PACKAGE_WID 
		FROM
			F88DWH.W_LOAN_DAILY_F LDA
			JOIN CAL ON	LDA.YEAR_NUM = CAL.YEAR_NUM
					 AND LDA.MONTH_NUM = CAL.MONTH_NUM
					 AND LDA.DATE_WID = CAL.DATE_WID
			JOIN F88DWH.W_LOAN_DAILY_F LDA1 ON LDA1.YEAR_NUM = CAL.YEAR_NUM_P
											  AND LDA1.MONTH_NUM = CAL.MONTH_NUM_P
											  AND LDA1.DATE_WID = CAL.DATE_WID_P
											  AND LDA1.LOAN_WID = LDA.LOAN_WID
											  AND LDA1.OVERDUE_DAYS > 0
			LEFT JOIN F88DWH.W_LOAN_DTL_F LDT ON LDA.LOAN_WID = LDT.LOAN_WID 
			LEFT JOIN F88DWH.W_ASSET_TP_D ATP ON LDT.ASSET_TYPE_WID = ATP.ASSET_TP_WID 
			LEFT JOIN F88DWH.W_SHOP_D S ON LDT.SHOP_WID = S.SHOP_WID 
			LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON LDT.CUSTOMER_WID = CUS.CUSTOMER_WID 
			LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON LDA.LOAN_WID = LS.LOAN_WID
										  AND LDA.SCHEDULE_NEXT_DATE = LS.TO_DATE 
			LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA2 ON LDA.LOAN_WID = LDA2.LOAN_WID 
												 AND LS.COMPLETED_PAID_DATE IS NULL
												 AND LDA2.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
											     AND LDA2.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
											     AND LDA2.DATE_WID =:DATE_WID
		WHERE LDA.OVERDUE_DAYS <= 0
			AND ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
			AND EXTRACT (YEAR FROM LDA.SCHEDULE_NEXT_DATE)  = CAL.YEAR_NUM
			AND EXTRACT (MONTH FROM LDA.SCHEDULE_NEXT_DATE)  = CAL.MONTH_NUM
		)
) P
LEFT JOIN F88DWH.W_LOAN_DTL_F LDT ON P.REFINANCED_TO = LDT.INTEGRATION_ID
LEFT JOIN F88DWH.W_PACKAGE_D PAK ON LDT.PACKAGE_WID = PAK.PACKAGE_WID
LEFT JOIN F88DWH.W_PACKAGE_D PAK2 ON P.PACKAGE_WID = PAK2.PACKAGE_WID
)
SELECT 
		H.YEAR_NUM,
		H.MONTH_NUM,
		H.SHOP_CODE,
		H.SHOP_NM,	
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) AND H.COLLECTION_STATUS IN ('THU ĐÚNG HẠN','TẤT TOÁN HĐ TRƯỚC HẠN') THEN DU_NO END) DN_THU_DUNG_HAN,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) THEN DU_NO END) DN_CAN_THU ,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) AND H.COLLECTION_STATUS IN ('THU ĐÚNG HẠN','TẤT TOÁN HĐ TRƯỚC HẠN') THEN DU_NO END)/SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) THEN DU_NO END) value_
FROM  H
	WHERE 1=1
		AND YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd') )
		AND YEAR_MONTH=to_char(to_date(:DATE_WID,'yyyymmdd'),'rrrrmm')
		AND CHANNEL_CODE='KDML'
		AND DEBT_STATUS='ĐÃ ĐẾN HẠN'
		AND H.OPENING_STATUS <>'QUÁ HẠN'
		AND loan_status IS NULL  
		AND h.PACKAGE_NM NOT IN ('F88_DKOT_HOTRO','F88_ĐKXM_HOTRO')
	GROUP BY 
		H.YEAR_NUM,
		H.MONTH_NUM,
		H.SHOP_CODE,
		H.SHOP_NM)
UNION ALL 
--TỶ LỆ THU ĐÚNG HẠN THEO LOẠI TS
SELECT 		
	shop_nm AS Ten_shop,
	CASE 
		WHEN ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN '3.5.1'
		WHEN ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN '3.5.2'
	ELSE 'Other' END  AS "Mã chỉ tiêu",
	'Tỷ lệ thu đúng hạn' AS "Chỉ tiêu",
	CASE 
		WHEN ASSET_TP_NM IN ('Xe máy', 'Đăng ký xe máy') THEN 'DKXM'
		WHEN ASSET_TP_NM IN ('Ô tô', 'Đăng ký Ô tô') THEN 'DKOTO'
	ELSE 'Other' END  LOAI_TAI_SAN,
	Value_
	,:DATE_WID DATE_WID
FROM 
	(--Tỷ lệ thu đúng hạn theo tài sản:
WITH CAL AS
(
	SELECT
		DATE_WID ,
		DATE_TM ,
		YEAR_NUM ,
		MONTH_NUM ,
		TO_CHAR(DATE_TM,'YYYYMM') YEAR_MONTH ,
		EXTRACT( YEAR FROM DATE_TM +1) YEAR_NUM_N ,
		EXTRACT( MONTH FROM DATE_TM +1) MONTH_NUM_N ,
		TO_CHAR(DATE_TM+1,'YYYYMM') YEAR_MONTH_N ,
		TO_NUMBER(TO_CHAR(DATE_TM-1,'YYYYMMDD')) DATE_WID_P ,
		EXTRACT( YEAR FROM DATE_TM -1) YEAR_NUM_P ,
		EXTRACT( MONTH FROM DATE_TM -1) MONTH_NUM_P ,
		LAST_DOM_FLAG
	FROM
		F88DWH.W_CALENDAR_D
	WHERE
		DATE_TM >= ADD_MONTHS( TRUNC(to_date(:DATE_WID,'yyyymmdd'), 'MM')-1,-1)
		AND DATE_TM <= to_date(:DATE_WID,'yyyymmdd') 
)
, DN AS -- DNĐK BAO GỒM NHÓM TRONG HẠN (ĐẾN HẠN TRONG THÁNG TỚI) & NHÓM QUÁ HẠN (1-29 NGÀY)
(
	SELECT
	YEAR_NUM_N YEAR_NUM,
	MONTH_NUM_N MONTH_NUM,
	YEAR_MONTH_N YEAR_MONTH,
	CAL.DATE_TM,
	CAL.DATE_WID,
	LDA.LOAN_CODE,
	LDA.LOAN_WID, 
	LDA.SCHEDULE_NEXT_DATE,
	CAL.DATE_TM - LDA.OVERDUE_DAYS SCH_DT,
	LDA.PRINCIPAL_REMAIN,
	LDA.OVERDUE_DAYS OPENING_OVERDUE_DAYS,
	CASE WHEN LDA.OVERDUE_DAYS <= 0 THEN LDA.PRINCIPAL_REMAIN ELSE 0 END DNTH_DK,
	CASE WHEN LDA.OVERDUE_DAYS > 0 THEN LDA.PRINCIPAL_REMAIN ELSE 0 END DNQH_DK,
	CASE WHEN LDA.OVERDUE_DAYS <= 0 THEN 'TRONG HẠN' ELSE 'QUÁ HẠN' END OPENING_STATUS,
	ATP.ASSET_TP_NM,
	S.SHOP_CODE,
	S.SHOP_NM,
	CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
	CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
	LDT.FUND_NAME,
	LDT.CUSTOMER_WID,
	LDT.REFINANCED_TO ,
	LDT.PACKAGE_WID 
FROM
	CAL,
	F88DWH.W_LOAN_DAILY_F LDA,
	F88DWH.W_SHOP_D S,
	F88DWH.W_ASSET_TP_D ATP,
	F88DWH.W_LOAN_DTL_F LDT
WHERE
	CAl.LAST_DOM_FLAG = 1
	AND CAL.YEAR_NUM = LDA.YEAR_NUM
	AND CAL.MONTH_NUM = LDA.MONTH_NUM 
	AND CAL.DATE_WID = LDA.DATE_WID
	--
	AND LDA.OVERDUE_DAYS <= 29
	AND LDA.STATUS <> 603
	--
	AND S.SHOP_WID = LDT.SHOP_WID
	AND LDA.ASSET_TYPE_WID = ATP.ASSET_TP_WID
	AND ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
	AND (TO_CHAR(CAL.DATE_TM+1,'YYYYMM') = TO_CHAR(CAL.DATE_TM - LDA.OVERDUE_DAYS,'YYYYMM') OR LDA.OVERDUE_DAYS > 0)
	--
	AND LDT.LOAN_WID = LDA.LOAN_WID
),
H AS 
(
SELECT
	P.*,
	CASE
		WHEN PAK.PACKAGE_NM LIKE '%HOTRO%' THEN 'Đóng & đảo nợ'
	END LOAN_STATUS ,
	PAK2.PACKAGE_NM
FROM
(
(
	SELECT
		DN.YEAR_NUM,
		DN.MONTH_NUM,
		DN.YEAR_MONTH,
		DN.DATE_TM,
		DN.LOAN_CODE,
		DN.LOAN_WID,
		DN.SCH_DT,
		DN.PRINCIPAL_REMAIN/1E6 DU_NO,
--		DN.PRINCIPAL_REMAIN DU_NO,
		DN.OPENING_STATUS,
		DN.OPENING_OVERDUE_DAYS,
		LS.COMPLETED_PAID_DATE ,
		LDA.SCHEDULE_NEXT_DATE ENDING_SCHEDULE_NEXT_DATE,
		CASE 
			WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(DN.DATE_TM+1,'YYYYMM') THEN LS.OVERDUE_DAYS
			WHEN LS.OVERDUE_DAYS IS NOT NULL THEN ADD_MONTHS(DN.DATE_TM, 1)-DN.SCH_DT
			ELSE LDA.OVERDUE_DAYS
		END ENDING_OVERDUE_DAYS,
		CASE 
			 WHEN (CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA.OVERDUE_DAYS END) >=31 THEN 'ROLL31'
			 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
			 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(DN.DATE_TM+1,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
			 WHEN DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			 ELSE 'CHƯA THU ĐƯỢC'
		END COLLECTION_STATUS,
		CASE WHEN DN.SCH_DT > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS ,
		DN.ASSET_TP_NM,
		DN.SHOP_CODE,
		DN.SHOP_NM,
		DN.CHANNEL_CODE,
		DN.PARTNER_CODE,
		DN.FUND_NAME,
		UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
		DN.REFINANCED_TO ,
		DN.PACKAGE_WID
	FROM
		DN
		LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON DN.LOAN_WID = LS.LOAN_WID
											AND DN.SCH_DT = LS.TO_DATE
		LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA ON DN.LOAN_WID = LDA.LOAN_WID
										   AND LS.COMPLETED_PAID_DATE IS NULL
										   AND LDA.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
										   AND LDA.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.DATE_WID = :DATE_WID
		LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON DN.CUSTOMER_WID = CUS.CUSTOMER_WID
)
UNION --Giải ngân mới và đến hạn trong kỳ
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		CAL.YEAR_MONTH,
		CAL.DATE_TM DISBURSE_DT,
		LDT.LOAN_CODE,
		LDT.LOAN_WID ,
		LS.TO_DATE SCH_DT,
		LDT.DISBURSE_AMT/1E6 DU_NO,
--		LDT.DISBURSE_AMT DU_NO,
		'GIẢI NGÂN MỚI' OPENING_STATUS,
		NULL OPENING_OVERDUE_DAYS,
		LS.COMPLETED_PAID_DATE ,
		NULL ENDING_SCHEDULE_NEXT_DATE,
		CASE 
			WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') THEN LS.OVERDUE_DAYS
			WHEN LS.OVERDUE_DAYS IS NOT NULL THEN ADD_MONTHS(TRUNC(LS.TO_DATE,'MM')-1,1)-LS.TO_DATE
			ELSE LDA.OVERDUE_DAYS
		END ENDING_OVERDUE_DAYS,
		CASE 
			 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
		     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
			 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
			 WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			 ELSE 'CHƯA THU ĐƯỢC'
		END COLLECTION_STATUS,
		CASE WHEN LS.TO_DATE >to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS,
		ATP.ASSET_TP_NM,
		S.SHOP_CODE,
		S.SHOP_NM,
		CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
		CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
		LDT.FUND_NAME ,
		UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
		LDT.REFINANCED_TO ,
		LDT.PACKAGE_WID 
	FROM F88DWH.W_LOAN_DTL_F LDT
		JOIN CAL ON LDT.DISBURSE_DATE_WID  = CAL.DATE_WID AND CAL.LAST_DOM_FLAG = 0
		LEFT JOIN F88DWH.W_ASSET_TP_D ATP ON LDT.ASSET_TYPE_WID = ATP.ASSET_TP_WID 
		LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON LDT.LOAN_WID = LS.LOAN_WID AND LS.SCHEDULE_NUM = 1
		LEFT JOIN F88DWH.W_SHOP_D S ON LDT.SHOP_WID = S.SHOP_WID
		LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON LDT.CUSTOMER_WID = CUS.CUSTOMER_WID 
		LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA ON LDT.LOAN_WID = LDA.LOAN_WID
										   AND LS.COMPLETED_PAID_DATE IS NULL
										   AND LDA.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
										   AND LDA.DATE_WID =:DATE_WID
	WHERE ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
		AND EXTRACT (YEAR FROM LS.TO_DATE)  = CAL.YEAR_NUM
		AND EXTRACT (MONTH FROM LS.TO_DATE)  = CAL.MONTH_NUM
		AND (LDT.DISBURSE_DATE_WID <> LDT.CLOSED_DATE_WID  OR LDT.CLOSED_DATE_WID IS NULL)
)
UNION --ROLLBACK và đến hạn trong kỳ
		(
		SELECT
			CAL.YEAR_NUM,
			CAL.MONTH_NUM,
			CAL.YEAR_MONTH,
			CAL.DATE_TM ROLL_BACK_DT,
			LDA.LOAN_CODE,
			LDA.LOAN_WID,
			LDA.SCHEDULE_NEXT_DATE SCH_DT,
			LDA.PRINCIPAL_REMAIN/1E6 DU_NO,
--			LDA.PRINCIPAL_REMAIN DU_NO,
			'ROLL BACK' OPENING_STATUS,
			LDA.OVERDUE_DAYS OPENING_OVERDUE_DAYS,
			LS.COMPLETED_PAID_DATE,
			LDA2.SCHEDULE_NEXT_DATE ENDING_SCHEDULE_NEXT_DATE,
			CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA2.OVERDUE_DAYS END ENDING_OVERDUE_DAYS,
			CASE 
				 WHEN (CASE WHEN LS.OVERDUE_DAYS IS NOT NULL THEN LS.OVERDUE_DAYS ELSE LDA2.OVERDUE_DAYS END) >=31 THEN 'ROLL31' 
				 WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS < 0 THEN 'TẤT TOÁN HĐ TRƯỚC HẠN'
			     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 AND LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
			     WHEN LS.COMPLETED_PAID_DATE IS NOT NULL AND LS.OVERDUE_DAYS = 0 THEN 'THU ĐÚNG HẠN'
				 WHEN TO_CHAR(LS.COMPLETED_PAID_DATE,'YYYYMM') = TO_CHAR(CAL.DATE_TM,'YYYYMM') AND LS.OVERDUE_DAYS > 0 THEN 'THU QUÁ HẠN'
				 WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA CẦN THU'
				 ELSE 'CHƯA THU ĐƯỢC'
			END COLLECTION_STATUS,
			CASE WHEN LS.TO_DATE > to_date(:DATE_WID,'yyyymmdd') THEN 'CHƯA ĐẾN HẠN' ELSE 'ĐÃ ĐẾN HẠN' END DEBT_STATUS,
			ATP.ASSET_TP_NM,
			S.SHOP_CODE,
			S.SHOP_NM,
			CASE WHEN LDT.CHANNEL_CODE LIKE '%E2E%' THEN 'E2E' ELSE LDT.CHANNEL_CODE END CHANNEL_CODE,
			CASE WHEN LDT.PARTNER_CODE IS NULL THEN LDT.CHANNEL_CODE ELSE LDT.PARTNER_CODE END PARTNER_CODE,
			LDT.FUND_NAME ,
			UPPER( CUS.CUSTOMER_NM ) CUSTOMER_NM ,
			LDT.REFINANCED_TO ,
			LDT.PACKAGE_WID 
		FROM
			F88DWH.W_LOAN_DAILY_F LDA
			JOIN CAL ON	LDA.YEAR_NUM = CAL.YEAR_NUM
					 AND LDA.MONTH_NUM = CAL.MONTH_NUM
					 AND LDA.DATE_WID = CAL.DATE_WID
			JOIN F88DWH.W_LOAN_DAILY_F LDA1 ON LDA1.YEAR_NUM = CAL.YEAR_NUM_P
											  AND LDA1.MONTH_NUM = CAL.MONTH_NUM_P
											  AND LDA1.DATE_WID = CAL.DATE_WID_P
											  AND LDA1.LOAN_WID = LDA.LOAN_WID
											  AND LDA1.OVERDUE_DAYS > 0
			LEFT JOIN F88DWH.W_LOAN_DTL_F LDT ON LDA.LOAN_WID = LDT.LOAN_WID 
			LEFT JOIN F88DWH.W_ASSET_TP_D ATP ON LDT.ASSET_TYPE_WID = ATP.ASSET_TP_WID 
			LEFT JOIN F88DWH.W_SHOP_D S ON LDT.SHOP_WID = S.SHOP_WID 
			LEFT JOIN F88DWH.VW_W_CUSTOMER_D CUS ON LDT.CUSTOMER_WID = CUS.CUSTOMER_WID 
			LEFT JOIN F88DWH.W_LOAN_SCH_DTL_F LS ON LDA.LOAN_WID = LS.LOAN_WID
										  AND LDA.SCHEDULE_NEXT_DATE = LS.TO_DATE 
			LEFT JOIN F88DWH.W_LOAN_DAILY_F LDA2 ON LDA.LOAN_WID = LDA2.LOAN_WID 
												 AND LS.COMPLETED_PAID_DATE IS NULL
												 AND LDA2.YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd'))
											     AND LDA2.MONTH_NUM = EXTRACT (MONTH  FROM to_date(:DATE_WID,'yyyymmdd') )
											     AND LDA2.DATE_WID =:DATE_WID
		WHERE LDA.OVERDUE_DAYS <= 0
			AND ATP.ASSET_TP_NM IN ('Đăng ký Ô tô','Đăng ký xe máy')
			AND EXTRACT (YEAR FROM LDA.SCHEDULE_NEXT_DATE)  = CAL.YEAR_NUM
			AND EXTRACT (MONTH FROM LDA.SCHEDULE_NEXT_DATE)  = CAL.MONTH_NUM
		)
) P
LEFT JOIN F88DWH.W_LOAN_DTL_F LDT ON P.REFINANCED_TO = LDT.INTEGRATION_ID
LEFT JOIN F88DWH.W_PACKAGE_D PAK ON LDT.PACKAGE_WID = PAK.PACKAGE_WID
LEFT JOIN F88DWH.W_PACKAGE_D PAK2 ON P.PACKAGE_WID = PAK2.PACKAGE_WID
)
SELECT 
		H.YEAR_NUM,
		H.MONTH_NUM,
		H.SHOP_CODE,
		H.SHOP_NM,	
		ASSET_TP_NM,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) AND H.COLLECTION_STATUS IN ('THU ĐÚNG HẠN','TẤT TOÁN HĐ TRƯỚC HẠN') THEN DU_NO END) DN_THU_DUNG_HAN,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) THEN DU_NO END) DN_CAN_THU ,
		SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) AND H.COLLECTION_STATUS IN ('THU ĐÚNG HẠN','TẤT TOÁN HĐ TRƯỚC HẠN') THEN DU_NO END)/SUM(CASE WHEN extract(MONTH FROM H.SCH_DT)=EXTRACT(MONTH FROM to_date(:DATE_WID,'yyyymmdd')) THEN DU_NO END) Value_
FROM  H
	WHERE 1=1
		AND YEAR_NUM = EXTRACT (YEAR FROM to_date(:DATE_WID,'yyyymmdd') )
		AND YEAR_MONTH=to_char(to_date(:DATE_WID,'yyyymmdd'),'rrrrmm')
		AND CHANNEL_CODE='KDML'
		AND DEBT_STATUS='ĐÃ ĐẾN HẠN'
		AND H.OPENING_STATUS <>'QUÁ HẠN'
		AND loan_status IS NULL  
		AND h.PACKAGE_NM NOT IN ('F88_DKOT_HOTRO','F88_ĐKXM_HOTRO')
	GROUP BY 
		H.YEAR_NUM,
		H.MONTH_NUM,		
		H.SHOP_CODE,
		H.SHOP_NM,
		ASSET_TP_NM)
) a 
LEFT JOIN sontb2.PGD_TARGET t ON TRIM(A.Ten_shop) = t."PGD" 
LEFT JOIN sontb2.WORKING_DAYS c ON a.DATE_WID = c.DATE_WID
WHERE 1=1
--AND A.TEN_SHOP = 'HNI17032.2A Trần Duy Hưng'
AND c.DATE_WID = :DATE_WID
--and B.CVKD_CODE = 'F02183'
)
ORDER BY  "Mã chỉ tiêu" ASC  

--split 
-- VH_QUERY
WITH pol AS 
(
SELECT 
	b.SHOP_NAME ,
	b.SHOP_CODE ,
	a.FORM_ASSET ,
	a.COMMENT1 ,
	a.FORM_CREATED_DT ,
	a.DISBURSE_DT ,
	a.STATUS,
	a.SHOP_REV_DT
FROM F88DWH.W_PAWN_ONLINE_TLS_F a
LEFT JOIN F88DWH.W_AREA_MANAGER_D b ON TRIM(b.SHOP_ID)= TO_CHAR(a.SHOP_CURRENT_WID)
WHERE 1=1
AND b.shop_code = 'KDML'
AND (COMMENT1 NOT LIKE '%test%'	OR COMMENT1 IS NULL)
),
form AS 
(
	SELECT
		pol.SHOP_NAME shop_nm,
		'Số lượng Form' AS kpi,
		count(1) AS total,
		sum(CASE WHEN FORM_ASSET IN ('Xe máy', 'Đăng ký xe máy') THEN 1 ELSE 0 END ) dkxm,
		sum(CASE WHEN FORM_ASSET IN ('Ô tô', 'Đăng ký Ô tô') THEN 1 ELSE 0 END ) dkot
	FROM pol
	WHERE 1 = 1
	AND EXTRACT (YEAR FROM	FORM_CREATED_DT) = substr(:DATE_WID, 1, 4)
	AND EXTRACT (MONTH FROM	FORM_CREATED_DT) = substr(:DATE_WID, 5, 2)
	GROUP BY pol.SHOP_NAME
),
sale AS 
 	(
SELECT
	pol.SHOP_NAME shop_nm,
	COUNT(1) AS total,
	sum(CASE WHEN FORM_ASSET IN ('Xe máy', 'Đăng ký xe máy') THEN 1 ELSE 0 END ) dkxm,
	sum(CASE WHEN FORM_ASSET IN ('Ô tô', 'Đăng ký Ô tô') THEN 1 ELSE 0 END ) dkot
FROM pol
WHERE
	1 = 1
	AND EXTRACT (YEAR FROM	DISBURSE_DT) = substr(:DATE_WID, 1, 4)
	AND EXTRACT (MONTH	FROM DISBURSE_DT) = substr(:DATE_WID, 5, 2)
	AND pol.STATUS = 4
GROUP BY pol.SHOP_NAME),
F2S AS 
(
	SELECT 
		a.shop_nm,
		'F2S' kpi,
		CASE WHEN a.total = 0 THEN NULL ELSE (b.total/a.total)*100 END total,
		CASE WHEN a.dkxm = 0 THEN NULL ELSE (b.dkxm/a.dkxm)*100 END dkxm,
		CASE WHEN a.dkot = 0 THEN NULL ELSE (b.dkot/a.dkot)*100 END dkot
	FROM form a
	LEFT JOIN sale b ON a.shop_nm = b.shop_nm 
),
lead_ AS 
(
SELECT
	pol.SHOP_NAME shop_nm,
	'Số lượng Lead' kpi,
	COUNT(1) AS total,
	sum(CASE WHEN FORM_ASSET IN ('Xe máy', 'Đăng ký xe máy') THEN 1 ELSE 0 END ) dkxm,
	sum(CASE WHEN FORM_ASSET IN ('Ô tô', 'Đăng ký Ô tô') THEN 1 ELSE 0 END ) dkot
FROM pol
WHERE 1 = 1
AND EXTRACT (YEAR FROM	SHOP_REV_DT) = substr(:DATE_WID, 1, 4)
AND EXTRACT (MONTH FROM	SHOP_REV_DT) = substr(:DATE_WID, 5, 2)
GROUP BY SHOP_NAME
),
L2S AS 
(
	SELECT 
		a.shop_nm,
		'L2S' kpi,
		CASE WHEN a.total = 0 THEN NULL ELSE (b.total/a.total)*100 END total,
		CASE WHEN a.dkxm = 0 THEN NULL ELSE (b.dkxm/a.dkxm)*100 END dkxm,
		CASE WHEN a.dkot = 0 THEN NULL ELSE (b.dkot/a.dkot)*100 END dkot
	FROM lead_ a
	LEFT JOIN sale b ON a.shop_nm = b.shop_nm 
),
cs AS 
	(
	SELECT
		to_number(CIFCODE) CIFCODE,
		CUSTOMER_SCORE
	FROM
		(
		SELECT
			to_number(a.CIFCODE) CIFCODE,
			a.CUSTOMER_SCORE ,
			ROW_NUMBER() OVER(PARTITION BY CIFCODE ORDER BY SCORE_DATE DESC) rn
		FROM
			f88dwh.W_CUSTOMER_SCORE a
		WHERE
			CIFCODE IS NOT NULL
	)
	WHERE
		rn = 1
), 
LTV_MAX AS (
	SELECT
		/*+ ordered */
		s.SHOP_NM  ,
		asset.CATEGORY_CODE ,
		dtl.DISBURSE_AMT ,
		dtl.INSURANCE_AMT ,
		dtl.COLLATERAL_AMT ,
		CASE 
			WHEN asset.CATEGORY_CODE = 17 THEN 
			    CASE 
			    	WHEN nvl(dtl.INTERNAL_SCORE,cs.CUSTOMER_SCORE) > 600 THEN --'Tốt'
				    	CASE 
				    		WHEN REGEXP_LIKE(lower(pak.package_nm),'taixecongnghe|laodongtudo|tieuthuong') THEN 0.7
				    		WHEN REGEXP_LIKE(lower(pak.package_nm),'congnhan') THEN 0.8
				    		WHEN REGEXP_LIKE(lower(pak.package_nm),'vienchuc') THEN 0.9
				    		ELSE 0.7
				    	END 
				    ELSE -- trung bình(<= 500) + khá (<= 600)
				    	CASE 
				    		WHEN REGEXP_LIKE(lower(pak.package_nm),'taixecongnghe|laodongtudo|tieuthuong') THEN 0.5
				    		WHEN REGEXP_LIKE(lower(pak.package_nm),'congnhan') THEN 0.6
				    		WHEN REGEXP_LIKE(lower(pak.package_nm),'vienchuc') THEN 0.8
				    		ELSE 0.5
				    	END
				END
			ELSE 
				CASE 
					WHEN nvl(dtl.INTERNAL_SCORE,cs.CUSTOMER_SCORE) > 600 THEN
						CASE
							WHEN cus.CUSTOMER_TYPE = 1 AND dtl.FUND_NAME = 'F88 Fund' THEN 0.8--individual 
							WHEN cus.CUSTOMER_TYPE = 2 AND dtl.FUND_NAME = 'F88 Fund' THEN 0.4
							WHEN cus.CUSTOMER_TYPE = 1 AND dtl.FUND_NAME = 'CIMB Fund' THEN 0.8
						END 
					ELSE 
						CASE
							WHEN cus.CUSTOMER_TYPE = 1 AND dtl.FUND_NAME = 'F88 Fund' THEN 0.7
							WHEN cus.CUSTOMER_TYPE = 2 AND dtl.FUND_NAME = 'F88 Fund' THEN 0.4
							WHEN cus.CUSTOMER_TYPE = 1 AND dtl.FUND_NAME = 'CIMB Fund' THEN 0.7
						END
				END
		END LTV_MAX,
		CASE 
			WHEN dtl.TERM_FREQUENCY_UNIT = 'Days' THEN dtl.TERM_FREQUENCY / 30
			WHEN dtl.TERM_FREQUENCY_UNIT = 'Months' THEN dtl.TERM_FREQUENCY 
		END TERM_FREQUENCY 
	FROM
		f88dwh.W_LOAN_DTL_F dtl
	LEFT JOIN f88dwh.VW_W_CUSTOMER_D cus ON dtl.CUSTOMER_WID = cus.CUSTOMER_WID 
	LEFT JOIN f88dwh.W_SHOP_D s ON dtl.SHOP_WID = s.SHOP_WID 
	LEFT JOIN f88dwh.W_PACKAGE_D pak ON pak.PACKAGE_WID = dtl.PACKAGE_WID 
	LEFT JOIN f88dwh.W_ASSET_TP_D asset ON dtl.ASSET_TYPE_WID = asset.ASSET_TP_WID 
	LEFT JOIN cs on to_number(cus.CUSTOMER_CODE) = cs.CIFCODE
	WHERE 1=1
	AND dtl.DISBURSE_DATE_WID <= :DATE_WID
	AND dtl.DISBURSE_DATE_WID > to_char(trunc(to_date(:DATE_WID,'yyyymmdd'),'mm')-1,'yyyymmdd')
	AND dtl.COLLATERAL_AMT <> 0
--	to_char(trunc(to_date(:DATE_WID,'yyyymmdd'),'mm')-1,'yyyymmdd')
),
LTV AS 
(	
	SELECT 
		shop_nm,
		total - ltv_total total_max,
		dkot - ltv_total dkot_max,
		dkxm - ltv_total dkxm_max,
		ltv_total total,
		ltv_dkot dkot,
		ltv_dkxm dkxm,
		thv_dkot,
		thv_total,
		thv_dkxm
	FROM 
	(
		SELECT 
			shop_nm,
			CASE WHEN sum(total) = 0 THEN NULL ELSE avg(total) END total,
			CASE WHEN sum(dkot) = 0 THEN NULL ELSE avg(dkot) END dkot,
			CASE WHEN sum(dkxm) = 0 THEN NULL ELSE avg(dkxm) END dkxm,
			CASE WHEN count(ltv_total) = 0 THEN NULL ELSE avg(ltv_total) END ltv_total,
			CASE WHEN count(ltv_dkot) = 0 THEN NULL ELSE avg(ltv_dkot) END ltv_dkot,
			CASE WHEN count(ltv_dkxm) = 0 THEN NULL ELSE avg(ltv_dkxm) END ltv_dkxm	,
			CASE WHEN sum(thv_total) = 0 THEN NULL ELSE avg(thv_total) END thv_total,
			CASE WHEN sum(thv_dkot) = 0 THEN NULL ELSE avg(thv_dkot) END thv_dkot,
			CASE WHEN sum(thv_dkxm) = 0 THEN NULL ELSE avg(thv_dkxm) END thv_dkxm
		FROM
		(
		SELECT
			(DISBURSE_AMT-INSURANCE_AMT)/COLLATERAL_AMT ltv_total,
			CASE WHEN CATEGORY_CODE = 15 THEN (DISBURSE_AMT-INSURANCE_AMT)/COLLATERAL_AMT ELSE NULL END ltv_dkot,
			CASE WHEN CATEGORY_CODE = 17 THEN (DISBURSE_AMT-INSURANCE_AMT)/COLLATERAL_AMT ELSE NULL END ltv_dkxm,
			CASE WHEN CATEGORY_CODE = 15 THEN TERM_FREQUENCY ELSE NULL END thv_dkot,
			CASE WHEN CATEGORY_CODE = 17 THEN TERM_FREQUENCY ELSE NULL END thv_dkxm,
			TERM_FREQUENCY thv_total,
			ltv_max total,
			CASE WHEN CATEGORY_CODE = 15 THEN ltv_max ELSE NULL END dkot,
			CASE WHEN CATEGORY_CODE = 17 THEN ltv_max ELSE NULL END dkxm,
			shop_nm
		FROM ltv_max
		)
		GROUP BY shop_nm
	)a
),
BH AS 
(
	SELECT 
		shop_nm,
		category_code,
		round(count(DISTINCT ins_cus)/count(DISTINCT customer_code),4)*100 BH,
		count(DISTINCT ins_cus) ins_code,
		count(DISTINCT customer_code) cus_code
	FROM 
	(
		SELECT 
			b.shop_nm,
			d.category_code,
			a.LOAN_WID ,
			c.LOAN_WID ,
			cus.CUSTOMER_CODE ,
			CASE WHEN c.LOAN_WID IS NULL THEN NULL ELSE cus.CUSTOMER_CODE END ins_cus
		FROM W_LOAN_DTL_F a
		LEFT JOIN W_SHOP_D b ON a.SHOP_WID = b.SHOP_WID 
		LEFT JOIN (SELECT DISTINCT LOAN_WID FROM W_INS_CONTRACT_F) c ON a.LOAN_WID = c.LOAN_WID 
		LEFT JOIN W_ASSET_TP_D d ON a.ASSET_TYPE_WID = d.ASSET_TP_WID 
		LEFT JOIN VW_W_CUSTOMER_D cus ON a.CUSTOMER_WID = cus.CUSTOMER_WID 
		WHERE 1=1
		AND a.DISBURSE_DATE_WID >= substr(:DATE_WID,1,6)||'01'
		AND a.DISBURSE_DATE_WID <= :DATE_WID 
		AND a.channel_code='KDML'
	)
	GROUP BY category_code , shop_nm
)
SELECT  
distinct
	A.SHOP_NM,
	A.KPI_CODE "Mã chỉ tiêu", 
	A.KPI "Chỉ số vận hành cấp 1",
	A.ASSET "Loại tài sản",
    'Đang cập nhật' "Mục tiêu",
	CASE 
		WHEN A."KPI" = 'F2S' THEN round(A.VALUE_,2)
		WHEN A."KPI" = 'L2S' THEN round(A.VALUE_,2)
		WHEN A."KPI" = 'GAP TRẦN LTV' THEN round(A.VALUE_,2) * 100
		WHEN A."KPI" = 'Số lượng Form' THEN round(A.VALUE_,0)
		WHEN A."KPI" = 'Số lượng Lead' THEN round(A.VALUE_,0)
		WHEN A."KPI" = 'LTV' THEN round(A.VALUE_,4) * 100
		WHEN A."KPI" = 'Thời gian vay TB theo HĐ' THEN round(A.VALUE_,1)
		WHEN A."KPI" = '%KH tham gia BH' THEN round(A.VALUE_,2)
	END "Thực hiện",
	CASE 
		WHEN A."KPI" = 'F2S' THEN '%'
		WHEN A."KPI" = 'L2S' THEN '%'
		WHEN A."KPI" = 'GAP TRẦN LTV' THEN '%'
		WHEN A."KPI" = 'Số lượng Form' THEN 'Form'
		WHEN A."KPI" = 'Số lượng Lead' THEN 'Lead'
		WHEN A."KPI" = 'LTV' THEN '%'
		WHEN A."KPI" = 'Thời gian vay TB theo HĐ' THEN 'Tháng'
		WHEN A."KPI" = '%KH tham gia BH' THEN '%'
    END "Đơn vị",
    'Đang cập nhật' "% Hoàn thành" 
FROM 
(
SELECT shop_nm,'4.1' kpi_code, kpi,'TỔNG' asset, Total value_  FROM form
UNION ALL 
SELECT shop_nm,'4.1.1' kpi_code, kpi,'ĐKXM' asset, dkxm value_  FROM form
UNION ALL 
SELECT shop_nm,'4.1.2' kpi_code, kpi,'ĐKOT' asset, dkot value_  FROM form
UNION ALL 
SELECT shop_nm,'4.2' kpi_code, kpi,'TỔNG' asset, Total value_  FROM f2s
UNION ALL 
SELECT shop_nm,'4.2.1' kpi_code, kpi,'ĐKXM' asset, dkxm value_  FROM f2s
UNION ALL 
SELECT shop_nm,'4.2.2' kpi_code, kpi,'ĐKOT' asset, dkot value_  FROM f2s
UNION ALL 
SELECT shop_nm,'4.3' kpi_code, kpi,'TỔNG' asset, Total value_  FROM lead_
UNION ALL 
SELECT shop_nm,'4.3.1' kpi_code, kpi,'ĐKXM' asset, dkxm value_  FROM lead_
UNION ALL 
SELECT shop_nm,'4.3.2' kpi_code, kpi,'ĐKOT' asset, dkot value_  FROM lead_
UNION ALL 
SELECT shop_nm,'4.4' kpi_code, kpi,'TỔNG' asset, Total value_  FROM l2s
UNION ALL 
SELECT shop_nm,'4.4.1' kpi_code, kpi,'ĐKXM' asset, dkxm value_  FROM l2s
UNION ALL 
SELECT shop_nm,'4.4.2' kpi_code, kpi,'ĐKOT' asset, dkot value_  FROM l2s	
UNION ALL 
SELECT shop_nm,'4.5' kpi_code, 'LTV' kpi,'TỔNG' asset, Total value_  FROM ltv
UNION ALL 
SELECT shop_nm,'4.5.1' kpi_code, 'LTV' kpi,'ĐKXM' asset, dkxm value_  FROM ltv
UNION ALL 
SELECT shop_nm,'4.5.2' kpi_code, 'LTV','ĐKOT' asset, dkot value_  FROM ltv
--UNION ALL 
--SELECT shop_nm,'4.6' kpi_code, 'GAP TRẦN LTV' kpi,'TỔNG' asset, total_max value_  FROM ltv
--UNION ALL 
--SELECT shop_nm,'4.6.1' kpi_code, 'GAP TRẦN LTV' kpi,'ĐKXM' asset, dkxm_max value_  FROM ltv
--UNION ALL 
--SELECT shop_nm,'4.6.2' kpi_code,'GAP TRẦN LTV' kpi,'ĐKOT' asset, dkot_max value_  FROM ltv
UNION ALL 
SELECT shop_nm,'4.7' kpi_code, 'Thời gian vay TB theo HĐ' kpi,'TỔNG' asset, thv_total value_  FROM ltv
UNION ALL 
SELECT shop_nm,'4.7.1' kpi_code, 'Thời gian vay TB theo HĐ' kpi,'ĐKXM' asset, thv_dkxm value_  FROM ltv
UNION ALL 
SELECT shop_nm,'4.7.2' kpi_code,'Thời gian vay TB theo HĐ' kpi,'ĐKOT' asset, thv_dkot value_  FROM ltv
UNION ALL 
SELECT shop_nm,'4.8' kpi_code, '%KH tham gia BH' kpi,'TỔNG' asset, (sum(ins_code)/sum(cus_code))*100 value_  FROM BH GROUP BY SHOP_NM
UNION ALL 
SELECT shop_nm,'4.8.1' kpi_code, '%KH tham gia BH' kpi,'ĐKXM' asset, BH value_  FROM BH WHERE category_code = '17'
UNION ALL 
SELECT shop_nm,'4.8.2' kpi_code,'%KH tham gia BH' kpi,'ĐKOT' asset, BH value_  FROM BH WHERE category_code = '15'
) a
ORDER BY "Mã chỉ tiêu"

--split 
-- REP_QUERY
WITH 
EMP_FULL AS 
(
SELECT DISTINCT EMPLOYEE_CODE , EMPLOYEE_NM , EMAIL  FROM F88DWH.W_EMPLOYEE_D WHERE CRN_ROW_IND = 1 AND "SOURCE" = 'IAM' 
),
emp as
(
SELECT 
	A.MONTH_ID,
	A.SHOP_CODE,
	S.SHOP_NM ,
	E1.EMPLOYEE_CODE CVKD_CODE,
	E1.EMPLOYEE_NM CVKD_NM,
	E1.EMAIL CVKD_EMAIL,
	E2.EMPLOYEE_CODE TPGD_CODE,
	E2.EMPLOYEE_NM TPGD_NM,
	E2.EMAIL TPGD_EMAIL
--	E3.EMPLOYEE_CODE QLKV_CODE,
--	E3.EMPLOYEE_NM QLKV_NM,
--	E3.EMAI
--	E4.EMPLOYEE_CODE QLV_CODE,
--	E4.EMPLOYEE_NM QLV_NM
--*
FROM BACHPX.INCENTIVE_NHAN_SU a 
LEFT JOIN F88DWH.W_SHOP_D S ON TRIM(A.SHOP_CODE) = TRIM(S.SHOP_CODE)
LEFT JOIN BACHPX.INCENTIVE_ORG_CHART b ON a.shop_code = b.shop_code 
LEFT JOIN EMP_FULL E1 ON A.EMPLOYEE_CODE = E1.EMPLOYEE_CODE 
LEFT JOIN EMP_FULL E2 ON B.CODE_TPGD = E2.EMPLOYEE_CODE 
LEFT JOIN EMP_FULL E3 ON B.CODE_QLKV = E3.EMPLOYEE_CODE 
LEFT JOIN EMP_FULL E4 ON B.CODE_QLV = E4.EMPLOYEE_CODE 
WHERE a.month_id = 202306
AND b.month_id = 202306
AND S.SHOP_TYPE = 'F88'
)
SELECT 
	SHOP_NM,
	LISTAGG(CVKD_EMAIL , ';') WITHIN GROUP (ORDER BY CVKD_EMAIL)||';'||TPGD_EMAIL  "EMAIL"
FROM EMP
--WHERE SHOP_NM = 'TTH00001.1030 Nguyễn Tất Thành'
GROUP BY SHOP_NM,TPGD_EMAIL