SELECT -- DAILY
	t.loan_id DISBURSE_ID,
	t2.name AS SHOPNAME,
	t.amount AS DISBURSE_AMOUNT,
	DATE(created_date) DATE,
	WEEK(created_date) WEEK,
	MONTH(created_date) MONTH,
	YEAR(created_date) YEAR,
	CASE
		WHEN action_name LIKE '%%<PERSON><PERSON><PERSON><PERSON> ngân%%' THEN 'Giải ngân'
		WHEN action_name LIKE 'Đáo hạn' THEN 'Đáo hạn'
		WHEN action_name LIKE 'Vay thêm' THEN 'Vay thêm'
	END DISBURSE_ACTION
FROM
	m_loan_transaction t
LEFT JOIN m_office t2 ON
	t.office_id = t2.id
WHERE
	(action_name LIKE '%%<PERSON><PERSON><PERSON><PERSON> ngân%%'
		OR action_name LIKE 'Đáo hạn'
		OR action_code LIKE 'Vay thêm')
	AND DATE(created_date) = %(DATE_WID)s
