import datetime
import logging
import os
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from DAGs.utils import set_kwargs
import pandas as pd
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import date
from datetime import timedelta
from airflow.providers.oracle.hooks.oracle import OracleHook


parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'QLV_DAILY_PGD_MAIL'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 9 * * *'
description = 'MAIL PGD'
tags = ['vas', 'daily']
department = 'Project - QLV'
file_name = 'PGD_MAIL'
report_name = 'PGD_MAIL'
overwrite = True
freq = 'monthly'
sql_temp, saved_folder = set_kwargs(parent_dir, file_name)
_type = 'Data Warehouse to SharePoint - Report F88'
sql = dict()

sql_temp = sql_temp.split('--split')
time = (date.today() - timedelta(days = 1)).strftime('%Y%m%d')
for s in sql_temp:
    if 'KPI_QUERY' in s:
        q_kpi = s.replace(':DATE_WID',time)
    elif 'KD_QUERY' in s:
        q_key_driver = s.replace(':DATE_WID',time)
    elif 'VH_QUERY' in s:
        q_vh = s.replace(':DATE_WID',time)
    elif 'REP_QUERY' in s:
        q_recipients = s.replace(':DATE_WID',time)



# change these as per use
your_email = "<EMAIL>"
your_password = "F88@6388"

# query.py


# Establish the database connection
# dwh = cx_Oracle.connect(user="sontb2", password='Jdz7kmR042TsBt5z0H02',
#                                dsn="************:1521/orcldwh1")

hook = OracleHook(oracle_conn_id='oracle_f88_dwh')

def get_data(q):
    pd.options.display.float_format = '{:.2f}'.format
    # pd.set_option('display.float_format', lambda x: '%.3f' % x)
    df = hook.get_pandas_df(q)
    # df = pd.read_sql_query(q, con = hook)
    return df


def insert_txt(content,substr,inserttxt):
    content = content
    substr = substr
    inserttxt = inserttxt
    idx = content.index(substr)
    content = content[:idx] + inserttxt + content[idx:]
    return content

def send_email():
    kd_data = get_data(q_key_driver)
    kpi_data = get_data(q_kpi)
    vh_data = get_data(q_vh)
    rep_data = get_data(q_recipients)
    rep_data = rep_data[rep_data['SHOP_NM'].isin(kpi_data['SHOP_NM'].unique())]
    rep = []
    rep_mail = rep_data['EMAIL'].to_list()
    shop_nm = rep_data['SHOP_NM'].to_list()
    for i in range(len(shop_nm)):
        rep.append((shop_nm[i],rep_mail[i]))
    # rep.append(('HGI22005.Thị Trấn Kinh Cùng','<EMAIL>'))
    rep = set(rep)
    for i in rep:
        # print(i[0], i[1])
        # print(email_list[email_list['EMAIL_TEST'] == i[1]])
        html_content = '''
        <html>
        <head></head>
        <body>
            <p>Dear anh/chị,</p>
            <p></p>
            <p>TT PTCL gửi anh/chị số liệu KPI, Key drivers của anh/chị tính đến hết ngày {date}</p>
            <p></p>
            <p>Tên PGD: {shop_nm} </p>
            <br>
            {table_kpi}
            <br>
            <br>
            {table_kd}
            <br>
            <br>
            {table_vh}
            <p></p>
            <p>Trân trọng!</p>
        </body>
        </html>
        '''
          # width: 800px;
        msg = MIMEMultipart()
        msg['From'] = your_email
        msg['To'] = i[1]
        # msg['To'] = '<EMAIL>'
        dt = (date.today() - timedelta(days = 1)).strftime('%d/%m/%Y')
        msg['Subject'] = 'Chỉ số KPI, Key drivers và chỉ số vận hành PGD ' + i[0] + ' - Ngày ' + dt
        vh_data = vh_data.fillna("")
        kpi_data = kpi_data.fillna("")
        kd_data = kd_data.fillna("")
        table_kpi = kpi_data[kpi_data['SHOP_NM'] == i[0]][["KPI","Đơn vị","Mục tiêu","Thực hiện",'Dự báo hoàn thành tháng', 'Trạng thái']].to_html(index=False)
        table_kd = kd_data[kd_data['SHOP_NM'] == i[0]][["Key drivers","Loại tài sản","Đơn vị","Mục tiêu","Thực hiện",'Dự báo hoàn thành tháng', 'Trạng thái']].drop_duplicates().to_html(index=False)
        table_vh = vh_data[vh_data['SHOP_NM'] == i[0]][["Chỉ số vận hành cấp 1","Loại tài sản","Đơn vị","Thực hiện"]].drop_duplicates().to_html(index=False)
        # print(table)
        content=html_content.format(date = dt,shop_nm = i[0], table_kpi = table_kpi, table_kd = table_kd, table_vh = table_vh)
        custom_table = """\n<style>
table.customTable {

  background-color: #FFFFFF;
  border-collapse: collapse;
  border-width: 1px;
  border-color: #000000;
  border-style: solid;
  color: #000000;
}

table.customTable td, table.customTable th {
  border-width: 1px;
  border-color: #000000;
  border-style: solid;
  padding: 4px;
}

table.customTable thead {
  background-color: #F8CA14;
}
</style>\n"""
        content = insert_txt(content,"</head>",custom_table)
        content = content.replace('''class="dataframe"''','''class="customTable"''')
        content = content.replace('''<th>KPI</th>''','''<th style="width:300px">KPI</th>''')
        content = content.replace('''<th>Key drivers</th>''','''<th style="width:200px">Key drivers</th>''')
        content = content.replace('''<th>Chỉ số vận hành cấp 1</th>''','''<th style="width:200px">Chỉ số VH cấp 1</th>''')
        content = content.replace('''<th>Loại tài sản</th>''','''<th style="width:100px">Loại tài sản</th>''')
        content = content.replace('''<th>Đơn vị</th>''','''<th style="width:100px">Đơn vị</th>''')
        content = content.replace('''<th>Mục tiêu</th>''','''<th style="width:100px">Mục tiêu</th>''')
        content = content.replace('''<th>Thực hiện</th>''','''<th style="width:100px">Thực hiện</th>''')
        content = content.replace('''<th>Dự báo hoàn thành tháng</th>''','''<th style="width:100px">Dự báo</th>''')
        content = content.replace('''<th>Trạng thái</th>''','''<th style="width:100px">Trạng thái</th>''')
        html_part = MIMEText(content, 'html')
        msg.attach(html_part)
#     # establishing connection with gmail
        server = smtplib.SMTP_SSL('smtp.f88.co', 587)
        server.ehlo()
        server.login(your_email, your_password)


        # sending the email
        server.send_message(msg)
        # time.sleep(60)
        # close the smtp server
        server.close()
        logging.info('Done sending email to PGD: '+ i[0])

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE'
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    send_email = PythonOperator(task_id = 'Send_Email',
                                python_callable = send_email
                                )

    end = EmptyOperator(task_id='End')

    start >> send_email >> end