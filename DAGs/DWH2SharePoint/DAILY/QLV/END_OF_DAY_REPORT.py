import datetime
import logging
import os

import pandas as pd
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from airflow.models.connection import Connection
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from office365.runtime.auth.user_credential import UserCredential
from office365.sharepoint.client_context import ClientContext
from UliPlot.XLSX import auto_adjust_xlsx_column_width

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'KDML_DAILY_END_OF_DAY_REPORT_QLV'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 17,21 * * *'
description = 'Báo cáo cuối ngày cho quản lý vùng daily - TuyenDN'
tags = ['qlv', 'daily']
department = 'Project - KDML'
report_name = '<PERSON>uản lý vùng'
overwrite = False
freq = 'daily'
_type = 'Data Warehouse to SharePoint - Report F88'
oracle_conn_id = 'oracle_f88_dwh'
mifos_conn_id = 'mysql_mifos'
ins_conn_id = 'mysql_ins'
sharepoint_conn_id = 'sharepoint_f88_data'
old_data_folder = os.path.join(parent_dir, 'Downloaded', 'Old_Report')
new_data_folder = os.path.join(parent_dir, 'Downloaded', 'New_Report')
if not os.path.exists(old_data_folder):
    os.makedirs(old_data_folder, exist_ok=True)
if not os.path.exists(new_data_folder):
    os.makedirs(new_data_folder, exist_ok=True)
qlv_data_folder = os.path.join(parent_dir, 'Downloaded', 'QLV.xlsx')
new_data_path = os.path.join(parent_dir, 'Downloaded', 'new_data')
if not os.path.exists(new_data_path):
    os.makedirs(new_data_path, exist_ok=True)
target_list = {'LOAN_NEW_CONTRACT': ['NEW_CONTRACT_ID', 'DATE', 'WEEK', 'MONTH', 'YEAR', 'principal_amount',
                                     'SHOPNAME'],
               'LOAN_DISBURSE': ['DISBURSE_ID', 'SHOPNAME', 'DISBURSE_AMOUNT', 'DATE', 'WEEK', 'MONTH', 'YEAR',
                                 'DISBURSE_ACTION'],
               'LOAN_WITHDRAW': ['WITHDRAW_ID', 'SHOPNAME', 'WITHDRAW_AMOUNT', 'DATE', 'WEEK', 'MONTH', 'YEAR',
                                 'WITHDRAW_ACTION'],
               'LOAN_CLOSED': ['CLOSED_ID', 'DATE', 'WEEK', 'MONTH', 'YEAR', 'SHOPNAME'],
               'ROLL': ['ROLLID', 'ROLL_AMOUNT', 'OVERDUE_DAYS', 'SHOPNAME', 'DATE', 'WEEK', 'MONTH', 'YEAR'],
               'INS': ['INSID', 'SHOPNAME', 'DATE', 'WEEK', 'MONTH', 'YEAR', 'INS_AMOUNT'],
               'ROLL_BACK': ['ROLL_BACK_LOAN_CODE', 'ROLL_BACK_AMOUNT', 'SHOPNAME', 'DATE', 'WEEK', 'MONTH', 'YEAR']}


def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date
    DATE_WID = time.strftime("%Y-%m-%d")
    params = {}
    params.update({'DATE_WID': DATE_WID})
    kwargs['ti'].xcom_push(key=dag_name, value=params)
    logging.info('Set Parameters Done!')


def get_qlv(ds, **kwargs):
    query = """SELECT
                SHOP_NAME SHOPNAME,
                TPK AS QLV
            FROM F88DWH.W_AREA_MANAGER_D
            WHERE FLAG = 'Active'
        """
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    qlv_df = hook.get_pandas_df(sql=query)
    headers = ['SHOPNAME', 'QLV']
    qlv_df.to_excel(qlv_data_folder, index=False, header=headers)


def download_old_report(ds, **kwargs):
    conn = Connection.get_connection_from_secrets(sharepoint_conn_id)
    logging.info("Using connection ID '%s' for task execution.", conn.conn_id)
    host = conn.host
    user = conn.login
    password = conn.get_password()
    download_path = conn.extra_dejson.get('download_path')
    session = ClientContext(host).with_credentials(UserCredential(user, password))
    logging.info('Connect to SharePoint Sucessfully')
    files = list()

    def enum_folder(parent_folder, fn):
        parent_folder.expand(["Files", "Folders"]).get().execute_query()
        for file in parent_folder.files:
            fn(file)
        for folder in parent_folder.folders:
            enum_folder(folder, fn)

    def append_files(f):
        files.append(f)

    root_folder = session.web.get_folder_by_server_relative_path(download_path + '/' + _type + '/' + department)
    enum_folder(root_folder, append_files)
    for file_url in files:
        logging.info("Downloading file: {0} ...".format(file_url.properties["ServerRelativeUrl"]))
        download_file_name = os.path.join(old_data_folder, os.path.basename(file_url.properties["Name"]))
        file = file_url.properties["ServerRelativeUrl"]
        with open(download_file_name, "wb") as local_file:
            a = session.web.get_file_by_server_relative_path(file).download(local_file).execute_query()
        logging.info("[Ok] file has been downloaded: {0}".format(download_file_name))


def set_and_execute(ds, **kwargs):
    target = kwargs['target']
    params = kwargs['ti'].xcom_pull(key=dag_name)
    f = open(os.path.join(parent_dir, 'queries', f'{target}.sql'), "r", encoding='utf-8')
    query = f.read()
    if target == 'ROLL_BACK':
        sql = query.split('-- Query Split --')
        sql_dwh, sql_mifos = sql[0], sql[1]
        dwh_time = datetime.datetime.strptime(params['DATE_WID'], "%Y-%m-%d")
        dwh_parms = {
            'DATE_WID': (dwh_time - datetime.timedelta(1)).strftime("%Y%m%d"),
            'MONTH_NUM': dwh_time.strftime("%m")
        }
        hook_mifos = MySqlHook(mysql_conn_id=mifos_conn_id)
        hook_dwh = OracleHook(oracle_conn_id=oracle_conn_id)
        roll_back_dwh = hook_dwh.get_pandas_df(sql=sql_dwh, parameters=dwh_parms)
        roll_back_mifos = hook_mifos.get_pandas_df(sql=sql_mifos, parameters=params)
        df = roll_back_mifos.merge(roll_back_dwh, on=['ROLL_BACK_LOAN_CODE', 'SHOPNAME'], how='inner')
        df.columns = target_list[target]
    else:
        if target == 'INS':
            hook = MySqlHook(mysql_conn_id=ins_conn_id)
        else:
            hook = MySqlHook(mysql_conn_id=mifos_conn_id)
        df = hook.get_pandas_df(sql=query, parameters=params)
        df.columns = target_list[target]
    logging.info(f'Query data {target} from DWH successfully!')
    logging.info(f'DF {target} has {df.shape[0]} records')
    qlv = pd.read_excel(qlv_data_folder, header=0)
    df_merged = df.merge(qlv, how='inner', on='SHOPNAME')
    if target == 'LOAN_NEW_CONTRACT':
        df = df_merged.groupby(['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR']).NEW_CONTRACT_ID.count().reset_index()
        df.columns = ['QLV_NAME', 'DATE_', 'WEEK', 'MONTH_', 'YEAR_', 'LOAN_NEW_CONTRACT']
    elif target == 'LOAN_DISBURSE':
        df = df_merged.groupby(['QLV', 'DISBURSE_ACTION', 'DATE', 'WEEK', 'MONTH', 'YEAR']).DISBURSE_AMOUNT \
            .sum().reset_index()
        df.columns = ['QLV_NAME', 'LOAN_DISBURSE_ACTION',
                      'DATE_', 'WEEK', 'MONTH_', 'YEAR_', 'LOAN_DISBURSE_AMOUNT']
    elif target == 'LOAN_WITHDRAW':
        df = df_merged.groupby(['QLV', 'WITHDRAW_ACTION', 'DATE', 'WEEK', 'MONTH', 'YEAR']).WITHDRAW_AMOUNT \
            .sum().reset_index()
        df.columns = ['QLV_NAME', 'LOAN_WITHDRAW_ACTION', 'DATE_', 'WEEK', 'MONTH_', 'YEAR_',
                      'LOAN_WITHDRAW_AMOUNT']
    elif target == 'LOAN_CLOSED':
        df = df_merged.groupby(['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR']).CLOSED_ID.count().reset_index()
        df.columns = ['QLV_NAME', 'DATE_', 'WEEK', 'MONTH_', 'YEAR_', 'LOAN_CLOSED']
    elif target == 'INS':
        df_count = df_merged.groupby(['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR']).INSID.count().reset_index()
        df_sum = df_merged.groupby(['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR']).INS_AMOUNT.sum().reset_index()
        df = df_count.merge(df_sum, on=['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR'], how='inner')
        df.columns = ['QLV_NAME', 'DATE_', 'WEEK', 'MONTH_', 'YEAR_', 'INS_NEW_CONTRACT', 'INS_AMOUNT']
    elif target == 'ROLL':
        df_count = df_merged.groupby(
            ['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR', 'OVERDUE_DAYS']).ROLLID.count().reset_index()
        df_sum = df_merged.groupby(
            ['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR', 'OVERDUE_DAYS']).ROLL_AMOUNT.sum().reset_index()
        df = df_count.merge(df_sum, on=['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR', 'OVERDUE_DAYS'], how='inner')
        df.columns = ['QLV_NAME', 'DATE_', 'WEEK', 'MONTH_', 'YEAR_', 'OVERDUE_DAYS', 'ROLL_COUNT', 'ROLL_AMOUNT']
    elif target == 'ROLL_BACK':
        df_roll_back_count = df_merged.groupby(
            ['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR']).ROLL_BACK_LOAN_CODE.count().reset_index()
        df_roll_back_sum = df_merged.groupby(
            ['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR']).ROLL_BACK_AMOUNT.sum().reset_index()
        df = df_roll_back_count.merge(df_roll_back_sum, on=['QLV', 'DATE', 'WEEK', 'MONTH', 'YEAR'], how='inner')
        df.columns = ['QLV_NAME', 'DATE_', 'WEEK', 'MONTH_', 'YEAR_', 'ROLL_BACK_COUNT', 'ROLL_BACK_AMOUNT']
    writer = pd.ExcelWriter(os.path.join(new_data_path, target + '.xlsx'), engine='openpyxl', mode='w')
    df.to_excel(writer, index=False, sheet_name=target)
    auto_adjust_xlsx_column_width(df, writer, sheet_name=target, margin=0, index=False)
    writer.save()


def merge(ds, **kwargs):
    time = kwargs['dag_run'].logical_date.strftime('%Y-%m-%d')
    new_data_files = next(os.walk(new_data_path))[2]
    new_report = {}
    for new in new_data_files:
        new_report[new.split('.')[0]] = pd.read_excel(os.path.join(new_data_path, new), header=0)
    qlv = pd.read_excel(qlv_data_folder, header=0)
    for qlv in qlv.QLV.unique():
        df_old = dict()
        if os.path.exists(os.path.join(old_data_folder, f'{qlv}.xlsx')):
            for target in new_report:
                try:
                    df = pd.read_excel(os.path.join(old_data_folder, f'{qlv}.xlsx'), sheet_name=target,
                                       index_col=None, header=0)
                    dropIndex = df[df.DATE_ == time].index
                    df.drop(dropIndex, inplace=True)
                    df_old[target] = df
                except:
                    df_old[target] = pd.DataFrame()
        writer = pd.ExcelWriter(os.path.join(new_data_folder, f'{qlv}.xlsx'), engine='xlsxwriter')
        for target in new_report:
            df_new = new_report[target]
            df_new_of_qlv = df_new[df_new.QLV_NAME == qlv]
            if isinstance(df_old.get(target, None), pd.DataFrame):
                df_of_qlv = pd.concat([df_new_of_qlv, df_old.get(target, None)])
            else:
                df_of_qlv = df_new_of_qlv
            df_of_qlv = df_of_qlv.sort_values(by='DATE_', ascending=False)
            df_of_qlv.to_excel(writer, sheet_name=target, index=False)
            auto_adjust_xlsx_column_width(df_of_qlv, writer, sheet_name=target, margin=0, index=False)
        writer.save()


def upload_to_sharepoint(ds, **kwargs):
    conn = Connection.get_connection_from_secrets(sharepoint_conn_id)
    logging.info("Using connection ID '%s' for task execution.", conn.conn_id)
    host = conn.host
    user = conn.login
    password = conn.get_password()
    upload_path = conn.extra_dejson.get('upload_path')
    session = ClientContext(host).with_credentials(UserCredential(user, password))
    logging.info('Connect to SharePoint Sucessfully')
    target_folder_url = dict()
    target_folder = dict()
    folders = next(os.walk(new_data_folder))[2]
    for file_name in folders:
        f = file_name.split('.')[0]
        target_folder_url[f] = f'{upload_path}/{_type}/{department}/{report_name}/{f}'
        target_folder[f] = session.web.ensure_folder_path(target_folder_url[f]) \
            .execute_query()
        logging.info(f'Create folder {target_folder_url} on SharePoint Successfully!')
        local_path = os.path.join(new_data_folder, file_name)
        size_chunk = 1000000
        uploaded_file = target_folder[f].files.create_upload_session(local_path, size_chunk).execute_query()
        logging.info(f'Upload {file_name} to Share Point to folder {target_folder_url[f]} Successfully!')


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    set_params = PythonOperator(task_id='Set_Parameters_for_SQL', python_callable=set_params)

    get_qlv_df = PythonOperator(task_id='Get_QLV_Data_From_DWH', python_callable=get_qlv)

    download_old_files = PythonOperator(task_id='Download_Old_Report', python_callable=download_old_report)

    with TaskGroup('get_target_data_task_group',
                   prefix_group_id=False,
                   ) as get_target_data_task_group:
        for target in target_list:
            PythonOperator(task_id=f'process_target_{target}',
                           provide_context=True,
                           python_callable=set_and_execute,
                           op_kwargs={'target': target})

    merge = PythonOperator(task_id='Merger_Old_and_New_DF', python_callable=merge)

    upload = PythonOperator(task_id='Upload_to_SharePoint', python_callable=upload_to_sharepoint)

    end = EmptyOperator(task_id='End')

    start >> set_params >> [get_qlv_df, download_old_files] >> get_target_data_task_group >> merge >> upload >> end
