import datetime
import logging
import os
import pandas as pd
from UliPlot.XLSX import auto_adjust_xlsx_column_width
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.microsoft.mssql.hooks.mssql import MsSqlHook
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs
from pyexcelerate import Workbook
from Provider.utils import *
from airflow.providers.oracle.hooks.oracle import OracleHook
from dateutil.relativedelta import relativedelta
parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'VH_DAILY_BCDS'
start_date = datetime.datetime(2022, 9, 1)
# schedule_interval = '*/30 * * * *'
schedule_interval = None
description = '<PERSON><PERSON>o c<PERSON><PERSON> đối soát giao dịch đảm bảo- ManhLV'
tags = ['vh', 'daily']
department = 'Project - Vận hành'
file_name = 'BCDS'
report_name = '<PERSON><PERSON>o c<PERSON>o đối soát giao dịch đảm bảo'
overwrite = False
freq = 'daily'
_type = 'Data Warehouse to SharePoint - Report F88'
sql_temp, saved_folder = set_kwargs(parent_dir, file_name)
if not os.path.exists(os.path.join(saved_folder, report_name)):
    os.makedirs(os.path.join(saved_folder, report_name))
pos_conn_id = 'mssql_pos'
ora_conn_id = 'oracle_f88_dwh'
sql_ora = """select a.CodeNo,
	a.PawnStatusName ,
  case when a.CreatedDate is not null then to_char(a.CreatedDate, 'dd/MM/yyyy') end Ngay_tao_HD,
	case when b.DISBURSE_DATE_WID is not null then to_char(to_date(b.DISBURSE_DATE_WID ,'yyyymmdd'), 'dd/mm/yyyy')  end Ngay_GN  ,
	c.SHOP_NM ,
	d.RECEIPTCODE ,
 case when d.CREATEDDATE is not null then to_char(d.CREATEDDATE , 'dd/mm/yyyy') end Ngay_tao_lenh_thu ,
 	g.GRP_NM Loai_GD,
	a.PAWNASSETNAME Ten_TS ,
	watd.ASSET_TP_NM TS_DK	,
	b.CTR_TYPE Loai_HD,
	b.FUND_NAME KENH_GN
from F88DWH.W_LOAN_MASTER_F a
left join F88DWH.W_LOAN_DTL_F b 
on a.codeno=b.LOAN_CODE
LEFT JOIN F88DWH.W_ASSET_TP_D watd 
ON b.ASSET_TYPE_WID =watd.ASSET_TP_WID 
LEFT JOIN F88DWH.W_SHOP_D c 
ON b.SHOP_WID =c.SHOP_WID 
LEFT JOIN F88DWH.W_REVENUEEXPENDITURE_F d 
ON a.CODENO =d.CODENO 
LEFT JOIN F88DWH.GRP g 
ON d.TYPETRANSACTIONID =g.GRP_ID 
WHERE a.CreatedDate >= to_date(':date1','yyyy-mm-dd') and a.CreatedDate < to_date(':date2','yyyy-mm-dd')
 AND ( g.GRP_CODE  ='184' OR g.GRP_CODE IS NULL)
and a.PawnAssetName like '%Ô tô%'
AND a.PAWNSTATUSNAME IS NOT null"""
sql_pos = """select 	b.CodeNo CODENO,
a.RegisterName Tai_khoan_dang_ky,
	format(a.ModifiedDate ,'dd/MM/yyyy') Ngay_cap_nhat,
	a.TransationNumber So_don_dang_ky ,
	u.UserName user_cap_nhat,
	b.Extend1 Bien_so_xe ,
	b.Extend2 So_khung,
	b.Extend3 So_may,
	case when a.Status =1 then 'chưa đăng ký'
	     when a.Status =2 then 'đã đăng ký'             
	     when a.Status =3 then 'điều chỉnh'           
	     when a.Status =-1 then 'xóa' end Trang_thai_GDDB
from estore.AssetSecuredTransactions a 
left join  estore.Asset b on a.AssetCode = b.WarehouseAssetCode 
left join estore.[User] u on a.ModifiedBy=u.UserCode
where b.CreateDate >= ':date1' and b.CreateDate < ':date2'"""

def get_date(date_run):
    if(date_run.day == 1):
        date1= (date_run- relativedelta(months=1)).strftime('%Y-%m-%d')
        date2= date_run.strftime('%Y-%m-%d')
    else:
        date1= (date_run.replace(day=1)).strftime('%Y-%m-%d')
        date2= date_run.strftime('%Y-%m-%d')
        print(date1)
    sql1 = sql_pos.replace(':date2',date2)
    sql1 = sql1.replace(':date1', date1)
    sql2 = sql_ora.replace(':date2',date2)
    sql2 = sql2.replace(':date1', date1)
    return sql1, sql2
def extract_data(ds, **kwargs):
    sql_pos, sql_ora = get_date(kwargs['dag_run'].logical_date)
    pos_hook = MsSqlHook(mssql_conn_id=pos_conn_id)
    pos_df = pos_hook.get_pandas_df(sql=sql_pos)
    ora_hook = OracleHook(oracle_conn_id=ora_conn_id)
    df_ora = ora_hook.get_pandas_df(sql=sql_ora)
    data_df = pos_df.merge(df_ora, on='CODENO', how='left')
    column = [('MA_HD','Tai_khoan_dang_ky','Ngay_cap_nhat','So_don_dang_ky','user_cap_nhat','Bien_so_xe','So_khung','So_may','Trang_thai_GDDB' ,'Trang_thai_HD', 'Ngay_tao_HD','Ngay_GN','Ten_PGD','So_phieu_thu','Ngay_tao_lenh_thu','Loai_GD','Ten_TS','TS_DK','LOAI_HD','KENH_GN')]
    data = column + list(data_df.itertuples(index=False))
    wb = Workbook()
    wb.new_sheet("sheet name", data=data)
    execution_date = (kwargs['dag_run'].logical_date - datetime.timedelta(1)).strftime('%Y%m%d')
    year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
    saved_folder_tmp, file_name_tmp = set_saved_folder_and_file_name(overwrite, freq,
                                                             saved_folder,
                                                             file_name, report_name, year,
                                                             month, day)
    wb.save(os.path.join(saved_folder_tmp, file_name_tmp))

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    extract = PythonOperator(task_id='Extract_Data_from_LOS_and_MIFOS', python_callable=extract_data)

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> extract >> load >> end
