import datetime
import logging
import os
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import Python<PERSON>perator
from airflow.providers.oracle.hooks.oracle import OracleH<PERSON>
from Provider.OracleProvider.operators.GetDataOperators import GetDataOracleOperator
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from DAGs.utils import set_kwargs

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'VH_DAILY_SLA_NGOAI_LE'
start_date = datetime.datetime(2022, 11, 16)
schedule_interval = None
description = 'Báo cáo sla ngoại lệ daily - TuyenDN'
tags = ['vh', 'daily']
department = 'Project - Vận hành'
file_name = 'SLA_NGOAI_LE'
report_name = 'SLA/Ngoại lệ'
overwrite = False
freq = 'daily'
sql, saved_folder = set_kwargs(parent_dir, file_name)
_type = 'Data Warehouse to SharePoint - Report F88'


def set_params(ds, **kwargs):
    time = kwargs['dag_run'].logical_date - datetime.timedelta(1)
    DATE_WID = time.strftime("%Y%m%d")
    params = {}
    params.update({'DATE_WID': DATE_WID})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')


def call_procedure(ds, **kwargs):
    param = kwargs['ti'].xcom_pull(key=file_name)
    hook = OracleHook(oracle_conn_id='oracle_f88_dwh')
    hook.callproc('PROC_W_OPS_SLA_EXCEPTIOIN', autocommit=True, parameters=param)


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=True,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start')

    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)

    update_by_call_procedure = PythonOperator(task_id='Call_Procedure', python_callable=call_procedure)

    extract = GetDataOracleOperator(task_id=f'extract_data_from_dwh_{file_name}',
                                    oracle_conn_id='oracle_f88_dwh',
                                    sql=sql,
                                    saved_folder=saved_folder,
                                    file_name=file_name,
                                    report_name=report_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    )

    load = UploadSharePointOperator(task_id=f'load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )

    end = EmptyOperator(task_id='End')

    start >> set_params >> update_by_call_procedure >> extract >> load >> end
