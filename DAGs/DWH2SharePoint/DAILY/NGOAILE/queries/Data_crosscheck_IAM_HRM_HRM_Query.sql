with hrm_status as (
    select
        <PERSON><PERSON>EmpId,
        HR_vEmp.EmpCode as HRMEmployeeCode,
        'ACTIVE' as HRMStatus,
        B.EditTime as HRMUpdateDate,
        B.CreateTime as HRMCreateDate
    FROM
        (
        select 
            AA.EmpID,
            BB.LSTinhtrangnhanvienID,
            ROW_NUMBER() OVER(PARTITION BY AA.EmpID
        ORDER BY
            BB.Rank ASC) AS RN,
            AA.EditTime,
            AA.CreateTime
        from
            iHRP_PROD_F88.dbo.HR_tblThongTinTinhTrangNhanVien AA
        left join iHRP_PROD_F88.dbo.LS_tblTinhTrangNhanVien BB ON
            BB.LSTinhTrangNhanVienID = AA.LSTinhTrangNhanVienID_New
        WHERE
            convert(datetime, GETDATE(), 103) BETWEEN convert(datetime, AA.FromDate, 103) 
            AND CASE
                WHEN convert(datetime, AA.ToDate, 103) IS NULL THEN CONVERT(datetime, N'2100/01/01', 103)
                ELSE convert(datetime, AA.ToDate, 103)
            END
            and (coalesce(AA.EditTime, AA.CreateTime) < cast(getdate() as date) or coalesce(AA.EditTime, AA.CreateTime) is null)
            --AND AA.EmpID = @EmpID
            ) B
    left join
        iHRP_PROD_F88.dbo.HR_vEmp HR_vEmp
    on
        B.EmpID = HR_vEmp.EmpID
    WHERE
        B.RN = 1
        and (B.LSTinhtrangnhanvienID not in (6, 7, 8) or B.LSTinhtrangnhanvienID is null)
), hrm_position_status as (
    select 
        EmpCode as HRMEmployeeCode,
        COALESCE(WorkingStatus, 'STATUS IS NULL') as WorkingStatus,
        JobTitle as HRMPosition,
        JobCode as HRMPositionCode,
        CurrentDepartmentName as HRMOrganization,
        CurrentDepartmentCode as HRMOrganizationCode,
        ConcurrentlyJobTitleCode as HRMConcurrentlyPositionCode,
        ConcurrentlyDepartmentName as HRMConcurrentlyOrganization,
        ConcurrentlyDepartmentCode as HRMConcurrentlyOrganizationCode,
        CreatedDate as HRMCreateDate,
        LastUpdate as HRMUpdateDate
    from 
        iHRP_PROD_F88_TichHop.dbo.HR_tblEmployee hte
    where 
        (WorkingStatus not in (6, 7, 8) or WorkingStatus is null)
        and (coalesce(LastUpdate, CreatedDate) < cast(getdate() as date) or coalesce(LastUpdate, CreatedDate) is null)
)
select 
    COALESCE(hrm_status.HRMEmployeeCode, hrm_position_status.HRMEmployeeCode) as HRMEmployeeCode,
    coalesce(hrm_status.HRMStatus, 'KHONG KHOP GIUA HE THONG NGUON HRM VA BANG TRUNG GIAN') as HRMStatus,
    hrm_position_status.HRMPosition,
    hrm_position_status.HRMPositionCode,
    hrm_position_status.HRMOrganization,
    hrm_position_status.HRMOrganizationCode,
    hrm_position_status.HRMConcurrentlyPositionCode,
    hrm_position_status.HRMConcurrentlyOrganization,
    hrm_position_status.HRMConcurrentlyOrganizationCode,
    COALESCE(hrm_status.HRMCreateDate, hrm_position_status.HRMCreateDate) as HRMCreateDate,
    COALESCE(hrm_status.HRMUpdateDate, hrm_position_status.HRMUpdateDate) as HRMUpdateDate
from 
    hrm_status
full outer join
    hrm_position_status
on
    hrm_status.HRMEmployeeCode = hrm_position_status.HRMEmployeeCode