SELECT
	LOAN_CODE "Mã HĐ",
	CREATED_DATE "Ngà<PERSON> vay",
	SHOP_CODE "Mã PGD",
	QLKV ,
	QLV ,
	AREA "<PERSON>ề<PERSON>",
	SHOP_NAME "Tên PGD",
	CUSTOMER_TYPE "KH cũ/mới",
	LOA<PERSON>_PACKAGE_NAME "Gói vay",
	DISB_TYPE "Hình thức giải ngân",
	DISB_AMT "Tiền gốc HĐ",
	TG_CHECKNHA ,
	TG_TDTD_DUYETTICKET ,
	TG_TUVAN ,
	TG_KH_BOSUNGHS ,
	CVKDMOI_TAOHDLAU ,
	CAPPHEDUYETF88_LAU ,
	CIMB_MANUAL ,
	CIMB_LOISMS ,
	CIMB_DANGXULY ,
	CIMB_DOISOATNAPAS ,
	CIMB_LOICHITM ,
	F88_LOIHT ,
	F88_LOICK ,
	F88_LOISMS ,
	TG_<PERSON>HA<PERSON> ,
	LYD<PERSON>_<PERSON><PERSON> ,
	TO<PERSON>L_TIME "Tổng thời gian xử lý",
	TG_GH<PERSON>HA<PERSON>_SLA ,
	TG_TAO_HS "TG tạo hồ sơ",
	FROMDATE ,
	FROMDATE_COMPLETE "Fromdate#complete" ,
	CHECK_NHA ,
	CUSTOMER_SCORE ,
	CUSTOMER_RANK
FROM F88DWH.W_OPS_SLA_EXCEPTIOIN
where trunc(CREATED_DATE) = TO_DATE(:DATE_WID,'YYYYMMDD')