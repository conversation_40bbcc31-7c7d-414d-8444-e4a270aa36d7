with iam_data as (
select 
    up.EmployeeCode as IAMEmployeeCode,
    case up.IsActived when 0 then 'CLOSE' when 1 then 'ACTIVE' end as IAMStatus,
    a_position.NameV2 as IAMPosition,
    a_position.CodeV2  as IAMPositionCode,
    a_org.NameV2 as IAMOrganization,
    a_org.ShortNameV2 as IAMOrganizationCode,
    uma.Priority,
    ROW_NUMBER() over(partition by up.EmployeeCode order by uma.Priority) as rn
from sso_db.user_profile up 
inner join 
    sso_db.user_map_attributes uma
on
    up.Id = uma.UserId
left join
    sso_db.`attributes` a_position
on
    uma.`Position` = a_position.Id
left join
    sso_db.`attributes` a_org
on
    uma.Organization = a_org.Id 
where uma.IsActived = 1 and up.IsActived = 1
and up.IsDeleted <> 1
and (uma.UpdateAt < cast(now() as date) or uma.UpdateAt is null)
-- and uma.Priority = 1
)
select
	<PERSON>AMEmployee<PERSON><PERSON>,
	IAMStatus,
	IAMPosition,
	IAMPositionC<PERSON>,
	IAMOrganization,
	IAMOrganizationCode
from
	iam_data
where (Priority = 1
	or rn = 1)