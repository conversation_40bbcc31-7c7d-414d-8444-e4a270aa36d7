from email.header import Header
from email.utils import formataddr
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email import encoders
from datetime import datetime, date, timedelta
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.microsoft.mssql.hooks.mssql import MsSqlHook
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
import logging
import os
from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from Provider.utils import set_saved_folder_and_file_name
from Provider.SharePointProvider.operators.UploadOperators import UploadSharePointOperator
from airflow.models import Variable
from pathlib import Path
from DAGs.utils import set_kwargs

dt = (date.today()).strftime('%Y%m%d')

overwrite = False
_type = 'Data Warehouse to SharePoint - Report F88'
freq = 'daily'
department = 'Project - CNTT'
report_name = 'Báo cáo tra soát dữ liệu IAM - HRM'

dag_name = 'CNTT_DAILY_Data_crosscheck_IAM_HRM'
start_date = datetime(2023, 10, 4)
schedule_interval = None
description = 'Báo cáo tra soát dữ liệu IAM - HRM'
tags = ['cntt', 'daily']

parent_folder = Path(__file__).resolve().parent

file_name = f'Report_crosscheck_IAM_HRM_{dt}'

iam_query_file_name = 'Data_crosscheck_IAM_HRM_IAM_Query'

hrm_query_file_name = 'Data_crosscheck_IAM_HRM_HRM_Query'

iam_query, saved_folder = set_kwargs(parent_folder, iam_query_file_name)

hrm_query, saved_folder = set_kwargs(parent_folder, hrm_query_file_name)

def send_mail(send_to: str = '<EMAIL>', **context):
    file_path = context['ti'].xcom_pull(key='Report_crosscheck_IAM_HRM_File_Path')
    dt = str(date.today())
    p_content = f'''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị,</p>
        <p></p>
        <p>TT PTCL gửi anh/chị số liệu đối soát IAM - HRM ngày {dt}</p>
        <p>Trân trọng!</p>
    </body>
    </html>
    '''
    # Sender Info
    sender_address = '<EMAIL>'
    sender_pass = 'F88@6388'
    #Setup the MIME
    message = MIMEMultipart()
    message['From'] = formataddr((str(Header('Phòng Quản trị dữ liệu', 'utf-8')), sender_address))
    message['To'] = send_to
    message['Subject'] = 'Báo cáo đối soát dữ liệu IAM - HRM ngày ' + dt

    message.attach(MIMEText(p_content, 'html'))
    
    part = MIMEBase('application', "octet-stream")
    part.set_payload(open(file_path, "rb").read())
    encoders.encode_base64(part)
    attachment_filename = f'attachment; filename="{file_name}.xlsx"'
    part.add_header('Content-Disposition', attachment_filename)
    
    message.attach(part)
    
    session = smtplib.SMTP('smtp.f88.co', 587)
    session.starttls()
    session.login(sender_address, sender_pass)
    
    session.sendmail(sender_address, send_to.split(','), message.as_string())
    session.quit()
    logging.info('Done sending email to CNTT')

def cross_check_data(**context):
    execution_date = (context['dag_run'].logical_date - timedelta(1)).strftime('%Y%m%d')
    year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
    saved_folder_file, file_name_excel = set_saved_folder_and_file_name(overwrite, freq,
                                                                 saved_folder,
                                                                 file_name, report_name, year,
                                                                 month, day)
    file_path = os.path.join(saved_folder_file, file_name_excel)
    iam_hook = MySqlHook(mysql_conn_id='iam_mysql_conn_id')
    
    hrm_hook = MsSqlHook(mssql_conn_id='hrm_mssql_conn_id')
    
    iam_df = iam_hook.get_pandas_df(sql=iam_query)

    hrm_df = hrm_hook.get_pandas_df(sql=hrm_query)
    
    hrm_iam_df = hrm_df.merge(iam_df, left_on='HRMEmployeeCode', right_on='IAMEmployeeCode', how='outer')
    hrm_iam_df['check_Status'] = hrm_iam_df.IAMStatus == hrm_iam_df.HRMStatus
    hrm_iam_df['check_PositionCode'] = hrm_iam_df.HRMPositionCode == hrm_iam_df.IAMPositionCode # np.logical_or(hrm_iam_df.HRMPositionCode == hrm_iam_df.IAMPositionCode, hrm_iam_df.HRMConcurrentlyPositionCode == hrm_iam_df.IAMPositionCode)
    hrm_iam_df['check_OrganizationCode'] = hrm_iam_df.HRMOrganizationCode == hrm_iam_df.IAMOrganizationCode # np.logical_or(hrm_iam_df.HRMOrganizationCode == hrm_iam_df.IAMOrganizationCode, hrm_iam_df.HRMConcurrentlyOrganizationCode == hrm_iam_df.IAMOrganizationCode)
    hrm_iam_df['check_all'] = np.logical_and(np.logical_and(hrm_iam_df.check_Status, hrm_iam_df.check_PositionCode), hrm_iam_df.check_OrganizationCode)
    
    hrm_iam_df.fillna({'HRMEmployeeCode': 'NOT IN IAM', 'IAMEmployeeCode': 'NOT IN HRM'}, inplace = True)
    
    hrm_iam_df[hrm_iam_df.check_all == False].to_excel(file_path, index=False, sheet_name=datetime.now().date().strftime('%Y%m%d'))
    
    context['ti'].xcom_push(key='Report_crosscheck_IAM_HRM_File_Path', value=file_path)

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'trigger_rule': 'all_success'
         },
         tags=tags) as dag:
    start = EmptyOperator(task_id='Start', dag=dag)

    extract_and_check_data = PythonOperator(task_id='extract_and_check_data', python_callable=cross_check_data, dag=dag, provide_context=True)
    
    send_result = PythonOperator(task_id='send_email', python_callable=send_mail, dag=dag, provide_context=True, op_kwargs={'send_to': '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'}) #'<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
        
    upload_to_sharepoint = UploadSharePointOperator(task_id='load_file_into_sharepoint',
                                    sharepoint_conn_id='sharepoint_f88_data',
                                    department=department,
                                    report_name=report_name,
                                    local_saved_folder=saved_folder,
                                    local_file_name=file_name,
                                    overwrite=overwrite,
                                    freq=freq,
                                    _type=_type
                                    )
    
    end = EmptyOperator(task_id='End', dag=dag)

    start >> extract_and_check_data >> send_result >> end
    extract_and_check_data >> upload_to_sharepoint >> end