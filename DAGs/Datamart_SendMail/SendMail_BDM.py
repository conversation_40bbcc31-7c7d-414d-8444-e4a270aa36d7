import smtplib
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.utils import formataddr
from email.header import Header
from DAGs.Datamart_SendMail.config import *
from airflow.hooks.base import BaseHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.providers.microsoft.mssql.hooks.mssql import MsSqlHook
from airflow.models import Variable

import pandas as pd
from datetime import datetime

Email = int(Variable.get("EMAIL_BDM", default_var="0"))

your_email = Config.BOT_EMAIL
your_password = Config.BOT_EMAIL_PWD
EMAIL_ADMIN = Config.EMAIL_ADMIN


def get_list_data():
    print(">>> Bắt đầu lấy danh sách bảng cần kiểm tra...")
    query = """
        SELECT ROW_NUMBER() OVER(ORDER BY id) as STT,TABLE_SOURCE, VALUE_CONDITION, NOTE_EMAIL, PHASE, VALUE_CONDITION_TARGET
        FROM W_DECLARE_DATAMART_BDM wddb
        WHERE TABLE_SOURCE NOT IN ('W_LOAN_DAILY_F','W_POL_JOURNEY_TRANS_STEP_F')
        ORDER BY id
    """
    conn_id = 'oracle_f88_dwh'
    oracle_hook = OracleHook(oracle_conn_id=conn_id)
    conn = oracle_hook.get_conn()
    oracle_hook = OracleHook(oracle_conn_id='oracle_f88_dwh')
    cursor = conn.cursor()

    cursor.execute(query)
    rows = cursor.fetchall()
    columns = [col[0] for col in cursor.description]
    df = pd.DataFrame(rows, columns=columns)

    cursor.close()
    conn.close()
    print(f">>> Đã lấy {len(df)} bảng từ cấu hình.")
    return df


def get_data(df: pd.DataFrame):
    print(">>> Bắt đầu kiểm tra dữ liệu các bảng...")

    # Oracle = Warehouse
    oracle_hook = OracleHook(oracle_conn_id='oracle_f88_dwh')
    conn_oracle = oracle_hook.get_conn()
    cursor_oracle = conn_oracle.cursor()

    # SQL Server = Mart
    mssql_hook = MsSqlHook(mssql_conn_id='bdm_f88')
    conn_sqlserver = mssql_hook.get_conn()
    cursor_sqlserver = conn_sqlserver.cursor()

    list_column = ["STT", "Table_name", "Row_num_warehouse",
                   "Row_num_mart", "Ghi_chu", "Phase"]
    data = pd.DataFrame(columns=list_column)

    for index, row in df.iterrows():
        table_name = row["TABLE_SOURCE"]
        ghi_chu = row["NOTE_EMAIL"]
        condition = row["VALUE_CONDITION"]
        condition_mssql = row["VALUE_CONDITION_TARGET"]
        phase = row["PHASE"]
        STT = row["STT"]
        print(f"--> Đang xử lý bảng: {table_name}")
        count_warehouse = None
        count_mart = None

        # Query từ Oracle
        try:
            count_warehouse_query = f"SELECT COUNT(*) FROM {table_name} WHERE {condition}"
            cursor_oracle.execute(count_warehouse_query)
            count_warehouse = cursor_oracle.fetchone()[0]
            print(f"    - Row_num_warehouse: {count_warehouse}")
        except Exception as e:
            print(f"    - Lỗi truy vấn Oracle (warehouse): {e}")

        # Query từ SQL Server
        try:
            count_mart_query = f"SELECT COUNT(*) FROM BDM_DATA_MART.F88DWH.{table_name} WITH (NOLOCK) WHERE {condition_mssql}"
            cursor_sqlserver.execute(count_mart_query)
            count_mart = cursor_sqlserver.fetchone()[0]
            print(f"    - Row_num_mart: {count_mart}")
        except Exception as e:
            print(f"    - Lỗi truy vấn SQL Server (mart): {e}")

        # Ghi vào DataFrame
        data = data.append({
            "Table_name": table_name,
            "Row_num_warehouse": count_warehouse,
            "Row_num_mart": count_mart,
            "Ghi_chu": ghi_chu,
            "Phase": phase,
            "STT": STT
        }, ignore_index=True)

    print(">>> Hoàn tất kiểm tra.")
    return data


def send_email_datamart_to_bdm(requestor, status, reason, dagId):
    if Email == 0:
        print("Sending email check bdm mart data has been stopped")
        return
    print(">>> Bắt đầu gửi email thống kê...")

    # Lấy dữ liệu từ Oracle và MSSQL
    try:
        df = get_list_data()
        data = get_data(df)
    except Exception as e:
        print(f">>> Lỗi khi lấy dữ liệu: {e}")
        return

    date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Tạo nội dung bảng HTML
    html_rows = ''
    for _, row in data.iterrows():
        # So sánh hai giá trị row count
        highlight = row['Row_num_warehouse'] != row['Row_num_mart']

        # Nếu khác nhau thì gán CSS cho từng ô
        style_wh = 'style="color:red; background-color:#ffe6e6;"' if highlight else ''
        style_mart = 'style="color:red; background-color:#ffe6e6;"' if highlight else ''

        html_rows += f"""
        <tr>
            <td>{row['STT']}</td>
            <td>{row['Table_name']}</td>
            <td {style_wh}>{row['Row_num_warehouse']}</td>
            <td {style_mart}>{row['Row_num_mart']}</td>
            <td>{row['Ghi_chu']}</td>
            <td>{row['Phase']}</td>
        </tr>
        """

    # Tạo template email
    template = f"""
    <html>
    <head><meta charset="utf-8" /></head>
    <body>
        <p><b>Dear A/c/e,</b></p>
        <p>Phòng QTDL xin thông báo tới đơn vị về dữ liệu data mart và data warehouse.</p>
        <p>Dữ liệu được thống kê đến ngày <span style="color: red;">{date}</span></p>
        <p>Thông tin chi tiết:</p>
        <table border="1" cellspacing="0" cellpadding="4" style="border-collapse: collapse;">
            <tr>
                <th>STT</th>
                <th>Table name</th>
                <th>Row num warehouse</th>
                <th>Row num mart</th>
                <th>Ghi chú</th>
                <th>Phase</th>
            </tr>
            {html_rows}
        </table>
        <p>Mọi thắc mắc a/c/e vui lòng liên hệ P.QTDL, Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        <p><b><i>Trân trọng!</i></b></p>
    </body>
    </html>
    """

    try:
        # Lấy thông tin email từ Variable
        email_var = Variable.get('BI_EMAIL', deserialize_json=True)
        sender_address = email_var.get('email')
        sender_pass = email_var.get('password')

        # Tạo message
        print(f"list email: {requestor} ")
        recipients = []
        for r in requestor:
            r = r.strip()
            if r:
                if '@' not in r:
                    r += '@f88.vn'
                recipients.append(r)

        if not recipients:
            print(">>> Không có người nhận hợp lệ.")
            return

        recipient_emails = ', '.join(recipients)
        msg = MIMEMultipart()
        msg['From'] = sender_address
        msg['To'] = recipient_emails
        msg['Subject'] = 'V/V: THÔNG BÁO VỀ DỮ LIỆU DAILY DATA MART BDM'
        msg.attach(MIMEText(template, 'html'))

        # Gửi email
        session = smtplib.SMTP('smtp.f88.co', 587)
        session.starttls()
        session.login(sender_address, sender_pass)
        session.send_message(msg)
        session.quit()
        print(">>> Email đã được gửi thành công đến BDM.")

    except Exception as e:
        print(f">>> Gửi email thất bại: {e}")
