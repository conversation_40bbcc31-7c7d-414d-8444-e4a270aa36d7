import os
import datetime
import logging
import pandas as pd
from airflow import DAG
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from DAGs.utils import send_msg, set_kwargs
from DAGs.utils import send_msg
import pytz

parent_dir = os.path.dirname(os.path.abspath(__file__))
file_name_ora = 'HV-PERFORMANCE-ORA'
file_name_hv = 'HV-PERFORMANCE-HV'
dag_name = 'BOT_GAPO_HV_PERFORMANCE'
oracle_conn_id = 'oracle_f88_dwh'
hv_conn_id = 'mysql_hv'
description = 'BOT GAPO cho HV'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 10 * * *'
tags = ['hv', 'bot-gapo']
sql_ora, saved_folder = set_kwargs(parent_dir, file_name_ora)
sql_hv, saved_folder = set_kwargs(parent_dir, file_name_hv)
def get_data():
    ora_hook = OracleHook(oracle_conn_id=oracle_conn_id)
    df_ora = ora_hook.get_pandas_df(sql=sql_ora)
    df_cource = ora_hook.get_pandas_df(sql="select ID_CATEGORY,START_DAY,END_DAY,LINK_COURSE,NAME_COURSE  from F88DWH.W_HV_CATERGORY WHERE STATUS  = '1'")
    hv_hook = MySqlHook(mysql_conn_id=hv_conn_id)
    df_hv = hv_hook.get_pandas_df(sql=sql_hv)
    return df_ora, df_hv, df_cource

def send_messages(data, name):
    f = open(parent_dir + "/messages" + "/HV-PERFORMANCE.txt", "r")
    message_list = f.read()
    message_list = message_list.split('--split')
    config = Variable.get("GAPO_BOT_HV", deserialize_json=True)
    if name == 'hv_1':
        for i in data.index:
            message_tmp = message_list[0]
            message_tmp = message_tmp.replace('{name}', data['EMPLOYEE_NM'][i])
            message_tmp = message_tmp.replace('{wrd_day}', str(int(data['WRK_DAY'][i])))
            message_tmp = message_tmp.replace('{list_cources}', data['LINK_COURSE_NAME'][i])
            msg = {
                'message': message_tmp,
                'config': config,
                'receiver': f"{data['GAPO_ID'][i]}",
                'receiver_type': 'partner_id',
            }
            send_msg(**msg)
    if name == 'hv_2':
        for i in data.index:
            message_tmp = message_list[1]
            message_tmp = message_tmp.replace('{name}', data['EMPLOYEE_NM'][i])
            message_tmp = message_tmp.replace('{pgd}', (data['SHOP_NM'][i]))
            message_tmp = message_tmp.replace('{tuan}', str(int(data['tg'][i])))
            msg = {
                'message': message_tmp,
                'config': config,
                'receiver': f"{data['GAPO_ID'][i]}",
                'receiver_type': 'partner_id',
            }
            send_msg(**msg)
def create_and_transform_data() -> None:
    tz_NY = pytz.timezone('Asia/Ho_Chi_Minh')
    date_now = datetime.datetime.now(tz_NY).strftime("%Y-%m-%d")
    # date_now = datetime.datetime(2023,4,8).strftime("%Y-%m-%d")
    df_ora, df_hv, df_cource = get_data()
    df_ora_cross = df_ora[['EMPLOYEE_CODE']]
    df_cource['key'] = 1
    df_ora_cross['key'] = 1
    df_temp1 = df_cource.merge(df_ora_cross, on='key').drop("key", 1)
    df_temp2 = pd.merge(df_temp1, df_hv, how="left", on=['NAME_COURSE', 'EMPLOYEE_CODE' ])
    data_1 = df_temp2.merge(df_ora, how="left", on=['EMPLOYEE_CODE'])
    data_1['status_sendmail'] = None
    for i in data_1.index:
        if( data_1['END_DAY'][i] - data_1['WRK_DAY'][i]<= 2 and data_1['status_complete'][i] != 1):
           data_1.loc[i, 'status_sendmail'] = 1
    data_1 = data_1[(data_1.status_sendmail == 1)]
    if data_1 is not None:
        data_1['LINK_COURSE_NAME'] = '- '+data_1['NAME_COURSE'] + ' (' + data_1['LINK_COURSE'] + ')'
        data_1 = data_1[['LINK_COURSE_NAME', 'GAPO_ID']]
        data_1 = data_1.groupby(['GAPO_ID'])['LINK_COURSE_NAME'].apply('\n'.join).reset_index()
        data_1 = pd.merge(df_ora, data_1, how="inner", on=['GAPO_ID'])
        send_messages(data_1,'hv_1')
    df_ora['status_sendmail'] = None
    for i in df_ora.index:
        if((df_ora['TUAN_1'][i].strftime("%Y-%m-%d") == date_now and df_ora['CHECK_T1'][i]== 'Không ontrack') or (df_ora['TUAN_2'][i].strftime("%Y-%m-%d") == date_now and df_ora['CHECK_T2'][i]== 'Không ontrack') or (df_ora['TUAN_3'][i].strftime("%Y-%m-%d") == date_now and df_ora['CHECK_T3'][i]== 'Không ontrack') or (df_ora['TUAN_4'][i].strftime("%Y-%m-%d") == date_now and df_ora['CHECK_T4'][i]== 'Không ontrack') ):
            df_ora.loc[i, 'status_sendmail'] = 1
            if(df_ora['TUAN_1'][i].strftime("%Y-%m-%d") == date_now):
                df_ora.loc[i, 'tg'] = df_ora.loc[i, 'TG_1']
            if(df_ora['TUAN_2'][i].strftime("%Y-%m-%d") == date_now):
                df_ora.loc[i, 'tg'] = df_ora.loc[i, 'TG_2']
            if(df_ora['TUAN_3'][i].strftime("%Y-%m-%d") == date_now):
                df_ora.loc[i, 'tg'] = df_ora.loc[i, 'TG_3']
            if(df_ora['TUAN_4'][i].strftime("%Y-%m-%d") == date_now):
                df_ora.loc[i, 'tg'] = df_ora.loc[i, 'TG_4']
    data_2 = df_ora[(df_ora.status_sendmail == 1)]
    if data_2 is not None:
        send_messages(data_2, 'hv_2')
with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id='Start')
    end = EmptyOperator(task_id='End')
    create_and_send_msg = PythonOperator(task_id='Create_and_Send_MSG_to_PTSP', python_callable= create_and_transform_data)
    start >> create_and_send_msg >> end
