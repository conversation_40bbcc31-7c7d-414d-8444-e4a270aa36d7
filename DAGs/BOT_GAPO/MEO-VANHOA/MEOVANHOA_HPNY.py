from datetime import datetime
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from DAGs.External2DWH.AWS_SQS.voicebot_sqs_etl import df_to_oracle
import os
from DAGs.utils import send_msg, set_kwargs
from airflow.providers.oracle.hooks.oracle import OracleHook
import logging
from airflow.models import Variable
import random
import pandas as pd

dag_name = 'MEOVANHOA_HPNY'
# scheduler_interval = None
# start_date = datetime(2024, 2, 6, 8, 00)
scheduler_interval = '@once'
start_date = datetime(2024, 2, 15, 8, 00)
parent_dir = os.path.dirname(os.path.abspath(__file__))
sql, saved_folder = set_kwargs(parent_dir, dag_name)
oracle_conn_id = 'oracle_f88_dwh'
ballots = {
    1: "https://vay.f88.vn/QueTaiLoc1",
    2: "https://vay.f88.vn/QueTaiLoc2",
    3: "https://vay.f88.vn/QueTaiLoc3",
    4: "https://vay.f88.vn/QueTaiLoc4",
    5: "https://vay.f88.vn/QueTaiLoc5",
    6: "https://vay.f88.vn/QueTaiLoc6",
    7: "https://vay.f88.vn/QueTaiLoc7",
    8: "https://vay.f88.vn/QueTaiLoc8",
    9: "https://vay.f88.vn/QueTaiLoc9",
    10: "https://vay.f88.vn/QueTaiLoc",
}


def import_list_user():
    # Excel file path
    excel_file_path = './files/hpny_lst.xlsx'

    # Sheet name or index (0-based)
    sheet_name = 'DS Tổng'  # Replace with the desired sheet name or index

    # Define the column names and their indexes
    column_names = {
        0: 'group_num',
        2: 'employee_code',
        3: 'gapo_id',
        6: 'email',
        16: 'status'
        # Add more columns if needed
    }

    # Read Excel sheet into a DataFrame, skipping the first two rows and using the defined column names
    df = pd.read_excel(excel_file_path, sheet_name=sheet_name, skiprows=3, header=None)
    df.rename(columns=column_names, inplace=True)
    # Select only the columns defined in column_names
    df = df[list(column_names.values())]
    # df.group_num = df.group_num.astype(str)
    df.group_num = df.group_num.astype(int)
    df.employee_code = df.employee_code.astype(str)
    df.gapo_id = df.gapo_id.fillna('0').astype(int)
    df.email = df.email.astype(str)
    df.status = df.status.astype(str)
    df_to_oracle('F88DWH.w_hpny_employee_f', 'oracle_f88_dwh', df)
    # Print the DataFrame


def get_list_gapo_id_by_group(group):
    ora_hook = OracleHook(oracle_conn_id=oracle_conn_id)
    logging.info(f"sql: {sql}")
    params = {'group_num': group}
    df_ora = ora_hook.get_pandas_df(sql=sql, parameters=params)
    logging.info(f"data: {df_ora}")
    return df_ora


def execute():
    config = Variable.get("GAPO_BOT_VAN_HOA", deserialize_json=True)
    f = open(parent_dir + "/messages" + "/hpny_2024.txt", "r")
    message = f.read()
    # Tạo một danh sách chứa các khóa của từ điển ballots
    keys = list(ballots.keys())

    # Lặp 10 lần để lấy ra các phần tử khác nhau ngẫu nhiên
    for i in range(10):
        # Lấy một phần tử ngẫu nhiên từ danh sách keys
        random_key = random.choice(keys)
        content = message.replace('{link}', ballots[random_key])

        # Xóa khóa đã được chọn để không lặp lại
        keys.remove(random_key)
        # get list gapo_id user to send content
        gapo_ids = get_list_gapo_id_by_group(i + 1)
        gapo_ids.GAPO_ID = gapo_ids.GAPO_ID.fillna(0).astype(int)
        for row in gapo_ids.itertuples():
            msg = {
                'message': content,
                'config': config,
                'receiver': f"{row.GAPO_ID}",
                'receiver_type': 'partner_id',
            }
            send_msg(**msg)

with DAG(dag_id=dag_name,
         start_date=start_date,
         schedule_interval=scheduler_interval) as dag:
    start = EmptyOperator(task_id='Start')
    # lấy gapo ID vs tin nhắn + link quẻ
    send_happy_new_year_wishes = PythonOperator(task_id='send_happy_new_year_wishes',
                               python_callable=execute,
                               provide_context=True)
    end = EmptyOperator(task_id='End')

start >> send_happy_new_year_wishes >> end


