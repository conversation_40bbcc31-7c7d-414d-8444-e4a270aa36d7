INSERT INTO F88DWH.W_EARLY_WARNING_HISTORY
(DATE_WID, YEAR_NUM, MONTH_NUM, SHOP_NM,SHOP_CODE, CHANNEL_CODE, MNV_TPGD, DPD0_DAU_KY, DPD0_CUOI_KY, HĐMM_ĐKXM, TICKET_SIZE_ĐKXM, HĐMM_ĐKOTO, TICKET_SIZE_ĐKOTO, DPD_ROLL_1, DPD_ROLL1_RATE, DPD_ROLL_BACK, DPD_ROLLBACK_RATE, DPD_CLOSE, DPD_CLOSED_RATE)

SELECT
    :DATE_WID DATE_WID,
	T.YEAR_NUM,
	T.MONTH_NUM,
	S.SHOP_NM,
	S.SHORT_NM ,
	T.CHANNEL_CODE,
	A.MNV_TPGD,
	SUM(T.DPD0_DAU_KY/1000000) DPD0_DAU_KY ,
	SUM(T.DPD0_CUOI_KY/1000000) DPD0_CUOI_KY,
	<PERSON>UM(T.H<PERSON>MM_ĐKXM) HĐMM_ĐKXM,
	SUM(T.TICKET_SIZE_ĐKXM/1000000) TICKET_SIZE_ĐKXM,
	SUM(T.HĐMM_ĐKOTO) HĐMM_ĐKOTO,
	SUM(T.TICKET_SIZE_ĐKOTO/1000000) TICKET_SIZE_ĐKOTO,
	SUM(T.DPD_ROLL_1) DPD_ROLL_1,
	CASE WHEN SUM(T.DPD0_DAU_KY) = 0 THEN NULL ELSE SUM(T.DPD_ROLL_1)/SUM(T.DPD0_DAU_KY) END DPD_ROLL1_RATE,
	SUM(T.DPD_ROLL_BACK) DPD_ROLL_BACK,
	CASE WHEN SUM(T.DPD0_CUOI_KY) = 0 THEN NULL ELSE SUM(T.DPD_ROLL_BACK)/SUM(T.DPD0_CUOI_KY) END DPD_ROLLBACK_RATE,
	SUM(T.DPD0_CLOSED) DPD_CLOSE,
	CASE WHEN SUM(T.DPD0_CUOI_KY) = 0 THEN NULL ELSE SUM(T.DPD0_CLOSED)/SUM(T.DPD0_CUOI_KY) END DPD_CLOSED_RATE
FROM
(
--1.DPD0 ĐẦU KỲ
(
	SELECT
		TO_NUMBER(TO_CHAR(CAL.DATE_TM + 1,'YYYY')) YEAR_NUM,
		TO_NUMBER(TO_CHAR(CAL.DATE_TM + 1,'MM')) MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE,
		SUM(LOAN_ACTIVE) DPD0_DAU_KY,
		0 DPD0_CUOI_KY,
		0 HĐMM_ĐKXM,
		0 TICKET_SIZE_ĐKXM,
		0 HĐMM_ĐKOTO,
		0 TICKET_SIZE_ĐKOTO,
		0 DPD_ROLL_1,
		0 DPD_ROLL_BACK,
		0 DPD0_CLOSED
	FROM
		F88DWH.W_TRAFFIC_LOAN_DAILY_F TLD,
		F88DWH.W_CALENDAR_D CAL
	WHERE
		TLD.CREATED = CAL.DATE_TM
		AND CAL.LAST_DOM_FLAG = 1
		AND TLD.GROUP_DEBT = 0
		AND CAL.DATE_WID = TO_NUMBER(TO_CHAR(LAST_DAY(ADD_MONTHS( to_date(to_char(:DATE_WID),'YYYYMMDD'),-1)),'YYYYMMDD'))
	GROUP BY
		TO_NUMBER(TO_CHAR(CAL.DATE_TM + 1,'YYYY')) ,
		TO_NUMBER(TO_CHAR(CAL.DATE_TM + 1,'MM')) ,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE
	HAVING SUM(LOAN_ACTIVE)>0
)
UNION ALL
--2.DPD0 CUỐI KỲ
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE,
		0,
		SUM(LOAN_ACTIVE) DPD0_CUOI_KY,
		0,0,0,0,0,0,0
	FROM
		F88DWH.W_TRAFFIC_LOAN_DAILY_F TLD,
		F88DWH.W_CALENDAR_D CAL
	WHERE
		TLD.CREATED = CAL.DATE_TM
		AND (CAL.DATE_WID  = :DATE_WID OR CAL.LAST_DOM_FLAG =1)
		AND TLD.GROUP_DEBT = 0
	GROUP BY
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE
	HAVING SUM(LOAN_ACTIVE)>0
)
UNION ALL
--3,4.HĐMM_ĐKXM & TICKET_SIZE_ĐKXM
(
		SELECT
		to_number(Substr(TLD.DISBURSE_DATE_WID,1,4)) YEAR_NUM,
		to_number(Substr(TLD.DISBURSE_DATE_WID,5,2)) MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE ,
		0,0,
		count(1) HĐMM_ĐKXM,
		ROUND(SUM(DISBURSE_AMT)/count(1)) TICKET_SIZE_ĐKXM,
		0,0,0,0,0
	FROM
		F88DWH.W_LOAN_DTL_F TLD
	WHERE TLD.ASSET_TYPE_WID  = 17
		AND TLD.DISBURSE_DATE_WID >= TO_NUMBER(TO_CHAR(TRUNC(to_date(to_char(:DATE_WID),'YYYYMMDD'),'MM'),'YYYYMMDD')) AND TLD.DISBURSE_DATE_WID  <= :DATE_WID
		--AND SHOP_WID = 8372
	GROUP BY
        to_number(Substr(TLD.DISBURSE_DATE_WID,1,4)) ,
		to_number(Substr(TLD.DISBURSE_DATE_WID,5,2)),
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE
)
UNION ALL
--5,6.HĐMM_ĐKOTO & TICKET_SIZE_ĐKOTO
(
		SELECT
		to_number(Substr(TLD.DISBURSE_DATE_WID,1,4)) YEAR_NUM,
		to_number(Substr(TLD.DISBURSE_DATE_WID,5,2)) MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE ,
		0,0,0,0,
		count(1) HĐMM_ĐKOTO,
		ROUND(SUM(DISBURSE_AMT)/count(1)) TICKET_SIZE_ĐKOTO,
		0,0,0
	FROM
		F88DWH.W_LOAN_DTL_F TLD
	WHERE TLD.ASSET_TYPE_WID  = 15
		AND TLD.DISBURSE_DATE_WID >= TO_NUMBER(TO_CHAR(TRUNC(to_date(to_char(:DATE_WID),'YYYYMMDD'),'MM'),'YYYYMMDD')) AND TLD.DISBURSE_DATE_WID  <= :DATE_WID
		--AND SHOP_WID = 8372
	GROUP BY
        to_number(Substr(TLD.DISBURSE_DATE_WID,1,4)) ,
		to_number(Substr(TLD.DISBURSE_DATE_WID,5,2)),
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE
)
UNION ALL
--7.DƯ NỢ ROLL B1 DAILY
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE,
		0,0,0,0,0,0,
		SUM(LOAN_ROLL_1) DPD_ROLL_1,
		0,0
	FROM
		F88DWH.W_TRAFFIC_LOAN_DAILY_F TLD,
		F88DWH.W_CALENDAR_D CAL
	WHERE
		TLD.CREATED = CAL.DATE_TM
		AND CAL.DATE_WID >= TO_NUMBER(TO_CHAR(TRUNC(to_date(to_char(:DATE_WID),'YYYYMMDD'),'MM'),'YYYYMMDD')) AND CAL.DATE_WID  <= :DATE_WID
	GROUP BY
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE
	HAVING SUM(LOAN_ROLL_1)>0
)
UNION ALL
--8.DƯ NỢ ROLL BACK
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE,
		0,0,0,0,0,0,0,
		SUM(LOAN_ROLLBACK) DPD_ROLL_BACK,
		0
	FROM
		F88DWH.W_TRAFFIC_LOAN_DAILY_F TLD,
		F88DWH.W_CALENDAR_D CAL
	WHERE
		TLD.CREATED = CAL.DATE_TM
		AND CAL.DATE_WID >= TO_NUMBER(TO_CHAR(TRUNC(to_date(to_char(:DATE_WID),'YYYYMMDD'),'MM'),'YYYYMMDD')) AND CAL.DATE_WID  <= :DATE_WID
	GROUP BY
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		TLD.SHOP_WID,
		TLD.CHANNEL_CODE
	HAVING SUM(LOAN_ROLLBACK)>0
)
UNION ALL
--9.DƯ NỢ RÚT
(
	SELECT
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		LDT.SHOP_WID,
		LDT.CHANNEL_CODE,
		0,0,0,0,0,0,0,0,
		sum(LTR.PRINCIPAL_AMT) DPD0_CLOSED
	FROM
		F88DWH.W_LOAN_TRANS_DTL_F LTR,
		F88DWH.W_LOAN_DTL_F LDT,
		F88DWH.W_CALENDAR_D CAL,
		F88DWH.W_LOAN_DAILY_F LDA
	WHERE
		LTR.LOAN_WID = LDT.LOAN_WID
		AND LTR.ACTION_CODE IN ('DONG_HD','TRA_BOT_GOC','DONG_HD_CD','DONG_HD_DH','TRA_CPV','TRA_CPV_CIMB','DONG_HD_CD_CIMB','DONG_HD_CIMB')
	    AND LTR.TRANS_DATE_WID = CAL.DATE_WID
	    AND CAL.DATE_WID >= TO_NUMBER(TO_CHAR(TRUNC(to_date(to_char(:DATE_WID),'YYYYMMDD'),'MM'),'YYYYMMDD')) AND CAL.DATE_WID  <= :DATE_WID
	    AND LDA.YEAR_NUM = CAL.YEAR_NUM
	    AND LDA.MONTH_NUM = CAL.MONTH_NUM
	    AND LDA.DATE_WID = TO_NUMBER(TO_CHAR(CAL.DATE_TM-1,'YYYYMMDD'))
	    AND LDT.LOAN_WID = LDA.LOAN_WID
		AND LDA.OVERDUE_DAYS <= -1
	GROUP BY
		CAL.YEAR_NUM,
		CAL.MONTH_NUM,
		LDT.SHOP_WID,
		LDT.CHANNEL_CODE
	HAVING sum(LTR.PRINCIPAL_AMT)>0
)
) T
LEFT JOIN F88DWH.W_SHOP_D S ON T.SHOP_WID = S.SHOP_WID
LEFT JOIN F88DWH.W_AREA_MANAGER_D A ON A.SHOP_ID  = S.SHOP_CODE
GROUP BY
	T.YEAR_NUM,
	T.MONTH_NUM,
	S.SHOP_NM,
	S.SHORT_NM ,
	T.CHANNEL_CODE,
	A.MNV_TPGD
--split--
SELECT * FROM
(
SELECT EMPLOYEE_NM
      , SHOP_NM
      , MNV_TPGD
      , GAPO_ID
      ,
       (DECODE(DPD0_CHECK,NULL,NULL,'Dư nợ DPD0: '||trim(to_char(DPD0_CUOI_KY,'999990D90')) ||'/' || trim(to_char(DPD0_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||DPD0_ACTION || CHR(10))
      || DECODE(HDMM_DKXM_CHECK,NULL,NULL,'Hợp đồng mở mới xe máy: '||trim(to_char(HĐMM_ĐKXM,'999990D90')) ||'/' || trim(to_char(HDMM_DKXM_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||HDMM_DKXM_ACTION || CHR(10))
      || DECODE(HDMM_DKOTO_CHECK,NULL,NULL,'Hợp đồng mở mới ô tô: '||trim(to_char(HĐMM_ĐKOTO,'999990D90')) ||'/' || trim(to_char(HDMM_DKOTO_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||HDMM_DKOTO_ACTION || CHR(10))
      || DECODE(TICKET_SIZE_ĐKXM_CHECK,NULL,NULL,'Ticket size DKXM: '||trim(to_char(TICKET_SIZE_ĐKXM,'999990D90')) ||'/' || trim(to_char(TICKET_SIZE_DKXM_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||TICKET_SIZE_DKXM_ACTION || CHR(10))
      || DECODE(TICKET_SIZE_ĐKOTO_CHECK,NULL,NULL,'Ticket size DKOTO: '||trim(to_char(TICKET_SIZE_ĐKOTO,'999990D90')) ||'/' || trim(to_char(TICKET_SIZE_DKOTO_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||TICKET_SIZE_DKOTO_ACTION || CHR(10))
      || DECODE(ROLL_B1_CHECK,NULL,NULL,'Tỷ lệ nhảy nhóm B1: '||trim(to_char(DPD_ROLL1_RATE,'999990D90')) ||'/' || trim(to_char(ROLL_B1_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||ROLL_B1_ACTION || CHR(10))
      || DECODE(ROLL_BACK_CHECK,NULL,NULL,ROLL_BACK_ACTION || CHR(10))
      || DECODE(ROLL_BACK_CHECK,NULL,NULL,'Tỷ lệ Roll Back: '||trim(to_char(DPD_ROLLBACK_RATE,'999990D90')) ||'/' || trim(to_char(ROLL_BACK_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||ROLL_BACK_ACTION || CHR(10))
      || DECODE(ROLL_RUT_CHECK,NULL,NULL,RUT_ACTION || CHR(10))
      || DECODE(ROLL_RUT_CHECK,NULL,NULL,'Tỷ lệ Rut: '||trim(to_char(DPD_CLOSED_RATE,'999990D90')) ||'/' || trim(to_char(RUT_VALUE,'999990D90')) ||'. Action đưa ra:'||CHR(10) ||RUT_ACTION || CHR(10))
      ) STATUS
FROM
(
SELECT  wed.EMPLOYEE_NM, A.SHOP_NM , A.MNV_TPGD , wgi.GAPO_ID, A.DPD0_CUOI_KY  , B.DPD0_VALUE,A.HĐMM_ĐKXM , B.HDMM_DKXM_VALUE,A.HĐMM_ĐKOTO  , B.HDMM_DKOTO_VALUE
	  , A.TICKET_SIZE_ĐKXM  , B.TICKET_SIZE_DKXM_VALUE
	  , A.TICKET_SIZE_ĐKOTO  , B.TICKET_SIZE_DKOTO_VALUE
      , ROUND(A.DPD_ROLL1_RATE,2) DPD_ROLL1_RATE , ROUND(B.ROLL_B1_VALUE,2) ROLL_B1_VALUE
      , ROUND(A.DPD_ROLLBACK_RATE,2) DPD_ROLLBACK_RATE, ROUND(B.ROLL_BACK_VALUE ,2) ROLL_BACK_VALUE
      , ROUND(A.DPD_CLOSED_RATE ,2) DPD_CLOSED_RATE, ROUND(B.RUT_VALUE  ,2) RUT_VALUE
      , CASE WHEN A.DPD0_CUOI_KY  < B.DPD0_VALUE THEN '1'   END DPD0_CHECK
      , CASE WHEN A.HĐMM_ĐKXM < B.HDMM_DKXM_VALUE THEN '1'   END HDMM_DKXM_CHECK
      , CASE WHEN A.HĐMM_ĐKOTO  < B.HDMM_DKOTO_VALUE  THEN '1'  END HDMM_DKOTO_CHECK
      , CASE WHEN A.TICKET_SIZE_ĐKXM  < B.TICKET_SIZE_DKXM_VALUE  THEN '1'   END TICKET_SIZE_ĐKXM_CHECK
      , CASE WHEN A.TICKET_SIZE_ĐKOTO  < B.TICKET_SIZE_DKOTO_VALUE  THEN '1'  END TICKET_SIZE_ĐKOTO_CHECK
      , CASE WHEN ROUND(A.DPD_ROLL1_RATE,2) > ROUND(B.ROLL_B1_VALUE,2) THEN '1'   END ROLL_B1_CHECK
      , CASE WHEN ROUND(A.DPD_ROLLBACK_RATE,2) < ROUND(B.ROLL_BACK_VALUE ,2) THEN '1'   END ROLL_BACK_CHECK
      , CASE WHEN ROUND(A.DPD_CLOSED_RATE ,2) > ROUND(B.RUT_VALUE  ,2) THEN '1'   END ROLL_RUT_CHECK
      , c.*
FROM  F88DWH.W_EARLY_WARNING_HISTORY A
LEFT JOIN F88DWH.W_EARLY_WARNING_TARGET B ON A.SHOP_CODE  = B.SHOP_CODE AND to_number(to_char(B.DATE_, 'YYYYMMDD'))  = :DATE_WID
INNER JOIN F88DWH.W_EMPLOYEE_D wed ON A.MNV_TPGD  = wed.EMPLOYEE_CODE AND  wed.CRN_ROW_IND  = 1
INNER JOIN F88DWH.W_GAPO_ID wgi ON wgi.EMAIL  = wed.EMAIL
LEFT JOIN F88DWH.W_EARLY_WARNING_ACTION c ON 1 = 1
WHERE A.CHANNEL_CODE  = 'KDML' AND A.DATE_WID  = :DATE_WID AND A.YEAR_NUM  = :YEAR_NUM AND A.MONTH_NUM  = :MONTH_NUM
AND A.SHOP_CODE  IN (
	'HNI15003','HNI15002','HNI15001','HNI17006','HNI17024','HNI17025'
)
) tmp
)
where STATUS is not null

