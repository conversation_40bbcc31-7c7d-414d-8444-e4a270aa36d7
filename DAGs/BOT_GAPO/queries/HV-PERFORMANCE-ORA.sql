SELECT dt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_NM ,dt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CODE,wsd.SHOP_NM ,dt.JOB_DATE_JOIN, dt.<PERSON>K_DAY, TUAN_1 ,TUAN_2,TUAN_3,TUAN_4 ,CH<PERSON><PERSON>_T1 ,CH<PERSON><PERSON>_T2 ,CHEC<PERSON>_T3 ,CHECK_T4  ,wgi.GAPO_ID,CEIL(KHM_TG_3/4) tg_1, CEIL(KHM_TG_3/2)  tg_2,CEIL(KHM_TG_3 * (2/3)) tg_3, CEIL(KHM_TG_3) tg_4  FROM NGANNT8.DTHT_TRACKING dt
INNER JOIN F88DWH.W_EMPLOYEE_D wed ON dt.EMPLOYEE_CODE  = wed.EMPLOYEE_CODE AND wed.EMAIL  LIKE '%@f88.co' AND wed.CRN_ROW_IND  = 1
INNER JOIN F88DWH.W_GAPO_ID wgi ON wgi.EMAIL  = wed.EMAIL
LEFT JOIN F88DWH.W_SHOP_D wsd ON wsd.SHORT_NM  = wed.DEPARTMENT_CODE  AND wsd.CRN_ROW_IND  = 1