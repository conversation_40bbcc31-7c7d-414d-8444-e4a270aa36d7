from typing import Any, Dict
from airflow.models import BaseOperator
from airflow.utils.decorators import apply_defaults
from DAGs.utils import DAGMonitor
from Provider.SharePointProvider.hooks.UploadHooks import UploadSharePointHook


class UploadSharePointOperator(BaseOperator):

    @apply_defaults
    def __init__(
            self,
            sharepoint_conn_id: str,
            department: str,
            report_name: str,
            local_file_name: str,
            local_saved_folder: str,
            overwrite: bool,
            freq: Any,
            _type: Any,
            **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)
        self.sharepoint_conn_id = sharepoint_conn_id
        self.department = department
        self.report_name = report_name
        self.local_file_name = local_file_name
        self.local_saved_folder = local_saved_folder
        self.overwrite = overwrite
        self.freq = freq
        self._type = _type

    def execute(self, context: Dict[str, Any]) -> Any:
        hook = UploadSharePointHook(self.sharepoint_conn_id)
        self.log.info("Call SharePoint method")
        self.parameters = DAGMonitor.set_params_operator(**context)
        execution_date = self.parameters['YESTERDAY']
        hook.run(self.department, self.report_name, self.local_file_name, self.local_saved_folder,
                 self.overwrite, self.freq, self._type, execution_date)
