from pydantic import BaseModel, Field
from typing import Dict, List
from Provider.OracleProvider.utils import TableSchema

class Xlsx_Read_Options(BaseModel): 
    # Lấy header ở dòng nào. Index tính từ 1
    header: int | None = Field(default=None) # Index Starts With 1
    # Bỏ qua các dòng nào. Index tính từ 1
    skiprows: int | List[int] | None = Field(default=None) # Index Starts With 1
    
class TableMetaData(BaseModel):
    # Tên sheet trong file excel mà mình muốn đọc
    xlsx_sheet_name: str | None = Field(default=None)
    # Các option khi đọc file dữ liệu
    xlsx_read_options: Xlsx_Read_Options | None = Field(default=None)
    # Tên cột muốn đổi tên
    rename_columns: Dict[str, str] = Field(default={})
    # Tên cột muốn đổi kiểu dữ liệu
    datatype_mapping: Dict[str, str] = Field(default={})
    # Danh sách các cột muốn insert vào bảng staging
    list_columns_to_ingest_to_stg: List[str] = Field(default=[])
    # Cấu trúc bảng stg
    stg_table_schema: TableSchema
    # Logic SQL insert từ bảng stg vào bảng dwh (Chỉ cần viết select) 
    sql_transform_stg_to_dwh: str | None = Field(default=None)
    # Cấu trúc bảng dwh
    dwh_table_schema: TableSchema | None = Field(default={})

class ListTableMetaData(BaseModel):
    list: List[TableMetaData]
