import os


def set_daily_folder(path, report_name, *args):
    YEAR_NUM, MONTH_NUM, DAY_NUM = args
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name)):
        if '/' not in report_name:
            os.makedirs(os.path.join(path, report_name), exist_ok=True)
        else:
            parent, child = report_name.split('/')
            if not os.path.exists(os.path.join(path, parent)):
                os.makedirs(os.path.join(path, parent), exist_ok=True)
            if not os.path.exists(os.path.join(path, parent, child)):
                os.makedirs(os.path.join(path, parent, child), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM, MONTH_NUM)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM, MONTH_NUM), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM, MONTH_NUM, DAY_NUM)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM, MONTH_NUM, DAY_NUM), exist_ok=True)
    saved_folder = os.path.join(path, report_name, YEAR_NUM, MONTH_NUM, DAY_NUM)
    return saved_folder


def set_weekyl_folder(path, report_name, *args):
    YEAR_NUM, week_folder = args
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name)):
        os.makedirs(os.path.join(path, report_name), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM, week_folder)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM, week_folder), exist_ok=True)
    saved_folder = os.path.join(path, report_name, YEAR_NUM, week_folder)
    return saved_folder


def set_monthly_folder(path, report_name, *args):
    YEAR_NUM, MONTH_NUM, DAY_NUM = args
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name)):
        if '/' not in report_name:
            os.makedirs(os.path.join(path, report_name), exist_ok=True)
        else:
            parent, child = report_name.split('/')
            if not os.path.exists(os.path.join(path, parent)):
                os.makedirs(os.path.join(path, parent), exist_ok=True)
            if not os.path.exists(os.path.join(path, parent, child)):
                os.makedirs(os.path.join(path, parent, child), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM), exist_ok=True)
    if not os.path.exists(os.path.join(path, report_name, YEAR_NUM, MONTH_NUM)):
        os.makedirs(os.path.join(path, report_name, YEAR_NUM, MONTH_NUM), exist_ok=True)
    saved_folder = os.path.join(path, report_name, YEAR_NUM, MONTH_NUM)
    return saved_folder


def set_realtime_folder(saved_folder, report_name):
    saved_folder = os.path.join(saved_folder, report_name)
    if not os.path.exists(saved_folder):
        os.makedirs(saved_folder, exist_ok=True)
    return saved_folder


def set_saved_folder_and_file_name(overwrite, freq, saved_folder, file_name, report_name, *date_args):
    year, month, day = date_args
    if freq == 'daily':
        saved_folder = set_daily_folder(saved_folder, report_name, year, month, day)
    elif freq == 'monthly':
        saved_folder = set_monthly_folder(saved_folder, report_name, year, month, day)
    elif freq == 'quarterly':
        quarter = 'Q' + str((int(month)-1)//3 + 1)
        saved_folder = set_monthly_folder(saved_folder, report_name, year, quarter, day)
    elif freq == 'realtime':
        saved_folder = set_realtime_folder(saved_folder, report_name)
    else:
        saved_folder = os.path.join(saved_folder, report_name)
        if not os.path.exists(saved_folder):
            os.makedirs(saved_folder, exist_ok=True)
    # if overwrite == 'folder':
        # file_name = f'{file_name}'
    if overwrite and file_name:
        file_name = f'{file_name}.xlsx'
    elif file_name:
        file_name = f'{file_name}_{year}{month}{day}.xlsx'
    return saved_folder, file_name
