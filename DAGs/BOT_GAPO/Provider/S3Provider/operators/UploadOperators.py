from airflow.models import <PERSON><PERSON>perator
from airflow.utils.context import Context
from DAGs.utils import DAGMonitor
from Provider.S3Provider.hooks.UploadHook import UpLoadS3Hook
from Provider.utils import set_saved_folder_and_file_name
from typing import Any
import json
import requests
from airflow.hooks.base import BaseHook

class UploadS3Operator(BaseOperator):
    def __init__(self, presign_base_url_conn_id: str, max_workers: int, saved_folder: str, overwrite: bool, freq: str, file_name: str, report_name: str, **kwargs):
        super().__init__(**kwargs)
        self.max_workers = max_workers
        self.saved_folder = saved_folder
        self.overwrite = overwrite
        self.freq = freq
        self.file_name = file_name
        self.report_name = report_name
        self.presign_base_url = BaseHook.get_connection(presign_base_url_conn_id).host
        
    def __postToPresignUrl(self, **context: Context):
        """POST TO PRESIGN TO GET URL PUT TO S3
        """
        file_path_end_point = context['ti'].xcom_pull(key='excel_file_path_'+context['dag'].dag_id)
        
        with open(file_path_end_point, 'r') as file:
            file_path_end_point = json.load(file)

        presign_headers = {
        'Content-Type': 'application/json'
        }
        presign_url_result = {}
        ### Tách thành 100 phần từng lần lấy presign URL
        payload_div = len(file_path_end_point)//100 + 1 
        self.log.info(f'payload_div = {str(payload_div)}')
        for i in range(payload_div):
            presign_payload = json.dumps(file_path_end_point[i*100:100*(i+1)])
            self.log.info(f'Signning to get Presign URL part {str(i+1)}/{str(payload_div)} of payload...')
            presign_res = requests.request('POST', url=self.presign_base_url, headers=presign_headers, data=presign_payload)
            if presign_res.status_code != 200:
                timeout = 1
                while presign_res.status_code != 200 and timeout < 5:
                    self.log.info(f'Attempt {str(timeout)} retry gettings Presign URL part {str(i+1)}/{str(payload_div)} of payload...')
                    presign_res = requests.request('POST', url=self.presign_base_url, headers=presign_headers, data=presign_payload)
                    timeout+=1
            presign_url_result.update(dict(zip(file_path_end_point[i*100:100*(i+1)], presign_res.json())))
            self.log.info(f'Done gettings Presign URL part {str(i+1)}/{str(payload_div)} of payload...')
        # context['ti'].xcom_push(key='s3hook_presign_url_result', value=dict(zip(file_path_end_point, presign_res.json())))
        return presign_url_result
    
    def execute(self, context: Context) -> Any:
        presign_url_result = self.__postToPresignUrl(**context)
        self.log.info('============================= presign_url_result =============================')
        
        self.log.info(str(presign_url_result))
        
        self.parameters = DAGMonitor.set_params_operator(**context)
        
        self.log.info(self.parameters)
        execution_date = self.parameters['YESTERDAY']
    
        year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
        saved_folder = set_saved_folder_and_file_name(self.overwrite, self.freq,
                                                                 self.saved_folder,
                                                                 self.file_name, self.report_name, year,
                                                                 month, day)[0]
        hook = UpLoadS3Hook(presign_url=self.presign_base_url, saved_folder=saved_folder)
        
        hook.run(presign_url_result=presign_url_result, max_workers=self.max_workers, **context)