from airflow.models import <PERSON>Operator
from airflow.providers.oracle.hooks.oracle import Oracle<PERSON><PERSON>
from DAGs.utils import DAG<PERSON><PERSON>tor
from Provider.OracleProvider.utils import TableSchema
from airflow.utils.context import Context
from typing import Any
from collections import OrderedDict
from Provider.OracleProvider.utils import INSERT_STRATEGY

class STG2DWHOperators(BaseOperator):
    def __init__(self, oracle_conn_id: str, source_table_schema: TableSchema, target_table_schema: TableSchema, select_sql_command: str,insert_strategy:INSERT_STRATEGY, **kwargs) -> None:
        super().__init__(**kwargs)
        self.oracle_hook = OracleHook(oracle_conn_id)
        self.source_table_schema = source_table_schema
        self.target_table_schema = target_table_schema
        self.select_sql_command = select_sql_command
        self.insert_strategy = insert_strategy

    def _create_insert_statements(self) -> str:
        column_map = {}
        sql = self.select_sql_command.upper()
        for column_name in self.target_table_schema.columns_datatype:
            column_map.update({sql.index(column_name): column_name})
        column_map = OrderedDict(sorted(column_map.items()))
        if self.insert_strategy == INSERT_STRATEGY.TRUNCATE:
            target_columns = ', '.join(column_map.values())
            query = f"""INSERT INTO {self.target_table_schema.table_name} ({target_columns})
            {self.select_sql_command}
            """  
        elif self.insert_strategy == INSERT_STRATEGY.MERGE and self.target_table_schema.merge_key:
            merge_condition = 'target_table.'+' AND target_table.'.join([' = source_table.'.join(i) for i in tuple(zip(self.target_table_schema.merge_key,self.target_table_schema.merge_key))])
            col_to_update = set(column_map.values()).difference(set(self.target_table_schema.merge_key))
            pair_col_to_update = tuple(zip(col_to_update,col_to_update))
            col_to_update_sql = 'target_table.'+', target_table.'.join([' = source_table.'.join(i) for i in pair_col_to_update])
            col_to_update_condition = 'target_table.'+' OR target_table.'.join([' <> source_table.'.join(i) for i in pair_col_to_update]) +  '\n' + ' OR ' \
                + '(target_table.'+' OR (target_table.'.join([i + ' is not null)' for i in [' is null AND source_table.'.join(i) for i in pair_col_to_update]]) + '\n' + ' OR ' \
                + '(target_table.'+' OR (target_table.'.join([i + ' is null)' for i in [' is not null AND source_table.'.join(i) for i in pair_col_to_update]])
            insert_sql = 'target_table.' + ', target_table.'.join(column_map.values())
            insert_values_sql = 'source_table.' + ', source_table.'.join(column_map.values())
            query = f"""
            MERGE INTO {self.target_table_schema.table_name} target_table 
            USING ({self.select_sql_command}) source_table
            ON ({merge_condition}) 
            WHEN MATCHED THEN 
            UPDATE SET {col_to_update_sql} 
            WHERE {col_to_update_condition}
            WHEN NOT MATCHED THEN
            INSERT({insert_sql})
            VALUES({insert_values_sql})
            """
        elif self.insert_strategy == INSERT_STRATEGY.DELETE_INSERT and self.target_table_schema.merge_key:
            target_columns = ', '.join(column_map.values())
            insert_cmd = f"""INSERT INTO {self.target_table_schema.table_name} ({target_columns})
            {self.select_sql_command}
            """
            delete_cmd =  "DELETE %s WHERE (%s) IN (SELECT %s FROM (%s) STG_TABLE)" % (self.target_table_schema.table_name, ', '.join(self.target_table_schema.merge_key), ', '.join(self.target_table_schema.merge_key) , self.select_sql_command)
            query = {'insert_cmd': insert_cmd, 'delete_cmd': delete_cmd}
        else:
            raise RuntimeError(f'No Strategy For {self.insert_strategy}')
        self.log.info(f'Query Used To Transform Table {self.source_table_schema.table_name}')
        self.log.info(str(query))
        return query
    
    def execute(self, context: Context) -> Any:
        # query = self._create_insert_statements()
        query = self._create_insert_statements()
        self.parameters = DAGMonitor.set_params_operator(**context)
        self.log.info(f'Begin To Transform Table {self.source_table_schema.table_name} To Table {self.target_table_schema.table_name}')
        if self.insert_strategy == INSERT_STRATEGY.DELETE_INSERT: 
            filtered_parameters = DAGMonitor.sql_get_filtered_params(self.parameters, query.get('insert_cmd'))
            self.log.info(f'filtered_parameters: {str(filtered_parameters)}')
            conn = self.oracle_hook.get_conn()
            cursor = conn.cursor()
            is_done = False
            excepttion = None
            try:
                self.log.info(query.get('delete_cmd'))
                cursor.execute(query.get('delete_cmd'))
                self.log.info(query.get('insert_cmd'))
                cursor.execute(query.get('insert_cmd'), filtered_parameters)
                conn.commit()
                is_done = True
            except Exception as e:
                excepttion = e
                conn.rollback()
            finally:
                cursor.close()
                conn.close()
            if not is_done:
                raise RuntimeError(excepttion)
            del is_done
            del excepttion
        else:
            filtered_parameters = DAGMonitor.sql_get_filtered_params(self.parameters, query)
            self.log.info(f'filtered_parameters: {str(filtered_parameters)}')
            self.oracle_hook.run(query, autocommit=True, parameters=filtered_parameters)
        self.log.info('Transformed')