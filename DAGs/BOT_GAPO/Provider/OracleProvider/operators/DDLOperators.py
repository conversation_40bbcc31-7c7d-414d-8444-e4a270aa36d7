from airflow.models import BaseOperator
from Provider.OracleProvider.hooks.DDLHooks import DDLHook
from typing import Dict, Any
from Provider.OracleProvider.utils import TableSchema, INSERT_STRATEGY


class DDLOperator(BaseOperator):
    def __init__(self,
                 table_schema: TableSchema,
                 oracle_conn_id: str,
                 insert_strategy: INSERT_STRATEGY,
                 **kwargs):
        super().__init__(**kwargs)
        self.ddl_hook = DDLHook(oracle_conn_id=oracle_conn_id)
        self.table_schema = table_schema
        self.insert_strategy = insert_strategy
    
    def execute(self, context: Dict[str, Any]) -> Any:
        if self.insert_strategy == INSERT_STRATEGY.CREATE_AND_TRUNCATE:
            self.ddl_hook.create_table(table_name=self.table_schema.table_name, column_datatype=self.table_schema.columns_datatype)
            self.ddl_hook.truncate_table(table_name=self.table_schema.table_name)
        else:
            self.ddl_hook.create_table(table_name=self.table_schema.table_name,
                                       column_datatype=self.table_schema.columns_datatype)

