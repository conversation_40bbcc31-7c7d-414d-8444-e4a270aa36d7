import os
import pandas as pd
from airflow.providers.oracle.hooks.oracle import <PERSON><PERSON><PERSON>
from airflow.models import BaseOperator
from datetime import datetime, timedelta
from typing import Any, Dict, List, Tuple
from Provider.SharePointProvider.xlsx_to_dwh_utils.utils import TableMetaData
import re
import polars as pl
from polars.exceptions import ComputeError

class XLSX2DWHOperator(BaseOperator):

    def __init__(
            self,
            table_metadata: TableMetaData,
            oracle_conn_id: str | None = None,
            local_file_path: str | List[str] | Tuple[str] | None = None,
            **kwargs: Any,
    ) -> None:
        super().__init__(**kwargs)
        self.table_metadata = table_metadata
        self.oracle_conn_id = oracle_conn_id
        self.oracle_hook = OracleHook(oracle_conn_id=oracle_conn_id) if oracle_conn_id else None
        self.local_file_path = local_file_path if not isinstance(local_file_path, str) else (local_file_path,)

    def _insert_data_to_stg(self, file_path: str, process_date: datetime) -> None:
        """datatype_mapping: 'str', 'int', 'float', 'datetime64'
        """
        self.log.info(f'Read And Ingest Sheet {self.table_metadata.xlsx_sheet_name}')
        if file_path.endswith('.csv') and re.findall('\*', os.path.basename(file_path)):
            if self.table_metadata.xlsx_read_options:
                if self.table_metadata.xlsx_read_options.skiprows:
                    skip_rows = self.table_metadata.xlsx_read_options.skiprows
            else:
                skip_rows = 0
            try:
                df = pl.read_csv(file_path, infer_schema_length=0, skip_rows=skip_rows, has_header=True, truncate_ragged_lines=True).to_pandas()
            except pl.exceptions.ComputeError as e:
                if 'no matching files found in' in str(e):
                    df = pd.DataFrame()
                else:
                    raise pl.exceptions.ComputeError(e)
        elif file_path.endswith('.csv') and not re.findall('\*', os.path.basename(file_path)):
            df = pd.read_csv(file_path, dtype=str)
        elif file_path.endswith('.xlsx'):
            sheet_name = 0 if not self.table_metadata.xlsx_sheet_name else self.table_metadata.xlsx_sheet_name
            df = pd.read_excel(file_path, sheet_name, dtype=str)
            if self.table_metadata.xlsx_read_options:
                if self.table_metadata.xlsx_read_options.header:
                    df.columns = df.iloc[self.table_metadata.xlsx_read_options.header-1].values
                if self.table_metadata.xlsx_read_options.skiprows:
                    df.drop(index=[i-1 for i in self.table_metadata.xlsx_read_options.skiprows], inplace=True)  
        if not df.empty:
            self.log.info(f'Total record in df: {str(len(df))}')     
            df.columns = [str(i).strip() for i in df.columns]
            df = df.rename(columns=self.table_metadata.rename_columns)
            df = df[self.table_metadata.list_columns_to_ingest_to_stg]
            df = df.astype(self.table_metadata.datatype_mapping)
            df[df.select_dtypes("object").columns] = df.select_dtypes("object").fillna("nan")
            df.drop_duplicates(inplace=True)
            self.log.info(f'Total record in df after clean: {str(len(df))}')  
            rows = df.itertuples(index=False)
            self.oracle_hook.bulk_insert_rows(table=self.table_metadata.stg_table_schema.table_name, rows=rows, target_fields=self.table_metadata.list_columns_to_ingest_to_stg, commit_every=1000)
            self.log.info(f'Ingested Sheet {self.table_metadata.xlsx_sheet_name}')

    def execute(self, context: Dict[str, Any]) -> Any:
        self.log.info("Get List Downloaded Files")
        if self.local_file_path:
            local_file_path = self.local_file_path
        else:
            xcom_key = context['dag_run'].dag_id + '_' + 'downloaded_file_list'
            local_file_path = context['ti'].xcom_pull(key=xcom_key)
        process_date = context['dag_run'].logical_date + timedelta(1)
        if self.table_metadata and self.oracle_hook:
            self.log.info('Begin Ingest Data To DWH')
            for file_path in local_file_path:
                self._insert_data_to_stg(file_path=file_path, process_date=process_date)
        else:
            self.log.info('Do Nothing!')