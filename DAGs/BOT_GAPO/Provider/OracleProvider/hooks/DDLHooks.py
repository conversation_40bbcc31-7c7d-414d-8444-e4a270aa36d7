from typing import Any, Dict
from airflow.providers.oracle.hooks.oracle import OracleHook
from wrapt_timeout_decorator import timeout

class DDLHook(OracleHook):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    @timeout(120)
    def create_table(self, table_name: str, column_datatype: Dict[str, Any]) -> None:
        # oracle_datamapping = {
        #                         'str': 'VARCHAR2(4000)', # Max length VARCHAR2
        #                         'float': 'NUMBER',
        #                         'int': 'NUMBER',
        #                         'datetime64': 'TIMESTAMP(0)'
        #                       }
        query = f"""CREATE TABLE {table_name} (\n"""
        for column, datatype in column_datatype.items():
            # query += f'\t{column} {oracle_datamapping.get(datatype)},\n'
            query += f'\t{column} {datatype},\n'
        query=query[:-2]
        query += '\n\t)'
        try:
            self.log.info(f'Start Creating Table {table_name}')
            self.log.info(query)
            self.run(query, autocommit=True)
        except Exception as e:
            self.log.info('==================SOME THING WRONG IN HERE ==========================')
            self.log.info(e)

    @timeout(120)
    def truncate_table(self, table_name: str) -> None:
        self.log.info(f'Start Truncate Table {table_name}')
        self.run(f'TRUNCATE TABLE {table_name}', autocommit=True)
        self.log.info(f'Truncated Table {table_name}')