from typing import Dict, List
from enum import Enum
from pydantic import BaseModel, Field

class INSERT_STRATEGY(Enum):
    TRUNCATE = 'truncate'
    CREATE = 'create'
    CREATE_AND_TRUNCATE = 'create and truncate'
    CREATE_AND_MERGE = 'create and merge'
    MERGE = 'merge'
    DELETE_INSERT = 'delete and insert'

class TableSchema(BaseModel):
    # Tên bảng
    table_name:str
    # Tên cột tương ứng với kiểu dữ liệu
    columns_datatype: Dict[str, str]
    # Key dùng để merge
    merge_key: List[str] = Field(default=[])