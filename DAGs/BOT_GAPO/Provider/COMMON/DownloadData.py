from __future__ import annotations
import datetime
import logging
from airflow.models import Base<PERSON>perator
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.providers.amazon.aws.hooks.redshift_sql import RedshiftSQLHook
from Provider.utils import *
from typing import TYPE_CHECKING, Any
from pyexcelerate import Workbook
if TYPE_CHECKING:
    from airflow.utils.context import Context

class DownloadData(BaseOperator):
    def __init__(
            self,
            conn_id: str,
            list_sql: str,
            saved_folder: str,
            file_name: str,
            report_name: str,
            overwrite: bool,
            freq: Any,
            parameters: dict | None = None,
            **kwargs,
    ) -> None:
        super().__init__(**kwargs)
        self.conn_id = conn_id
        self.list_sql = list_sql
        self.saved_folder = saved_folder  # saved folder path
        self.file_name = file_name  # saved file name
        self.report_name = report_name  # saved folder name
        self.overwrite = overwrite
        self.freq = freq
        self.parameters = parameters

    def execute(self, context: Context) -> None:
        ti = context['task_instance']
        self.parameters = ti.xcom_pull(key=self.file_name)
        logging.info(f'param: {self.parameters}')
        if self.conn_id == 'f88_redshift':
            hook = RedshiftSQLHook(redshift_conn_id=self.conn_id)
        else:
            hook = OracleHook(oracle_conn_id=self.conn_id)
        list_data = []
        for sql in self.list_sql:
            data = hook.get_pandas_df(sql=sql, parameters=self.parameters)
            data = [data.columns] + data.values.tolist()
            list_data.append(data)
        execution_date = (context['dag_run'].logical_date - datetime.timedelta(1)).strftime('%Y%m%d')
        year, month, day = execution_date[0:4], execution_date[4:6], execution_date[6:]
        saved_folder, file_name = set_saved_folder_and_file_name(self.overwrite, self.freq,
                                                                 self.saved_folder,
                                                                 self.file_name, self.report_name, year,
                                                                 month, day)
        wb = Workbook()
        for idx, dt in enumerate(list_data):
            wb.new_sheet(f"sheet_{idx + 1}", data=dt)
        wb.save(os.path.join(saved_folder, file_name))


