from typing import Any, Dict, Optional
import pandas as pd
import gspread
from airflow.hooks.base import BaseHook
from Provider.utils import *


class GoogleSheetHook(BaseHook):

    def __init__(
            self,
            googlesheet_conn_id: str
    ) -> None:
        super().__init__()
        self.googlesheet_conn_id = googlesheet_conn_id

    def get_conn(self, headers: Optional[Dict[Any, Any]] = None) -> Any:
        conn = self.get_connection(self.googlesheet_conn_id)
        file_path = conn.host
        session = gspread.service_account(filename=os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)),
                                                                             file_path)))
        self.log.info('Connect to GoogleSheet Sucessfully')
        return session

    def get_sheet_data_to_df(
            self,
            spreadsheet_key,
            sheet_name
    ) -> Any:
        session = self.get_conn()
        sheet = session.open_by_key(spreadsheet_key)
        data = sheet.worksheet(sheet_name).get_all_values()
        return pd.DataFrame(data=data[1:], columns=data[0])
