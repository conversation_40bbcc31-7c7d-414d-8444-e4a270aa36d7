from airflow.models.connection import Connection
import pendulum
from datetime import timed<PERSON><PERSON>
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from DAGs.BOT_GAPO.utils import send_msg
from airflow.models import Variable
import pandas as pd
import logging
from typing import Dict, Any
import re

def get_df_markdown(connection_id: str, query: str, parameters: str = {}) -> str:
    hook = Connection.get_connection_from_secrets(conn_id=connection_id).get_hook()
    parameter_names = re.findall(r":(\w+)", query)
    filtered_parameters = {param: parameters[param.upper()] for param in parameter_names if param.upper() in parameters}
    for param, value in filtered_parameters.items():
        query = query.replace(f':{param}', f"'{value}'")
    df = hook.get_pandas_df(query)
    if not df.empty:
        total_df = pd.DataFrame([df.sum(numeric_only=True, axis=0).to_dict()])
        df = pd.concat([df, total_df],ignore_index=True)
        df[df.columns.difference(total_df.columns)] = df[df.columns.difference(total_df.columns)].fillna('Total')
        return df.to_markdown(index=False)
    return """|  NO DATA  |
|:---------:|"""
    
def get_report_and_send_message(connection_id: str, report_metadata: Dict[str, Any], template_message: str):
    oracle_conn_id='oracle_f88_dwh'
    hook=Connection.get_connection_from_secrets(conn_id=oracle_conn_id).get_hook()
    for report_type in report_metadata.keys():
        if report_metadata[report_type].get('full'):
            message = ''
            for report_name, query in report_metadata[report_type]['full']["query"].items():
                dataframe_markdown = get_df_markdown(connection_id=connection_id, query=query)
                date_report = pendulum.now(tz='Asia/Ho_Chi_Minh')
                message += '<br>\n' + template_message.format(date=date_report.strftime('%Y/%m/%d'), dataframe_markdown=dataframe_markdown, timestamp=date_report, report_name=report_name, report_type=report_type)
            for gapo_receiver_id in report_metadata[report_type]['full']["receiver"]:
                send_msg(message=message, config=config, receiver=gapo_receiver_id, receiver_type='partner_id')
                logging.info(f'send type: full {report_type} {str(gapo_receiver_id)}')
        
        if report_metadata[report_type].get('team'):
            for team in report_metadata[report_type].get('team').get("receiver"):
                message = ''
                team_members = [i[0] for i in hook.get_records("SELECT GAPO_ID FROM HANHDTM.NHAN_SU_TLS WHERE TEAM_LEAD = :TEAM", parameters={'TEAM':team})]
                logging.info(team_members)
                for report_name, query in report_metadata[report_type]['team']["query"].items():
                    dataframe_markdown = get_df_markdown(connection_id=connection_id, query=query, parameters={'TEAM':team})
                    date_report = pendulum.now(tz='Asia/Ho_Chi_Minh')
                    message += '<br>\n' + template_message.format(date=date_report.strftime('%Y/%m/%d'), dataframe_markdown=dataframe_markdown, timestamp=date_report, report_name=report_name, report_type=report_type)
                for gapo_receiver_id in team_members:
                    send_msg(message=message, config=config, receiver=gapo_receiver_id, receiver_type='partner_id')
                    logging.info(f'send type: team {report_type} {str(gapo_receiver_id)}')
                    
        if report_metadata[report_type].get('person'):
            for gapo_receiver_id in report_metadata[report_type]['person']["receiver"]:
                message = ''
                person_name = hook.get_first("SELECT MA_NV FROM HANHDTM.NHAN_SU_TLS WHERE GAPO_ID = to_number(:PERSON)", parameters={'PERSON':gapo_receiver_id})[0]
                for report_name, query in report_metadata[report_type]['person']["query"].items():
                    dataframe_markdown = get_df_markdown(connection_id=connection_id, query=query, parameters={'PERSON':person_name})
                    date_report = pendulum.now(tz='Asia/Ho_Chi_Minh')
                    message += '<br>\n' + template_message.format(date=date_report.strftime('%Y/%m/%d'), dataframe_markdown=dataframe_markdown, timestamp=date_report, report_name=report_name, report_type=report_type)
                send_msg(message=message, config=config, receiver=gapo_receiver_id, receiver_type='partner_id')
                logging.info(f'send type: person {report_type} {str(gapo_receiver_id)}')

tls_template_message = """
**Telesale - ngày {date}** 
**Dữ liệu tổng hợp: {report_type}**
**{report_name}:**

{dataframe_markdown}

Dữ liệu được lấy vào lúc: {timestamp}
<br>
"""

tls_report_metadata = Variable.get("tls_report_metadata_realtime_bot_bi", deserialize_json=True)

ptdt_template_message = """
**PTĐT - ngày {date}** 
**Dữ liệu tổng hợp: {report_type}**
**{report_name}:**

{dataframe_markdown}

Dữ liệu được lấy vào lúc: {timestamp}
<br>
"""

ptdt_report_metadata = Variable.get("ptdt_report_metadata_realtime_bot_bi", deserialize_json=True)

config = Variable.get("GAPO_BOT_BI", deserialize_json=True)
dag_id = 'REALTIME_BOT_BI_TLS_PTDT'

with DAG(dag_id=dag_id,
         start_date=pendulum.datetime(year=2024, month=5, day=8, tz='Asia/Ho_Chi_Minh'),
         schedule='0 11,15,20 * * *',
         catchup=False,
         max_active_runs=1,
         dagrun_timeout=timedelta(minutes=20)) as dag:
    start = EmptyOperator(task_id='Start')
    
    tls_get_report_and_send_message_task = PythonOperator(task_id='tls_get_report_and_send_message',
                               python_callable=get_report_and_send_message,
                               op_kwargs={'connection_id': 'mysql_pol_prod', 'report_metadata':tls_report_metadata, 'template_message':tls_template_message},
                               provide_context=True)
    ptdt_get_report_and_send_message_task = PythonOperator(task_id='ptdt_get_report_and_send_message_task',
                               python_callable=get_report_and_send_message,
                               op_kwargs={'connection_id': 'mysql_pol_prod', 'report_metadata':ptdt_report_metadata, 'template_message':ptdt_template_message},
                               provide_context=True)
    end = EmptyOperator(task_id='End')

start >> tls_get_report_and_send_message_task >> end
start >> ptdt_get_report_and_send_message_task >> end

if __name__ == '__main__':
    import json
    print(json.dumps(tls_report_metadata))
    print('=======================================================')
    print(json.dumps(ptdt_report_metadata))
    # get_report_and_send_message(report_metadata=tls_report_metadata, template_message=tls_template_message)