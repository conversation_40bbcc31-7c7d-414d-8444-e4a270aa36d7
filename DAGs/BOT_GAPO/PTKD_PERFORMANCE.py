import os
import datetime
import logging
import pandas as pd
from airflow import DAG
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.hooks.oracle import Oracle<PERSON><PERSON>
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from DAGs.BOT_GAPO.utils import send_msg, set_kwargs
import pytz
parent_dir = os.path.dirname(os.path.abspath(__file__))
file_name = 'PTKD-PERFORMANCE'
dag_name = 'BOT_GAPO_PTKD_PERFORMANCE'
oracle_conn_id = 'oracle_f88_dwh'
description = 'BOT GAPO cho PTKD'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None#'50 11 * * *'
# schedule = None
tags = ['ptkd', 'bot-gapo']
sql, saved_folder = set_kwargs(parent_dir, file_name)
def get_sql():
    sql_ls = sql.split('--split--')
    return sql_ls

def set_params(**kwargs):
    time = kwargs['dag_run'].logical_date - datetime.timedelta(1)
    DATE_WID = time.strftime("%Y%m%d")
    YEAR_NUM, MONTH_NUM = DATE_WID[:4], DATE_WID[4:6]
    params = {}
    params.update({'DATE_WID': DATE_WID, 'YEAR_NUM': YEAR_NUM, 'MONTH_NUM': MONTH_NUM})
    kwargs['ti'].xcom_push(key=file_name, value=params)
    logging.info('Set Parameters Done!')
def get_data(parameters):
    sql_get_data = get_sql()
    ora_hook = OracleHook(oracle_conn_id=oracle_conn_id)
    logging.info(f"sql: {sql_get_data[1]}")
    df_ora = ora_hook.get_pandas_df(sql=sql_get_data[1], parameters= parameters)
    logging.info(f"data: {df_ora}")
    return df_ora
def process_history_performance(**kwargs):
    ti = kwargs['ti']
    params = ti.xcom_pull(key=file_name, task_ids='Set_Parameters_for_SQL')
    sql_delete = f"Delete from F88DWH.W_EARLY_WARNING_HISTORY where DATE_WID = {params['DATE_WID']}"
    logging.info('Executing: %s', sql_delete)
    hook = OracleHook(oracle_conn_id=oracle_conn_id)
    hook.run(sql_delete, autocommit=True)
    logging.info(f'delete W_EARLY_WARNING_HISTORY Done!')
    sql_insert = get_sql()
    sql_insert[0] = sql_insert[0].replace(':DATE_WID', params['DATE_WID'])
    logging.info(f'sql_insert: {sql_insert[0]}')
    hook.run(sql_insert[0], autocommit=True)
    logging.info(f'insert W_EARLY_WARNING_HISTORY Done!')
def send_messages(data,date_wid):
    f = open(parent_dir + "/messages" + "/PTKD-PERFORMANCE.txt", "r")
    message = f.read()
    config = Variable.get("GAPO_BOT_CANH_BAO", deserialize_json=True)
    # arr = [1146746099, 1928495471, 257492043 , 208754 , 918161306 , 1869506325 , 839306870 , 255726, 1092390262]
    arr = [188543406]
    for ar in arr:
        for i in data.index:
            message_tmp = message
            message_tmp = message_tmp.replace('{name}', data['EMPLOYEE_NM'][i])
            message_tmp = message_tmp.replace('{PGD_name}', data['SHOP_NM'][i])
            message_tmp = message_tmp.replace('{ngày}', str(date_wid))
            message_tmp = message_tmp.replace('{listKPI}', data['STATUS'][i])
            logging.info(f"message:{message_tmp}")
            msg = {
                    'message': message_tmp,
                    'config': config,
                    'receiver': ar,
                    # 'receiver': f"{data['GAPO_ID'][i]}",
                    'receiver_type': 'partner_id',
                }
            send_msg(**msg)

def create_and_transform_data(**kwargs) -> None:
    ti = kwargs['ti']
    params = ti.xcom_pull(key=file_name, task_ids='Set_Parameters_for_SQL')
    df_ora = get_data(params)
    send_messages(df_ora, params['DATE_WID'])
    



with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id='Start')
    end = EmptyOperator(task_id='End')
    set_params = PythonOperator(task_id='Set_Parameters_for_SQL',
                                python_callable=set_params)
    # load_file = TriggerDagRunOperator(task_id=f"loadfile", trigger_dag_id="XLN_DAILY_SHAREPOINT_PTKD_DATA_TO_DWH", wait_for_completion=True)
    process_history = PythonOperator(task_id='process_history',
                                python_callable=process_history_performance)
    create_and_send_msg = PythonOperator(task_id='Create_and_Send_KPI_to_PGD', python_callable= create_and_transform_data)
    # start >> set_params >> load_file >> process_history >> create_and_send_msg >> end
    start >> set_params  >> process_history >> create_and_send_msg >> end
