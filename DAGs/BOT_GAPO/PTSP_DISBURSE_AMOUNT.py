import os
import datetime
import logging
import pandas as pd
from airflow import DAG
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.mysql.hooks.mysql import MySqlHook
from airflow.providers.oracle.hooks.oracle import OracleHook
from DAGs.utils import send_message_bot_bi
import pytz

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'BOT_GAPO_PTSP_DISBURSE_AMOUNT'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None #'0 6-22/4 * * *'
description = 'BOT GAPO cho giải ngân'
tags = ['ptsp', 'bot-gapo']
los_conn_id = 'mysql_los'
mifos_conn_id = 'mysql_mifos'


def create_msg(day_acc, month_acc, duno, money_receive):
    day_acc = day_acc[['LOANPACKAGENAME', 'SOURCE', 'DISBURSEAMOUNT']]
    total_source_day = day_acc.groupby(['SOURCE']).DISBURSEAMOUNT.sum().reset_index()
    total_day = day_acc.DISBURSEAMOUNT.sum()

    hanmuc_df = total_source_day.merge(duno, on=['SOURCE'], how='inner')
    hanmuc_df = hanmuc_df.merge(money_receive, on=['SOURCE'], how='inner')
    hanmuc_df.columns = ['Nguồn', 'GN trong ngày', 'Dư nợ đầu ngày', 'Tiền gốc nhận được trong ngày']
    hanmuc_df['Hạn mức gốc'] = hanmuc_df.apply(
        lambda row: 2115000000000 if row['Nguồn'] == 'F88' else 1955000000000, axis=1)
    hanmuc_df['Hạn mức thời điểm hiện tại'] = hanmuc_df.apply(lambda row:
                                                              int(row['Hạn mức gốc']) - int(row['GN trong ngày']) +
                                                              int(row['Tiền gốc nhận được trong ngày']) -
                                                              int(row['Dư nợ đầu ngày']), axis=1)
    hanmuc_df = hanmuc_df.pivot_table(
        values=['GN trong ngày', 'Dư nợ đầu ngày', 'Tiền gốc nhận được trong ngày', 'Hạn mức gốc',
                'Hạn mức thời điểm hiện tại'], columns=['Nguồn']).reset_index()
    hanmuc_df.columns = ['Chỉ tiêu', 'CIMB', 'F88']
    hanmuc_df['CIMB'] = hanmuc_df['CIMB'].apply(
        lambda x: '{:,.0f}'.format(x).rjust(40))
    hanmuc_df['F88'] = hanmuc_df['F88'].apply(
        lambda x: '{:,.0f}'.format(x).rjust(40))
    chitieu_max_length = hanmuc_df['Chỉ tiêu'].str.len().max()
    hanmuc_df['Chỉ tiêu'] = hanmuc_df['Chỉ tiêu'].apply(
        lambda x: x.ljust(chitieu_max_length - len(x) + 50))
    hanmuc_df.columns = [str(i).ljust(90) for i in hanmuc_df.columns]
    loan_package_max_length = day_acc['LOANPACKAGENAME'].str.len().max()
    day_acc['LOANPACKAGENAME'] = day_acc['LOANPACKAGENAME'].apply(
        lambda x: x.ljust(loan_package_max_length - len(x) + 50))
    day_acc['SOURCE'] = day_acc['SOURCE'].apply(lambda x: x.ljust(20))
    day_acc['DISBURSEAMOUNT'] = day_acc['DISBURSEAMOUNT'].apply(lambda x: '{:,.0f} VNĐ'.format(x))
    total_source_day['DISBURSEAMOUNT'] = total_source_day['DISBURSEAMOUNT'].apply(
        lambda x: '{:,.0f} VNĐ'.format(x).rjust(5))
    day_acc.columns = [str(i).ljust(50) for i in day_acc.columns.values]
    tz_VN = pytz.timezone('Asia/Ho_Chi_Minh')
    today = datetime.datetime.now(tz_VN).strftime('%d/%m/%Y %H:%M:%S')
    message1 = 'Số liệu đến thời điểm ' + today + ':\n' + "Giải ngân theo gói vay trong ngày: " + '\n' + day_acc.to_csv(
        sep='\t', index=False) + '\n' + 'Giải ngân theo nguồn: ' + '\n' + total_source_day.to_csv(sep='\t',
                                                                                                  index=False) + '\n' + 'Tổng giải ngân: ' + '{:,.0f}'.format(
        total_day) + ' VNĐ'

    month_acc = month_acc[['LOANPACKAGENAME', 'SOURCE', 'DISBURSEAMOUNT']]
    total_source_month = month_acc.groupby(['SOURCE']).DISBURSEAMOUNT.sum().reset_index()
    total_month = month_acc.DISBURSEAMOUNT.sum()
    loan_package_max_length = month_acc['LOANPACKAGENAME'].str.len().max()
    month_acc['LOANPACKAGENAME'] = month_acc['LOANPACKAGENAME'].apply(
        lambda x: x.ljust(loan_package_max_length - len(x) + 50))
    month_acc['SOURCE'] = month_acc['SOURCE'].apply(lambda x: x.ljust(20))
    month_acc['DISBURSEAMOUNT'] = month_acc['DISBURSEAMOUNT'].apply(lambda x: '{:,.0f} VNĐ'.format(x))
    total_source_month['DISBURSEAMOUNT'] = total_source_month['DISBURSEAMOUNT'].apply(
        lambda x: '{:,.0f} VNĐ'.format(x).rjust(5))
    month_acc.columns = [str(i).ljust(50) for i in month_acc.columns.values]
    message2 = 'Số liệu đến thời điểm ' + today + ':\n' + "Giải ngân theo gói vay trong tháng: " + '\n' + month_acc.to_csv(
        sep='\t', index=False) + '\n' + 'Giải ngân theo nguồn: ' + '\n' + total_source_month.to_csv(sep='\t',
                                                                                                    index=False) + '\n' + 'Tổng giải ngân: ' + '{:,.0f}'.format(
        total_month) + ' VNĐ'

    message3 = 'Số liệu đến thời điểm ' + today + ':\n' + hanmuc_df.to_csv(sep='\t', index=False)
    return [message1, message2, message3]


def get_data():
    sql = """SELECT
                p.id,
                PriceDebitAmount ,
                DAY(p.DisburseDate) DAY,
                MONTH(p.DisburseDate) MONTH,
                CASE
                    WHEN FundSource = '05' THEN 'CIMB'
                    ELSE 'F88'
                END SOURCE ,
                l.Name
            FROM
                Pawn p
            left JOIN LoanPackage l ON p.LoanPackageId = l.id
            where date_format(p.DisburseDate,'%Y%m') = date_format(CURRENT_DATE(),'%Y%m')
            AND DisburseDate IS NOT NULL
            and PawnStatusCode >= 4
	"""
    los_hook = MySqlHook(mysql_conn_id=los_conn_id)
    disburse_los = los_hook.get_pandas_df(sql=sql)
    disburse_los.columns = ['ID', 'DISBURSEAMOUNT', 'DAY', 'MONTH', 'SOURCE', 'LOANPACKAGENAME']
    logging.info('Query data from LOS successfully!')
    logging.info(f'LOS has {disburse_los.shape[0]}')

    money_receive = """
                    SELECT
                        CASE
                            WHEN mf.name = 'CIMB Fund' THEN 'CIMB'
                            ELSE 'F88'
                        END SOURCE,
                        sum(a.principal_portion_derived) RECEIVEMONEY
                    from m_loan_transaction a
                    left OUTER JOIN m_loan ml ON a.loan_id = ml.id
                    left OUTER JOIN m_fund mf ON ml.fund_id = mf.id
                    where 1 = 1
                        AND action_code IN('DONG_HD', 'TRA_BOT_GOC', 'DONG_HD_CD', 'DONG_HD_DH', 'TRA_CPV', 'TRA_CPV_CIMB', 'DONG_HD_CD_CIMB')
                        AND transaction_date = date_format(CURRENT_DATE(),'%Y%m%d')
                        AND a.is_reversed = 0
                        GROUP BY mf.name """
    mifos_hook = MySqlHook(mysql_conn_id=mifos_conn_id)
    money_receive_mifos = mifos_hook.get_pandas_df(sql=money_receive)
    money_receive_mifos.columns = ['SOURCE', 'RECEIVEMONEY']

    tz_VN = pytz.timezone('Asia/Ho_Chi_Minh')
    today = (datetime.datetime.now(tz_VN)).strftime('%Y-%m-%d')
    yesterday = (datetime.datetime.now(tz_VN) - datetime.timedelta(1)).strftime('%Y-%m-%d')
    sql_mifos = f"""SELECT
                        sum(t.principal_outstanding),
                        CASE
                            WHEN fund_source = 4 THEN 'F88'
                            ELSE 'CIMB'
                        END fund_name
                    FROM (
                        SELECT
                            mdoh.id,
                            principal_outstanding ,
                            fund_source ,
                            modifier_date ,
                            RANK() OVER(PARTITION BY mdoh.loan_id ORDER BY mdoh.modifier_date ASC) zn
                        from m_document_ods_history mdoh
                        where mdoh.modifier_date > '{today}'
                            AND is_bad_debt = 0
                            AND loan_status = 300
                            AND is_buy_debt = 0) t
                    Where zn = 1
                    GROUP BY fund_source 
                    """
    hook = MySqlHook(mysql_conn_id=mifos_conn_id)
    df_mifos = hook.get_pandas_df(sql=sql_mifos)
    df_mifos.columns = ['DUNO', 'SOURCE']
    return disburse_los, df_mifos, money_receive_mifos


def create_and_send_msg(ds, **kwargs):
    disburse_los, df_mifos, money_receive_mifos = get_data()
    tz_VN = pytz.timezone('Asia/Ho_Chi_Minh')
    today = datetime.datetime.now(tz_VN).strftime('%-m%d')
    day_acc = disburse_los.groupby(['LOANPACKAGENAME', 'SOURCE', 'MONTH', 'DAY']).DISBURSEAMOUNT.sum().reset_index()
    day_acc = day_acc[(day_acc['DAY'] == int(today[1:])) & (day_acc['MONTH'] == int(today[:1]))]
    day_acc = day_acc.sort_values(by=['SOURCE'])
    month_acc = disburse_los.groupby(['LOANPACKAGENAME', 'SOURCE', 'MONTH']).DISBURSEAMOUNT.sum().reset_index()
    month_acc = month_acc.sort_values(by=['SOURCE'])
    msg_detail = create_msg(day_acc, month_acc, df_mifos, money_receive_mifos)
    config = Variable.get("GAPO_BOT_BI", deserialize_json=True)
    for gapo_id in ['290205444', '917170657', '1707261663', '1377255913', '1806618204', '1069602735', '1172255964',
                    '1740783739', '917170657', '1402816635', '582665521', '1673433359453', '270389852', '629014035',
                    '1699499002', '136478', '1435317992','918161306','208754', '176489', '682002642']:

        for m in msg_detail:
            msg = {
                'message': m,
                'config': config,
                'receiver': f'{gapo_id}',
                'receiver_type': 'partner_id'
            }
            send_message_bot_bi(**msg)
            logging.info(f"Send message to {gapo_id}")


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id='Start')
    end = EmptyOperator(task_id='End')
    create_and_send_msg = PythonOperator(task_id='Create_and_Send_MSG_to_PTSP', python_callable=create_and_send_msg)
    start >> create_and_send_msg >> end
