import os
import datetime
import logging
import pandas as pd
from airflow import DAG
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.hooks.oracle import OracleHook
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from DAGs.utils import send_msg

first = datetime.datetime.now().replace(day=1)
last_day_of_month = first - datetime.timedelta(days=1)
weekday_map = {
    'thứ 2': 'Monday',
    'thứ 3': 'Tuesday',
    'thứ 4': 'Wednesday',
    'thứ 5': 'Thursday',
    'thứ 6': 'Friday',
    'thứ 7': 'Saturday',
    'chủ nhật': 'Sunday',
    'last_day_of_month': last_day_of_month.strftime('%Y%m%d')
}

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'BOT_GAPO_QTRR_INTERNAL_ADS'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '0 8 * * *'
description = 'BOT GAPO cho truyền thông nôi bộ cho QTRR'
tags = ['qtrr', 'bot-gapo']
department = 'Project - BOT GAPO Truyền thông'
report_name = ''
saved_folder = os.path.join(parent_dir, 'Downloaded', 'QTRR')
local_gapo_id_file = 'GAPO_ID.xlsx'
freq = ''
_type = 'BOT GAPO'


def create_msg(row: pd.Series):
    subject = row['Subject']
    content = row['Content']
    message = "Hello {name}\n" + subject + ':' + content + '\nChúc bạn một ngày làm việc vui vẻ!'
    return message


def get_gapo_id():
    hook = OracleHook(oracle_conn_id='oracle_f88_dwh')
    sql = """SELECT
                A.GAPO_ID,
                A.EMAIL,
                B.EMPLOYEE_NM
            FROM
                F88DWH.W_GAPO_ID A
            INNER JOIN (
                SELECT
                    EMAIL,
                    EMPLOYEE_NM
                FROM
                    F88DWH.W_EMPLOYEE_D wed
                WHERE
                    EMAIL LIKE '%@f88.co'
                    AND JOB_DATE_OUT IS NULL
                    AND CRN_ROW_IND = 1
                    AND (LOWER(POSITION_NM) LIKE '%trưởng phòng giao dịch%'
                        OR LOWER(POSITION_NM) LIKE '%chuyên viên kinh doanh%')) B ON A.EMAIL = B.EMAIL
            UNION
            SELECT
                A.GAPO_ID,
                A.EMAIL,
                B.EMPLOYEE_NM
            FROM
                F88DWH.W_GAPO_ID A
            INNER JOIN (
                SELECT
                    EMAIL,
                    EMPLOYEE_NM
                FROM
                    F88DWH.W_EMPLOYEE_D wed
                WHERE
                    JOB_DATE_OUT IS NULL
                    AND CRN_ROW_IND = 1
                    AND LOWER(DEPARTMENT_NM) LIKE '%quản trị rủi ro%') B ON A.EMAIL = B.EMAIL"""
    df = hook.get_pandas_df(sql)
    logging.info('Query data from DWH successfully!')
    logging.info(f'DF has {df.shape[0]} records')
    df.to_excel(os.path.join(saved_folder, local_gapo_id_file), index=False)


def create_and_send_msg(ds, **kwargs):
    # Read downloaded file to df
    gapo_id_path = os.path.join(saved_folder, local_gapo_id_file)
    ads_path = ''
    downloaded_file = next(os.walk(saved_folder))[2]
    if len(downloaded_file) == 2:
        for file in downloaded_file:
            if file != local_gapo_id_file:
                ads_path = os.path.join(saved_folder, file)
    gapo_id_df = pd.read_excel(gapo_id_path)
    if ads_path:
        ads_df = pd.read_excel(ads_path)
        config = Variable.get("GAPO_BOT_QTRR", deserialize_json=True)
        for index, row in ads_df.iterrows():
            if row['Status'].lower() == 'deactive':
                continue
            else:
                time = (kwargs['dag_run'].logical_date + datetime.timedelta(1)).strftime('%A')
                freq = row['Frequency'].split(',')
                for f in freq:
                    a = f.strip().lower()
                    if a in ['thứ 2', 'thứ 3', 'thứ 4', 'thứ 5', 'thứ 6', 'thứ 7', 'chủ nhật',
                                         'last_day_of_month']:
                        if time == weekday_map[a]:
                            msg_detail = create_msg(row)
                            # count = 0
                            for gapo_id in gapo_id_df['GAPO_ID'].unique():
                                # if count == 1:
                                #     break
                                # count += 1
                                user_name = gapo_id_df[gapo_id_df['GAPO_ID'] == gapo_id].EMPLOYEE_NM.values[0]
                                msg_dtl = msg_detail.replace('{name}', user_name)
                                msg = {
                                    'message': msg_dtl,
                                    'config': config,
                                    # 'receiver': '290205444',
                                    'receiver': f'{gapo_id}',
                                    'receiver_type': 'partner_id',
                                }
                                send_msg(**msg)
                                logging.info(f"Send message to {gapo_id} {user_name}")


with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id='Start')
    end = EmptyOperator(task_id='End')

    get_gapo_id = PythonOperator(task_id='Get_Gapo_ID', python_callable=get_gapo_id)
    get_ads_data = DownloadSharePointOperator(task_id='Download_ADS_Data_from_Sharepoint',
                                              sharepoint_conn_id='sharepoint_f88_data',
                                              department=department,
                                              report_name=report_name,
                                              local_saved_folder=saved_folder,
                                              freq=freq,
                                              _type=_type
                                              )
    create_and_send_msg = PythonOperator(task_id='Create_and_Send_MSG_to_CVKD', python_callable=create_and_send_msg)
    start >> [get_gapo_id, get_ads_data] >> create_and_send_msg >> end
