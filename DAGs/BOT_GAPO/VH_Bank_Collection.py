import os
import datetime
import logging
import pandas as pd
from airflow import DAG
from airflow.models import Variable
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.providers.mysql.hooks.mysql import MySqlHook
from Provider.SharePointProvider.operators.DownloadOperators import DownloadSharePointOperator
from DAGs.utils import send_msg
import pytz

parent_dir = os.path.dirname(os.path.abspath(__file__))
dag_name = 'BOT_VH_Nhac_GD_Treo'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = None #'0 8,13,17,19 * * *'
description = 'BOT GAPO Nhắc hoàn tất giao dịch'
tags = ['VH', 'bot-gapo']
los_conn_id = 'mysql_los'
thuho_conn_id = 'mysql_thuho'


def create_msg(partner, codeno, verifyreason, memo, username, TransDate):
    tz_VN = pytz.timezone('Asia/Ho_Chi_Minh')
    today = datetime.datetime.now(tz_VN).strftime('%d/%m/%Y %H:%M:%S')
    doi_tac = partner
    ma_hd = codeno
    verifyreason = verifyreason
    memo = memo
    cvkd = username
    tran_dt = TransDate
    
    message = f"Danh sách giao dịch thu qua ngân hàng cần xác minh\n "\
              f"Thời gian cập nhật : {today}\n "\
              f"Ngày CK : {tran_dt}\n "\
              f"Đối tác : {doi_tac}\n "\
              f"Mã HĐ : {ma_hd}\n "\
              f"Lý do cần xác mình : {verifyreason}\n "\
              f"Nội dung : {memo}\n "\

    return message

def get_data():
    query_thuho = """
        SELECT ti.ContractNumber CodeNo,
                t.Id,
                t.TransSource Partner,
                t.Amount,
                t.TransDate,
                t.TransCode,
                t.TransStatusCode status,
                NULL verifyReason,
                t.Memo
        FROM payment_gateway.Transactions t
        LEFT JOIN payment_gateway.TransactionInfo ti
                ON t.RefTransCode = ti.RefTransCode
        WHERE 1=1
                AND PaymentTo = 'KH'
                AND (TransStatusCode <> 'SUC' OR TransStatusCode IS NULL)
    """
    
    query_los = """
        SELECT p.CodeNo,
                p.ShopName,
                p.LoanPackageSource,
                p.CustomerName,
                p.CreatedUserName,
                p.CreatedFullName,
                p.CreatedEmployeeCode
        FROM Pawn p
    """

    thuho_hook = MySqlHook(mysql_conn_id=thuho_conn_id)
    thuho_get_pandas = thuho_hook.get_pandas_df(sql=query_thuho)

    los_hook = MySqlHook(mysql_conn_id=los_conn_id)
    los_get_pandas = los_hook.get_pandas_df(sql=query_los)

    data_merge = pd.merge(pd.DataFrame(thuho_get_pandas), pd.DataFrame(los_get_pandas), on = 'CodeNo', how = 'left')

    # los_get_pandas.columns = ['ShopName','LoanPackageSource','Partner','CustomerName','CodeNo','id','Amount','TransDate','TransCode','status','verifyReason','memo','CreatedUserName','CreatedFullName','CreatedEmployeeCode']
    data_merge.columns = ['CodeNo','id','Partner','Amount','TransDate','TransCode','status','verifyReason','memo','ShopName','LoanPackageSource','CustomerName','CreatedUserName','CreatedFullName','CreatedEmployeeCode']
    logging.info('Query data successfully!')
    logging.info(f'{data_merge.shape[0]} records loaded')
    
    gapo_id = """
                SELECT
                    A.GAPO_ID,
                    A.EMAIL,
                    B.EMPLOYEE_NM,
                    B.EMPLOYEE_USER_NM
                FROM
                    F88DWH.W_GAPO_ID A
                INNER JOIN (
                    SELECT
                        EMAIL,
                        EMPLOYEE_NM,
                        EMPLOYEE_USER_NM
                    FROM
                        F88DWH.W_EMPLOYEE_D wed
                    WHERE
                        EMAIL LIKE '%@f88.co'
                        AND JOB_DATE_OUT IS NULL
                        AND CRN_ROW_IND = 1
                        AND (LOWER(POSITION_NM) LIKE '%trưởng phòng giao dịch%'
                            OR LOWER(POSITION_NM) LIKE '%chuyên viên kinh doanh%')) B ON A.EMAIL = B.EMAIL"""

    hook = OracleHook(oracle_conn_id='oracle_f88_dwh')
    gapo_id = hook.get_pandas_df(gapo_id)
    gapo_id.columns = ['GAPO_ID','EMAIL','EMPLOYEE_NM','EMPLOYEE_USER_NM']
    logging.info('Query data from DWH successfully!')
    logging.info(f'DF has {gapo_id.shape[0]} records')
    
    df = pd.merge(data_merge, pd.DataFrame(gapo_id), left_on='CreatedUserName', right_on='EMPLOYEE_USER_NM')
    logging.info('Query data from DWH successfully!')
    logging.info(f'DataFrame send message has {df.shape[0]} records')
    
    return df


def get_gapo_id():
   
    sql = """SELECT
                A.GAPO_ID,
                A.EMAIL,
                B.EMPLOYEE_NM,
                B.EMPLOYEE_USER_NM
            FROM
                F88DWH.W_GAPO_ID A
            INNER JOIN (
                SELECT
                    EMAIL,
                    EMPLOYEE_NM,
                    EMPLOYEE_USER_NM
                FROM
                    F88DWH.W_EMPLOYEE_D wed
                WHERE
                    EMAIL LIKE '%@f88.co'
                    AND JOB_DATE_OUT IS NULL
                    AND CRN_ROW_IND = 1
                    AND (LOWER(POSITION_NM) LIKE '%trưởng phòng giao dịch%'
                        OR LOWER(POSITION_NM) LIKE '%chuyên viên kinh doanh%')) B ON A.EMAIL = B.EMAIL"""
    hook = OracleHook(oracle_conn_id='oracle_f88_dwh')
    gapo_id = hook.get_pandas_df(sql)
    gapo_id.columns = ['GAPO_ID','EMAIL','EMPLOYEE_NM','EMPLOYEE_USER_NM']
    logging.info('Query data from DWH successfully!')
    logging.info(f'DF has {gapo_id.shape[0]} records')
    return gapo_id


def create_and_send_msg():
    df = get_data()
    df = pd.DataFrame(df)
    #df = pd.DataFrame(los_get_pandas).merge(pd.DataFrame(gapo_ids), left_on="CreatedUserName", right_on="EMPLOYEE_USER_NM")
    #df = pd.merge(pd.DataFrame(los_get_pandas), pd.DataFrame(gapo_ids), how='left', left_on='CreatedUserName', right_on='EMPLOYEE_USER_NM')
    #logging.info(f'DF has {df.shape[0]} records')

    config = Variable.get("GAPO_BOT_VH", deserialize_json=True)
    for index, row in df.iterrows():
        partner = row["Partner"]
        codeno = row["CodeNo"]
        verifyreason = row["verifyReason"]
        memo = row["memo"]
        username = row["CreatedUserName"]
        TransDate = row["TransDate"]
        gapo_id = row["GAPO_ID"]
        shop_name = row["ShopName"]
        msg_detail = create_msg(partner, codeno, verifyreason, memo, username, TransDate)
        msg = {
            'message': msg_detail,
            'config': config,
            'receiver': f'{gapo_id}',
            'receiver_type': 'partner_id'
        }
        send_msg(**msg)
        logging.info(f"Send message to PGD :{shop_name} , CVKD : https://www.gapowork.vn/{gapo_id}")

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         catchup=False,
         description=description,
         default_args={
             'owner': 'F88-DE',
             'pool': 'Streaming_Pool'
         },
         tags=tags
         ) as dag:
    start = EmptyOperator(task_id='Start')
    end = EmptyOperator(task_id='End')
    create_and_send_msg = PythonOperator(task_id='Create_and_Send_to_VH', python_callable=create_and_send_msg)
    start >> create_and_send_msg >> end
