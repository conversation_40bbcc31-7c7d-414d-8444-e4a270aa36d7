from DAGs.utils import DAGMonitor
from airflow.providers.oracle.hooks.oracle import OracleHook
from airflow.utils.context import Context
from airflow.operators.python import Python<PERSON>perator
from airflow import DAG
from airflow.models import Variable
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime, date, timedelta
import logging

def send_mail(p_mail_to, p_subject, p_content):
    
    email_var = Variable.get('BI_EMAIL', deserialize_json=True)
    sender_address = email_var.get('email')
    sender_pass = email_var.get('password')

    #Setup the MIME
    message = MIMEMultipart()
    message['From'] = sender_address
    message['To'] = p_mail_to
    message['Subject'] = p_subject
    #message.attach(MIMEText(p_content, 'plain'))
    message.attach(MIMEText(p_content, 'html'))

    session = smtplib.SMTP('smtp.f88.co', 587)
    session.starttls()
    session.login(sender_address, sender_pass)
    
    #text = message.as_string() recipients.split(',')
    session.sendmail(sender_address, p_mail_to.split(','), message.as_string())
    session.quit()
    logging.info('Mail Sent')

################################ CONTENT ###################################
def get_data_report(**context: Context):
    today = DAGMonitor.set_params_operator(**context)['TODAY']
    # Connect DWH Oracle Database
    # dsn_tns = cx_Oracle.makedsn('************', '1521', service_name='orcldwh1')
    try:
        conn = OracleHook(oracle_conn_id = 'oracle_f88_dwh').get_conn()
        # conn = cx_Oracle.connect(user='f88dwh', password='Z5SITGJBEX5B', dsn=dsn_tns, encoding='UTF-8')
    except BaseException as error:
        logging.info('Connect to Oracle failure!!!' + error)
        raise error
    else:
        logging.info('Connect success')

    v_date = datetime.strptime(today, '%Y%m%d').strftime('%d-%b-%y')

    c = conn.cursor()

    v_mail_content = """<html><head>
    <style>
        html {font-family: arial;}
        table {border-collapse: collapse; font-size: 10pt;}
        th, td {border: 1px solid #999; text-align: left; padding: 3px 5px;}
    </style>
    </head><body>"""

    v_mail_content += "<h1>ODI REPORT DAILY " + datetime.strptime(today, '%Y%m%d').strftime('%d/%m/%Y') + chr(10) + "</h1>"

    ################# JOB ERROR #################

    logging.info('Get data job error')

    v_mail_content += "<h3>JOB ERROR</h3>"

    c.execute("select s.sess_no, s.scen_name, " +
            "to_char(sl.step_beg, 'hh24:mi:ss') start_time, " +
            "to_char(sl.step_end, 'hh24:mi:ss') end_time, " +
            "to_char(sl.step_dur) duration, f88dwh.get_odi_error_message(sl.error_message) error_message " +
            "from odiprod_odi_repo.snp_step_log sl, odiprod_odi_repo.snp_session s " +
            "where sl.sess_no = s.sess_no " +
            "and sl.step_status = 'E' "+
            "and sl.step_beg >= '" + v_date + "'")
    rows = c.fetchall() 

    v_mail_content += """<table style="width: 100%">
        <tr>
        <th>sess_no</th>
        <th>scen_name</th>
        <th>start_time</th>
        <th>end_time</th>
        <th>duration</th>
        <th>error_message</th>
        </tr>
    """

    for row in rows:
        v_mail_content += "<tr>"
        v_mail_content += "<td>" + str(row[0]) + "</td>"
        v_mail_content += "<td>" + row[1] + "</td>"
        v_mail_content += "<td>" + row[2] + "</td>"
        v_mail_content += "<td>" + row[3] + "</td>"
        v_mail_content += "<td style='text-align: right;'>" + str(row[4]) + "</td>"
        v_mail_content += "<td>" + row[5] + "</td>"    
        v_mail_content += "</tr>"
    del rows

    v_mail_content += "</table>"

    ################# CHECK DATA EXIST #################

    logging.info('Get data check exist')

    v_mail_content += "<h3>CHECK DATA EXIST</h3>"

    v_mail_content += """<table>
        <tr>
        <th style="width: 250px">check_type</th>
        <th style="width: 100px">object_code</th>
        <th style="width: 100px">count</th>
        </tr>
    """

    c.execute("select t.check_type, t.object_code, t.object_count from f88dwh.w_check_data_exist_v t order by 1, 2")
    rows = c.fetchall() 

    for row in rows:
        v_mail_content += "<tr>"
        v_mail_content += "<td>" + row[0] + "</td>"
        
        if str(row[1]) == 'None':
            v_mail_content += "<td>null</td>"
        else:
            v_mail_content += "<td>" + row[1] + "</td>"
            
        v_mail_content += "<td style='text-align: right;'>" + str(row[2]) + "</td>"
        v_mail_content += "</tr>"
    del rows

    v_mail_content += "</table>"

    ################# CHECK CROSS VALIDATION #################

    logging.info('Get data cross validation')

    v_mail_content += "<h3>CHECK CROSS VALIDATION</h3>"

    v_mail_content += """<table>
        <tr>
        <th style="width: 250px">check_type</th>
        <th style="width: 500px">description</th>
        <th style="width: 100px">count</th>
        </tr>
    """

    c.execute("select t.check_type, t.description, to_char(count(1), 'FM999G999G999') invalid_count " +
            "from f88dwh.w_check_cross_validation_v t group by t.check_type, t.description order by 1")
    rows = c.fetchall() 

    for row in rows:
        v_mail_content += "<tr>"
        v_mail_content += "<td>" + row[0] + "</td>"
        v_mail_content += "<td>" + row[1] + "</td>"    
        v_mail_content += "<td style='text-align: right;'>" + row[2] + "</td>"
        v_mail_content += "</tr>"
    del rows

    v_mail_content += "</table>"


    # Finally Close
    c.close()
    conn.close()

    v_mail_content += "</body></html>"

    v_mail_subject = "ODI Daily Report " + datetime.strptime(today, '%Y%m%d').strftime('%d/%m/%Y')
    v_mail_to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
    

    send_mail(v_mail_to, v_mail_subject, v_mail_content)
    
with DAG(dag_id='DAILY_REPORT_ODI_JOBS', description='Báo cáo tình trạng job ODI hàng ngày vào 5,6,9h sáng',
        dagrun_timeout=timedelta(minutes=10),
        catchup=False,
        max_active_runs=1,
        default_args={
            'owner': 'F88-DE',
            'retries': 3,
            'retry_delay': timedelta(minutes=1),
             'trigger_rule': 'all_success'
         },
         start_date=datetime(2023, 12, 21),
         schedule_interval='0 5,6,9 * * *',
         tags=['report','ODI','daily']
         ) as dag:
    get_data_report_dag = PythonOperator(task_id='get_data_report_and_send_mail', python_callable=get_data_report, provide_context=True)

if __name__ == '__main__':
    get_data_report()