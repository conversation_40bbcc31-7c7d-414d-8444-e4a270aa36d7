from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from datetime import timedelta, datetime

# G<PERSON>i các hàm bạn đã định nghĩa
from DAGs.Datamart_SendMail.SendMail_BDM import send_email_datamart_to_bdm
from DAGs.Datamart_SendMail.SendMail_QTRR import send_email_datamart_to_qtrr

default_args = {
    'owner': 'qtdl',
    'depends_on_past': False,
    'email': ['<EMAIL>'],
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=2),
}

# Định nghĩa DAG
with DAG(
    'DAG_DATAMART_EMAIL_DAILY_dangld',
    default_args=default_args,
    description='DAG gửi email thống kê so sánh giữa DWH và Data Mart hàng ngày',
    schedule_interval='00 8 * * *',  # chạy lúc 7h sáng hàng ngày
    start_date=datetime(2024, 6, 1),
    catchup=False,
    tags=['datamart', 'email', 'monitor'],
) as dag:

    def run_send_email_bdm():
        # Bạn có thể cấu hình lại 3 tham số này tùy vào mục đích
        print("start send email bdm")
        send_email_datamart_to_bdm(
            requestor=['dangld'],       # username email
            status=1,               # status = 1 (success), 0 (fail)
            reason='Daily check',   # lý do (nếu cần log)
            dagId='DAG_DATAMART_EMAIL_DAILY_dangld'
        )

    def run_send_email_qtrr():
        # Bạn có thể cấu hình lại 3 tham số này tùy vào mục đích
        print("start send email qtrr")
        send_email_datamart_to_qtrr(
            requestor=['dangld'],       # username email
            status=1,               # status = 1 (success), 0 (fail)
            reason='Daily check',   # lý do (nếu cần log)
            dagId='DAG_DATAMART_EMAIL_DAILY_dangld'
        )

    send_email_bdm_task = PythonOperator(
        task_id='send_email_to_bdm',
        python_callable=run_send_email_bdm
    )
    send_email_task_qtrr = PythonOperator(
        task_id='send_email_to_qtrr',
        python_callable=run_send_email_qtrr
    )

    send_email_bdm_task >> send_email_task_qtrr
