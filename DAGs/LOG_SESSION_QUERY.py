import datetime
from airflow.providers.oracle.operators.oracle import OracleStoredProcedureOperator
from airflow import DAG

dag_name = 'LOG_SESSION_QUERY'
start_date = datetime.datetime(2022, 9, 1)
schedule_interval = '30 * * * *'
description = 'Log các session có query nặng trên DWH mỗi 30 phút'
tags = ['LOG', 'EXPENSIVE QUERY']
oracle_conn_id = 'oracle_f88_dwh'

with DAG(dag_id=dag_name,
         schedule_interval=schedule_interval,
         start_date=start_date,
         description=description,
         default_args={
             'owner': 'F88-DE',
         },
         tags=tags,
         catchup=False
         ) as dag:
    run_proc = OracleStoredProcedureOperator(task_id='RUN_PRD_W_LOG_SESSION_QUERY_F',oracle_conn_id=oracle_conn_id, procedure='F88DWH.PRD_W_LOG_SESSION_QUERY_F')