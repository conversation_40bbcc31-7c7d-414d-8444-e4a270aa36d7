import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from airflow.utils.context import Context
from airflow.hooks.base import BaseHook

DAG_MONITOR = 'F88DWH.W_DAG_MONITOR'
def send_email(context):
    dag_id = context['dag'].dag_id
    execution_date = context['execution_date']
    # change these as per use
    your_email = "<EMAIL>"
    your_password = "F88@6388"

    # establishing connection with gmail
    server = smtplib.SMTP_SSL('smtp.f88.co', 465)
    server.ehlo()
    server.login(your_email, your_password)

    html_content = '''
    <html>
    <head></head>
    <body>
        <p>Dear anh Nam,</p>
        <p></p>
        <p>Hiện tại dag có id là: {dag_id} đang bị lỗi vào ngày: {date}</p>
        <P></P>
        <P>Trân trọng!</P>
    </body>
    </html>
    '''
        

    msg = MIMEMultipart()
    msg['From'] = your_email
    msg['To'] = "<EMAIL>,<EMAIL>"
    msg['Subject'] = 'THÔNG BÁO VỀ DAG CHẠY LỖI'
    content=html_content.format(dag_id = dag_id, date = execution_date)

    html_part = MIMEText(content, 'html')
    msg.attach(html_part)

    server.send_message(msg)
    server.close()
    
def dagmonitor_on_fail_callback(context: Context):
    """Hàm này chỉ sử dụng được cho các dag có trong {DAG_MONITOR}

    Args:
        context (Context): Airflow context
    """
    dag_id = context['dag'].dag_id
    # dag_id = 'QTRR_MONTHLY_SHAREPOINT_RRHD_PGD_TO_DWH'
    hook = BaseHook.get_hook(conn_id='oracle_f88_dwh')
    user_callback, report_folder_callback = hook.get_first(f"SELECT REQUESTOR, PATTERN_TYPE || '/' || DEPARTMENT || '/' || REPORT_NAME  FROM {DAG_MONITOR} dm WHERE dag_id = '{dag_id}' AND STATUS = 1")
    user_callback = ','.join([i if i.endswith('@f88.vn') else i + '@f88.vn' for i in user_callback.replace(' ', '').split(',')])
    print(user_callback, report_folder_callback)
    execution_date = context['execution_date']
    # change these as per use
    your_email = "<EMAIL>"
    your_password = "F88@6388"
    print('Send mail')
    # establishing connection with gmail
    session = smtplib.SMTP('smtp.f88.co', 587)
    session.starttls()
    session.login(your_email, your_password)

    html_content = '''
    <html>
    <head></head>
    <body>
        <p>Dear anh/chị {user_callback}</p>
        <p></p>
        <p>Hiện tại dag có id là: {dag_id} phục vụ dữ liệu báo cáo trên folder sharepoint có đường dẫn: {report_folder_callback} đang bị lỗi vào ngày: {date}.</p>
        <P></P>
        <P>Trân trọng!</P>
    </body>
    </html>
    '''
        
    msg = MIMEMultipart()
    msg['From'] = your_email
    msg['To'] = user_callback
    msg['Subject'] = f'THÔNG BÁO VỀ DAG CHẠY LỖI NGÀY {execution_date.strftime("%Y-%m-%d")}'
    content=html_content.format(user_callback=user_callback,dag_id = dag_id, report_folder_callback=report_folder_callback, date = execution_date, exception=context.get('exception'))

    html_part = MIMEText(content, 'html')
    msg.attach(html_part)

    session.sendmail(your_email, user_callback.split(','), msg.as_string())
    session.quit()
    print('Mail sent')
    
if __name__ == '__main__':
    dagmonitor_on_fail_callback({'execution_date':'aaa','report_folder_callback':'report_folder_callback', 'exception':'report_folder_callback', 'user_callback':'<EMAIL>', 'dag':'dag'})
    # hook = BaseHook.get_hook(conn_id='oracle_f88_dwh')
    # print(hook.get_first("SELECT REQUESTOR, PATTERN_TYPE || '/' || DEPARTMENT || '/' || REPORT_NAME  FROM {DAG_MONITOR} dm WHERE dag_id = 'QTRR_MONTHLY_SHAREPOINT_RRHD_PGD_TO_DWH' AND STATUS = 1"))